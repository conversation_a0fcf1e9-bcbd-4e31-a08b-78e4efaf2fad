/* eslint-disable no-use-before-define */
export type IMenuMode = 'normal' | 'collapse';

export type IMenuItemGroup = {
  key: string;
  group?: string;
  children: IMenuItem[];
  [x: string]: any;
};

export type IMenuItem = {
  icon?: string;
  key: string;
  label: string;
  children?: IMenuItemGroup[];
  meta?: Record<string, any>;
  [x: string]: any;
};

export type IMenuKey = string;

export type IMenuKeys = Array<IMenuKey>;
