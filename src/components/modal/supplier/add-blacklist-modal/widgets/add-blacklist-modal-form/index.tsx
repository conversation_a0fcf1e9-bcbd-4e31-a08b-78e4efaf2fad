import { computed, defineComponent, onMounted, PropType, ref } from 'vue';
import { Col, Form, Input, Row } from 'ant-design-vue';
import type { WrappedFormUtils } from 'ant-design-vue/types/form/form';
import moment from 'moment';

import { FormController, FormItemType } from '@/components/enhancement/form-builder';
import { company as companyService, blackList as blackListService } from '@/shared/services';
import { BLACKLIST_DURATION_MAP, BLACKLIST_DURATION_TIME_CONFIG } from '@/shared/constants/black-list.constants';
import QIcon from '@/components/global/q-icon';
import { getHTMLText } from '@/utils';
import { getFilterOptions } from '@/hooks/use-group-label-oprator';

import LabelSelect from '../../../add-partner-modal/widgets/label-select';
import CompanySelect from '../../../company-select';
import styles from './form.module.less';
import { useValidateCount } from '@/shared/composables/use-validate-count';

const AddBlacklistForm = defineComponent({
  name: 'AddBlacklistForm',
  props: {
    form: {
      type: Object as PropType<WrappedFormUtils>,
    },
    // 增加 flex 布局支持
    formLayoutType: {
      type: String as PropType<'ant-row' | 'ant-row-flex'>,
      default: 'ant-row-flex',
    },
    itemLayout: {
      type: Object,
      default: () => ({
        labelCol: { flex: '80px' },
        wrapperCol: { flex: 'auto' },
      }),
    },
    formData: {
      type: Object,
      required: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  setup(props) {
    const fullListExpanded = ref(false);
    const departList = ref<{ departmentId: number; name: string }[]>([]);

    const { allTags, groups, getAllTags, getGroups } = getFilterOptions({ type: 3, table: 'InnerBlacklist' });

    const getDeptList = async () => {
      if (departList.value?.length) {
        return;
      }
      try {
        departList.value = await blackListService.getBlackListDepts();
      } catch (error) {
        console.log(error);
      }
    };

    const toggleFulleListExpanded = async () => {
      fullListExpanded.value = !fullListExpanded.value;
      if (!departList.value.length) {
        getDeptList();
      }
      if (!allTags.value.length) {
        getAllTags(3);
      }
    };

    const { groupValidator, departmentValidator } = useValidateCount('blacklist');

    const formConfig = computed<FormItemType[]>(() => [
      {
        name: 'id',
        type: 'input',
        hidden: true,
      },
      {
        name: 'companyName',
        type: 'input',
        hidden: true,
      },
      {
        name: 'companyId',
        type: 'input',
        hidden: true,
      },
      {
        name: 'econkindcode',
        type: 'input',
        hidden: true,
      },
      {
        name: 'province',
        type: 'input',
        hidden: true,
      },
      {
        name: 'areacode',
        type: 'input',
        hidden: true,
      },
      {
        name: 'industry',
        type: 'input',
        hidden: true,
      },
      {
        name: 'econkind',
        type: 'input',
        hidden: true,
      },
      {
        name: 'subind',
        type: 'input',
        hidden: true,
      },
      {
        name: 'groupId',
        type: 'select',
        label: '选择分组',
        placeholder: '请选择分组',
        size: 'large',
        attrs: {
          allowClear: false,
          defaultValue: props.formData.groupId,
        },
        options: groups.value.map((item: any) => {
          return {
            value: item.groupId,
            label: item.name,
          };
        }),
        rules: [
          {
            validator: groupValidator({ id: props.formData.id, count: props.formData.transData?.length }),
          },
        ],
      },
      {
        name: 'reason',
        type: 'text',
        label: '列入原因',
        placeholder: '如合同违约、断供、欠款未付等 ',
        autosize: {
          maxRows: 4,
          minRows: 3,
        },
        size: 'large',
        rules: [
          {
            max: 200,
            message: '列入原因不能超过200字',
          },
        ],
      },
      {
        name: 'joinDate',
        type: 'date',
        label: '列入日期',
        placeholder: '请选择列入日期',
        size: 'large',
        onMethods: {
          change: (value) => {
            const date = props.form.getFieldValue('date') || props.formData.date;
            let expiredDate;
            const duration = BLACKLIST_DURATION_TIME_CONFIG[date];
            if (duration && date) {
              const { unit, count } = duration;
              expiredDate = moment(value).add(count, unit).format();
              props.form.setFieldsValue({
                expiredDate,
              });
            }
          },
        },
      },
      {
        name: 'date',
        type: 'select',
        label: '有效期',
        size: 'large',
        attrs: {
          defaultValue: -1,
        },
        onMethods: {
          change: (value) => {
            const addTime = props.form.getFieldValue('joinDate') || props.formData.joinDate;
            let expiredDate;
            const duration = BLACKLIST_DURATION_TIME_CONFIG[value];
            if (duration && addTime) {
              const { unit, count } = duration;
              expiredDate = moment(addTime).add(count, unit).format();
            }
            props.form.setFieldsValue({
              expiredDate,
            });
          },
        },
        placeholder: '请选择黑名单有效期',
        options: [
          {
            value: -2,
            label: BLACKLIST_DURATION_MAP[-2],
          },
          {
            value: 0,
            label: BLACKLIST_DURATION_MAP[0],
          },
          {
            value: 1,
            label: BLACKLIST_DURATION_MAP[1],
          },
          {
            value: 2,
            label: BLACKLIST_DURATION_MAP[2],
          },
          {
            value: 3,
            label: BLACKLIST_DURATION_MAP[3],
          },
          {
            value: 4,
            label: BLACKLIST_DURATION_MAP[4],
          },
          {
            value: 5,
            label: BLACKLIST_DURATION_MAP[5],
          },
          {
            value: -1,
            label: BLACKLIST_DURATION_MAP[-1],
          },
        ],
      },
      {
        name: 'expiredDate',
        type: 'date',
        label: '截止日期',
        placeholder: '请选择截止日期',
        size: 'large',
        disabledDate: (current) => {
          const joinDate = props.form.getFieldValue('joinDate') || props.formData.joinDate;
          return joinDate && current < joinDate;
        },
        onMethods: {
          change: () => {
            props.form.setFieldsValue({
              date: -2,
            });
          },
        },
      },
      {
        name: 'departmentNames',
        type: 'select',
        label: '来源部门',
        placeholder: '请填写来源部门',
        size: 'large',
        options: departList.value.map((item: any) => {
          return {
            value: item.name,
            label: item.name,
            id: item.departmentId,
          };
        }),
        attrs: {
          tokenSeparators: [','],
          mode: 'tags',
          allowClear: true,
        },
        rules: [
          {
            validator: departmentValidator({ id: props.formData.id, count: props.formData.transData?.length }),
          },
        ],
      },
      {
        name: 'labelIds',
        type: 'select',
        label: '选择标签',
        placeholder: '请选择标签',
        size: 'large',
        options: [],
        custom: true,
      },
      {
        name: 'note',
        type: 'text',
        label: '备注',
        placeholder: '如合同违约、断供、欠款未付等 ',
        autosize: {
          maxRows: 4,
          minRows: 3,
        },
        size: 'large',
        rules: [
          {
            max: 200,
            message: '备注不能超过200字',
          },
        ],
      },
    ]);

    const suggestionRequest = (keywords: string) => {
      return companyService.searchLite({ searchKey: keywords }).then((Result = []) => {
        return Result.map(({ Name, ...other }) => ({
          // option shape
          value: getHTMLText(Name),
          label: Name,
          ...other,
        }));
      });
    };

    onMounted(() => {
      if (!groups.value.length) {
        getGroups(3);
      }
    });

    return {
      allTags,
      formConfig,
      fullListExpanded,
      toggleFulleListExpanded,
      suggestionRequest,
    };
  },
  render() {
    const renderCompany = () => {
      if (this.formData.transData) {
        const name = this.formData.transData.length > 1 ? `企业数量${this.formData.transData.length}` : this.formData.transData[0].name;
        return <Input size="large" disabled={true} placeholder={name} />;
      }
      return (
        <CompanySelect
          placeholder="请输入企业名称"
          size="large"
          remote={this.suggestionRequest}
          onInput={(value) => {
            this.form?.setFields({
              name: { value },
            });
          }}
          disabled={this.disabled}
          onChange={(value, option) => {
            // 设置 form 字段
            this.form?.setFields({
              name: { value: getHTMLText(option.label) },
              companyName: { value: getHTMLText(option.label) },
              companyId: { value: option.KeyNo },
            });
          }}
        />
      );
    };
    const renderFormController = (formItem) => {
      if (formItem.custom) {
        return (
          <Form.Item class={this.formLayoutType} label="选择标签" id="labelItem">
            {this.form?.getFieldDecorator('labelIds', {
              initialValue: (this.formData.labels || []).map((item) => item.labelId),
            })(
              <LabelSelect
                options={this.allTags || []}
                onChange={(ids) => {
                  this.form?.setFieldsValue({
                    labelIds: ids,
                  });
                }}
              ></LabelSelect>
            )}
          </Form.Item>
        );
      }
      return <FormController formLayoutType={this.formLayoutType} key={formItem.name} form={this.form} formItem={formItem} />;
    };
    return (
      <Form
        class={styles.container}
        form={this.form}
        labelAlign="right"
        colon={false}
        layout="horizontal"
        labelCol={this.itemLayout.labelCol}
        wrapperCol={this.itemLayout.wrapperCol}
      >
        <Form.Item class={this.formLayoutType} label="企业名称" required>
          {this.form?.getFieldDecorator('name', {
            rules: [
              {
                required: !this.formData.transData,
                message: '请输入企业名称',
              },
            ],
          })(renderCompany())}
        </Form.Item>

        {this.formConfig.slice(0, this.formConfig.findIndex((item) => item.label === '截止日期') + 1).map(renderFormController)}

        <Row type="flex" style={{ marginBottom: this.fullListExpanded ? '15px' : '0px' }}>
          <Col {...{ props: this.itemLayout.labelCol }} />
          <Col {...{ props: this.itemLayout.wrapperCol }}>
            <span onClick={this.toggleFulleListExpanded} class={styles.expand}>
              信息补充
              <QIcon type={this.fullListExpanded ? 'icon-a-xianduanshang' : 'icon-a-xianduanxia'} />
            </span>
          </Col>
        </Row>
        <div v-show={this.fullListExpanded}>
          {this.formConfig.slice(this.formConfig.findIndex((item) => item.label === '截止日期') + 1).map(renderFormController)}
        </div>
      </Form>
    );
  },
});

const AddBlacklistFormWrapper = Form.create({})(AddBlacklistForm);

export default AddBlacklistFormWrapper;
