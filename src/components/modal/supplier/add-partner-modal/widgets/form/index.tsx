import { computed, defineComponent, onMounted, PropType, ref } from 'vue';
import { Col, Form, Row } from 'ant-design-vue';
import moment from 'moment';
import type { WrappedFormUtils } from 'ant-design-vue/types/form/form';

import { FormController, FormItemType } from '@/components/enhancement/form-builder';
import CompanySelect from '@/components/modal/supplier/company-select';
import { company as companyService } from '@/shared/services';
import QIcon from '@/components/global/q-icon';
import { getHTMLText } from '@/utils';
import { characterPattern, isValidEmail, landlineRegex } from '@/utils/validator';

import styles from './form.module.less';
import LabelSelect from '../label-select';
import ContacterDetail from '../contacter-detail';
import { useValidateCount } from '@/shared/composables/use-validate-count';

function limitDecimalPoint(e) {
  if (e.target.value > 999999999999.9999) {
    e.target.value = 999999999999.9999;
  }
  e.target.value = e.target.value.replace(/^(-)*(\d+)\.(\d{1,6}).*$/, '$1$2.$3');
}

export const AddPartnerForm = defineComponent({
  name: 'AddPartnerForm',
  props: {
    form: {
      type: Object as PropType<WrappedFormUtils>,
    },
    // 增加 flex 布局支持
    formLayoutType: {
      type: String as PropType<'ant-row' | 'ant-row-flex'>,
      default: 'ant-row-flex',
    },
    itemLayout: {
      type: Object,
      default: () => ({
        labelCol: { flex: '80px' },
        wrapperCol: { flex: '1' },
      }),
    },
    groups: {
      type: Array,
      required: true,
    },
    departList: {
      type: Array as PropType<{ name: string; departmentId?: number }[]>,
      default: () => [],
    },
    allTags: {
      type: Array,
      required: true,
    },
    disable: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    const fullListExpanded = ref(false);
    const toggleFulleListExpanded = () => {
      fullListExpanded.value = !fullListExpanded.value;
    };
    const personList = ref<Array<string>>([]);

    const contactsData = ref<
      {
        name?: string;
        phone?: string;
        email?: string;
        changed?: boolean;
      }[]
    >([]);
    const addContacts = () => {
      contactsData.value.push({
        name: undefined,
        phone: undefined,
        email: undefined,
        changed: false,
      });
    };
    const suggestionRequest = (keywords: string) => {
      return companyService.getCompanyListQCC({ searchKey: keywords }).then(({ Result = [] }) => {
        return Result.map(({ Name, ...other }) => ({
          // option shape
          value: getHTMLText(Name),
          label: Name,
          ...other,
        }));
      });
    };

    const initPersonList = async () => {
      personList.value = await companyService.getCompanyPersonList();
    };

    const { groupValidator, departmentValidator } = useValidateCount('customer');

    onMounted(() => {
      initPersonList();
    });

    const formConfig = computed<FormItemType[][]>(() => [
      [
        {
          name: 'id',
          type: 'input',
          hidden: true,
        },
        {
          name: 'companyName',
          type: 'input',
          hidden: true,
        },
        {
          name: 'companyId',
          type: 'input',
          hidden: true,
        },

        {
          name: 'groupId',
          type: 'select',
          label: '选择分组',
          placeholder: '请选择分组',
          size: 'large',
          attrs: {
            allowClear: false,
          },
          options: props.groups.map((item: any) => {
            return {
              value: item.groupId || item.value,
              label: item.name || item.label,
            };
          }),
          rules: [
            {
              validator: groupValidator({ id: props.data?.customerId }),
            },
          ],
        },
        {
          name: 'labelIds',
          type: 'tree-select',
          label: '选择标签',
          placeholder: '请选择标签',
          size: 'large',
          customed: true,
        },
        {
          name: 'departmentNames',
          type: 'select',
          label: '所属部门',
          placeholder: '请填写管理部门',
          showsSuffix: true,
          size: 'large',
          options: props.departList.map((item: any) => {
            return {
              value: item.name,
              label: item.name,
              id: item.departmentId,
            };
          }),
          attrs: {
            tokenSeparators: [','],
            mode: 'tags',
            allowClear: true,
          },
          rules: [
            {
              validator: departmentValidator({ id: props.data?.customerId }),
            },
          ],
        },
        {
          name: 'owner',
          type: 'auto-complete',
          label: '负责人',
          placeholder: '请填写部门负责人',
          dataSource: personList.value,
          size: 'large',
          showsSuffix: true,
          rules: [
            {
              max: 20,
              message: '负责人不能超过20字',
            },
          ],
        },
      ],
      [
        {
          name: 'contactName',
          type: 'input',
          label: '联系人',
          placeholder: '请填写第三方联系人',
          attrs: {
            allowClear: true,
          },
          rules: [
            {
              validator: (rule, value, callback) => {
                if (value && !characterPattern.test(value)) {
                  callback('请输入正确的联系人');
                } else {
                  callback();
                }
              },
            },
          ],
        },
        {
          name: 'contactPhone',
          type: 'input',
          label: '联系电话',
          placeholder: '请填写第三方联系电话',
          attrs: {
            allowClear: true,
          },
          rules: [
            {
              validator: (rule, value, callback) => {
                if (value && !landlineRegex.test(value)) {
                  callback('请输入正确的联系电话');
                } else {
                  callback();
                }
              },
            },
          ],
        },
        {
          name: 'contactEmail',
          type: 'input',
          label: '邮箱',
          placeholder: '请填写第三方邮箱',
          attrs: {
            allowClear: true,
          },
          rules: [
            {
              validator: (rule, value, callback) => {
                if (value && !isValidEmail(value)) {
                  callback('请输入正确的邮箱');
                } else {
                  callback();
                }
              },
            },
          ],
        },
        {
          name: 'startDate',
          type: 'date',
          label: '开始时间',
          size: 'large',
          attrs: {
            valueFormat: 'YYYY-MM-DD',
          },
        },
        {
          name: 'endDate',
          type: 'date',
          label: '截止时间',
          size: 'large',
          rules: [
            {
              validator: (rule, value, callback) => {
                if (value && moment(value).isBefore(moment(props?.form?.getFieldValue('startDate')))) {
                  callback('截止时间不能小于开始时间');
                } else {
                  callback();
                }
              },
            },
          ],
          attrs: {
            valueFormat: 'YYYY-MM-DD',
          },
        },
        {
          name: 'creditQuota',
          type: 'input',
          label: '授信金额',
          placeholder: '请填写授信金额',
          size: 'large',
          attrs: {
            type: 'number',
            suffix: '万',
            onChange: limitDecimalPoint,
          },
        },
        {
          name: 'contactQuota',
          type: 'input',
          label: '合同金额',
          placeholder: '请填写合同金额',
          size: 'large',
          attrs: {
            type: 'number',
            suffix: '万',
            onChange: limitDecimalPoint,
          },
        },
        {
          name: 'cost',
          type: 'input',
          label: '资金占用',
          placeholder: '请填写资金占用',
          size: 'large',
          attrs: {
            type: 'number',
            suffix: '万',
            onChange: limitDecimalPoint,
          },
        },
      ],
    ]);

    return {
      formConfig,
      fullListExpanded,
      toggleFulleListExpanded,
      contactsData,
      addContacts,
      suggestionRequest,
    };
  },
  render() {
    return (
      <div class={styles.container}>
        <Form
          form={this.form}
          labelAlign="right"
          colon={false}
          layout="horizontal"
          labelCol={this.itemLayout.labelCol}
          wrapperCol={this.itemLayout.wrapperCol}
        >
          <Form.Item class={this.formLayoutType} label="企业名称" required>
            {this.form?.getFieldDecorator('name', {
              rules: [
                {
                  required: true,
                  message: '请输入企业名称',
                },
              ],
            })(
              <CompanySelect
                size="large"
                placeholder="请输入企业名称"
                remote={this.suggestionRequest}
                disabled={this.disable}
                onChange={(value, option) => {
                  // 设置 form 字段
                  this.form?.setFields({
                    name: { value: getHTMLText(option.label) },
                    companyName: { value: getHTMLText(option.label) },
                    companyId: { value: option.KeyNo },
                  });
                }}
              />
            )}
          </Form.Item>

          {this.formConfig[0].map((formItem) => {
            if (formItem.customed) {
              return (
                <Form.Item class={this.formLayoutType} label="选择标签" id="labelItem">
                  {this.form?.getFieldDecorator('labelIds', {
                    initialValue: (this.data.labels || []).map((item) => item.labelId),
                  })(
                    <LabelSelect
                      options={this.allTags || []}
                      onChange={(ids) => {
                        this.form?.setFieldsValue({
                          labelIds: ids,
                        });
                      }}
                    ></LabelSelect>
                  )}
                </Form.Item>
              );
            }
            return (
              <FormController
                formLayoutType={this.formLayoutType}
                class={styles.from}
                key={formItem.name}
                form={this.form}
                formItem={formItem}
              />
            );
          })}

          <Row type="flex" style={{ marginBottom: this.fullListExpanded ? '15px' : '0px' }}>
            <Col {...{ props: this.itemLayout.labelCol }} />
            <Col {...{ props: this.itemLayout.wrapperCol }}>
              <span onClick={this.toggleFulleListExpanded} class={styles.expand} data-testid="expand-btn">
                信息补充
                <QIcon type={this.fullListExpanded ? 'icon-a-xianduanshang' : 'icon-a-xianduanxia'} />
              </span>
            </Col>
          </Row>

          <div v-show={this.fullListExpanded}>
            {this.formConfig[1].map((formItem) => {
              return (
                <FormController
                  formLayoutType={this.formLayoutType}
                  class={styles.from}
                  key={formItem.name}
                  form={this.form}
                  formItem={formItem}
                />
              );
            })}

            {/* 联系人信息,现在只有一个，暂不使用这种处理方式 */}
            {/* <Form.Item class={this.formLayoutType} label="联系人信息" style="width: 100%;" v-show={false}>
              <div style="background: #fafafa;padding: 10px;">
                {this.contactsData.length > 0
                  ? this.contactsData.map((contacts, index) => {
                      return (
                        <ContacterDetail
                          v-model={contacts}
                          key={index}
                          dataLength={this.contactsData.length}
                          onDelete={() => {
                            this.contactsData.splice(index, 1);
                          }}
                        />
                      );
                    })
                  : null}
                <div>
                  <span
                    data-testid="add-relation-button"
                    class={styles.relativeAdd}
                    onClick={() => {
                      this.addContacts();
                    }}
                  >
                    <QIcon type="icon-a-tianjiaxian"></QIcon>新增近联系人信息
                  </span>
                </div>
              </div>
            </Form.Item> */}
          </div>
        </Form>
      </div>
    );
  },
});

const AddPartnerFormWrapper = Form.create({})(AddPartnerForm);

export default AddPartnerFormWrapper;
