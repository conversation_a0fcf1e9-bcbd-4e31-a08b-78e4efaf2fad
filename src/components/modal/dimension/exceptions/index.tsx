import { defineComponent, PropType } from 'vue';

import styles from './index.module.less';

interface ViewData {
  Date?: string;
  Agency?: string;
  Reason?: string;
}

const Exceptions = defineComponent({
  name: 'Exceptions',

  props: {
    viewData: {
      type: Object as PropType<ViewData>,
      default: () => ({}),
    },
  },

  render() {
    const { viewData } = this;

    if (!viewData) {
      return <div></div>;
    }

    return (
      <div>
        <table class={styles.ntable}>
          <tr>
            <td width="23%" class={styles.tb}>列入日期：</td>
            <td width="27%">
              {viewData.Date || '-'}
            </td>
            <td width="23%" class={styles.tb}>作出决定机关：</td>
            <td width="27%">
              {viewData.Agency || '-'}
            </td>
          </tr>
          <tr>
            <td class={styles.tb} width="23%">列入原因：</td>
            <td colspan="3">
              {viewData.Reason || '-'}
            </td>
          </tr>
        </table>
      </div>
    );
  },
});

export default Exceptions;
