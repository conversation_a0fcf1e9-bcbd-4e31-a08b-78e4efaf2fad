import { shallowMount } from '@vue/test-utils';

import Exceptions from '../index.tsx';

describe('Exceptions', () => {
  let wrapper: any;
  let mockOptions: any;

  beforeEach(() => {
    mockOptions = {
      propsData: {
        viewData: {
          Date: '2023-01-01',
          Agency: '北京市工商行政管理局',
          Reason: '未按规定公示年度报告'
        },
      },
    };
  });

  const createWrapper = (propsData: any = {}, options: any = {}) => {
    const mergedOptions = {
      ...mockOptions,
      propsData: {
        viewData: {
          ...mockOptions.propsData.viewData,
          ...propsData.viewData,
        },
      },
      ...options,
    };

    wrapper = shallowMount(Exceptions, mergedOptions);
    return wrapper;
  };

  afterEach(() => {
    if (wrapper) {
      wrapper.destroy();
    }
  });

  describe('基本信息渲染', () => {
    test('应该正确渲染列入日期', () => {
      const wrapper = createWrapper();

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('列入日期');
      expect(wrapper.text()).toContain('2023-01-01');
    });

    test('应该正确显示作出决定机关', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('作出决定机关');
      expect(wrapper.text()).toContain('北京市工商行政管理局');
    });

    test('应该正确显示列入原因', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('列入原因');
      expect(wrapper.text()).toContain('未按规定公示年度报告');
    });
  });

  describe('边界情况', () => {
    test('应该处理空的viewData', () => {
      const wrapper = createWrapper({ viewData: {} });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理null的viewData', () => {
      const wrapper = createWrapper({ viewData: null });

      expect(wrapper.exists()).toBe(true);
      // 当viewData为null时，由于有默认值{}，表格仍会渲染
      expect(wrapper.find('table').exists()).toBe(true);
    });

    test('应该处理undefined的字段', () => {
      const wrapper = createWrapper({
        viewData: {
          Date: undefined,
          Agency: undefined,
          Reason: undefined
        }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理null值字段', () => {
      const wrapper = createWrapper({
        viewData: {
          Date: null,
          Agency: null,
          Reason: null
        }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理空字符串', () => {
      const wrapper = createWrapper({
        viewData: {
          Date: '',
          Agency: '',
          Reason: ''
        }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });
  });

  describe('表格结构', () => {
    test('应该渲染正确的表格结构', () => {
      const wrapper = createWrapper();

      const table = wrapper.find('table');
      expect(table.exists()).toBe(true);
      expect(table.classes()).toContain('ntable');

      const rows = wrapper.findAll('tr');
      expect(rows.length).toBe(2);
    });

    test('应该有正确的列宽设置', () => {
      const wrapper = createWrapper();

      const cells23 = wrapper.findAll('td[width="23%"]');
      expect(cells23.length).toBe(3);

      const cells27 = wrapper.findAll('td[width="27%"]');
      expect(cells27.length).toBe(2);
    });

    test('应该有正确的样式类', () => {
      const wrapper = createWrapper();

      const tbCells = wrapper.findAll('td.tb');
      expect(tbCells.length).toBe(3);
    });

    test('应该有正确的colspan设置', () => {
      const wrapper = createWrapper();

      const colspanCells = wrapper.findAll('td[colspan="3"]');
      expect(colspanCells.length).toBe(1);
    });
  });

  describe('组件属性', () => {
    test('应该正确接收viewData属性', () => {
      const wrapper = createWrapper();

      expect(wrapper.vm.viewData).toEqual(mockOptions.propsData.viewData);
    });

    test('应该有默认的viewData', () => {
      const wrapper = shallowMount(Exceptions);

      expect(wrapper.vm.viewData).toEqual({});
    });
  });

  describe('条件渲染', () => {
    test('当viewData存在时应该显示表格', () => {
      const wrapper = createWrapper();

      expect(wrapper.find('table').exists()).toBe(true);
    });

    test('当viewData为null时应该显示表格', () => {
      const wrapper = createWrapper({ viewData: null });

      expect(wrapper.find('table').exists()).toBe(true);
    });

    test('当viewData为undefined时应该显示表格', () => {
      const wrapper = createWrapper({ viewData: undefined });

      expect(wrapper.find('table').exists()).toBe(true);
    });

    test('当viewData为空对象时应该显示表格', () => {
      const wrapper = createWrapper({ viewData: {} });

      expect(wrapper.find('table').exists()).toBe(true);
    });
  });

  describe('数据显示', () => {
    test('应该正确显示所有字段', () => {
      const testData = {
        Date: '2023-12-25',
        Agency: '测试机关',
        Reason: '测试原因'
      };

      const wrapper = createWrapper({ viewData: testData });

      expect(wrapper.text()).toContain('2023-12-25');
      expect(wrapper.text()).toContain('测试机关');
      expect(wrapper.text()).toContain('测试原因');
    });

    test('应该正确处理部分字段为空的情况', () => {
      const testData = {
        Date: '2023-12-25',
        Agency: '',
        Reason: '测试原因'
      };

      const wrapper = createWrapper({ viewData: testData });

      expect(wrapper.text()).toContain('2023-12-25');
      expect(wrapper.text()).toContain('测试原因');
      // Agency为空时应该显示'-'
      const agencyCells = wrapper.findAll('td').filter((cell: any) =>
        cell.text().includes('-') && !cell.text().includes('列入')
      );
      expect(agencyCells.length).toBeGreaterThan(0);
    });
  });
});
