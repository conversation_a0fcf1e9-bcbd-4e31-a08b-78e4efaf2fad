import { shallowMount } from '@vue/test-utils';

import EntType from '../index.tsx';

describe('EntType', () => {
  let wrapper: any;
  let mockOptions: any;

  beforeEach(() => {
    mockOptions = {
      propsData: {
        viewData: {
          KeyNo: 'KEY123',
          Name: '测试企业有限公司',
          ChangeDate: '2023-01-01',
          BeforeContent: '有限责任公司',
          AfterContent: '股份有限公司'
        },
      },
      stubs: {
        'q-entity-link': true
      }
    };
  });

  const createWrapper = (propsData: any = {}, options: any = {}) => {
    const mergedOptions = {
      ...mockOptions,
      propsData: {
        viewData: {
          ...mockOptions.propsData.viewData,
          ...propsData.viewData,
        },
      },
      ...options,
    };

    wrapper = shallowMount(EntType, mergedOptions);
    return wrapper;
  };

  afterEach(() => {
    if (wrapper) {
      wrapper.destroy();
    }
  });

  describe('基本信息渲染', () => {
    test('应该正确渲染变更企业信息', () => {
      const wrapper = createWrapper();

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('变更企业');
      expect(wrapper.find('q-entity-link-stub').exists()).toBe(true);
    });

    test('应该正确显示变更日期', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('变更日期');
      expect(wrapper.text()).toContain('2023-01-01');
    });

    test('应该正确显示变更前内容', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('变更前');
      expect(wrapper.text()).toContain('有限责任公司');
    });

    test('应该正确显示变更后内容', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('变更后');
      expect(wrapper.text()).toContain('股份有限公司');
    });
  });

  describe('实体链接处理', () => {
    test('当有KeyNo时应该显示实体链接', () => {
      const wrapper = createWrapper();

      expect(wrapper.find('q-entity-link-stub').exists()).toBe(true);
      expect(wrapper.find('q-entity-link-stub').attributes('coy-obj')).toBeDefined();
    });

    test('当没有KeyNo时应该显示普通文本', () => {
      const wrapper = createWrapper({
        viewData: {
          KeyNo: '',
          Name: '测试企业有限公司',
          ChangeDate: '2023-01-01',
          BeforeContent: '有限责任公司',
          AfterContent: '股份有限公司'
        }
      });

      expect(wrapper.find('q-entity-link-stub').exists()).toBe(false);
      expect(wrapper.text()).toContain('测试企业有限公司');
    });

    test('当KeyNo为null时应该显示普通文本', () => {
      const wrapper = createWrapper({
        viewData: {
          KeyNo: null,
          Name: '测试企业有限公司',
          ChangeDate: '2023-01-01',
          BeforeContent: '有限责任公司',
          AfterContent: '股份有限公司'
        }
      });

      expect(wrapper.find('q-entity-link-stub').exists()).toBe(false);
      expect(wrapper.text()).toContain('测试企业有限公司');
    });
  });

  describe('边界情况', () => {
    test('应该处理空的viewData', () => {
      const wrapper = createWrapper({ viewData: {} });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理undefined的字段', () => {
      const wrapper = createWrapper({
        viewData: {
          KeyNo: undefined,
          Name: undefined,
          ChangeDate: undefined,
          BeforeContent: undefined,
          AfterContent: undefined
        }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理null值字段', () => {
      const wrapper = createWrapper({
        viewData: {
          KeyNo: null,
          Name: null,
          ChangeDate: null,
          BeforeContent: null,
          AfterContent: null
        }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理空字符串', () => {
      const wrapper = createWrapper({
        viewData: {
          KeyNo: '',
          Name: '',
          ChangeDate: '',
          BeforeContent: '',
          AfterContent: ''
        }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理部分字段为空的情况', () => {
      const wrapper = createWrapper({
        viewData: {
          KeyNo: '', // 没有KeyNo，这样企业名称会显示在文本中
          Name: '测试企业',
          ChangeDate: '',
          BeforeContent: '有限责任公司',
          AfterContent: ''
        }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('测试企业');
      expect(wrapper.text()).toContain('有限责任公司');
      expect(wrapper.text()).toContain('-');
    });
  });

  describe('表格结构', () => {
    test('应该渲染正确的表格结构', () => {
      const wrapper = createWrapper();

      const table = wrapper.find('table');
      expect(table.exists()).toBe(true);
      expect(table.classes()).toContain('ntable');

      const rows = wrapper.findAll('tr');
      expect(rows.length).toBe(2);
    });

    test('应该有正确的列宽设置', () => {
      const wrapper = createWrapper();

      const cells23 = wrapper.findAll('td[width="23%"]');
      expect(cells23.length).toBe(4);

      const cells27 = wrapper.findAll('td[width="27%"]');
      expect(cells27.length).toBe(4);
    });

    test('应该有正确的样式类', () => {
      const wrapper = createWrapper();

      const tbCells = wrapper.findAll('td.tb');
      expect(tbCells.length).toBe(4);
    });
  });

  describe('组件属性', () => {
    test('应该正确接收viewData属性', () => {
      const wrapper = createWrapper();

      expect(wrapper.vm.viewData).toEqual(mockOptions.propsData.viewData);
    });

    test('应该有默认的viewData', () => {
      const wrapper = shallowMount(EntType);

      expect(wrapper.vm.viewData).toEqual({});
    });
  });

  describe('条件渲染', () => {
    test('当有KeyNo时应该渲染实体链接', () => {
      const wrapper = createWrapper();

      const entityLink = wrapper.find('q-entity-link-stub');
      expect(entityLink.exists()).toBe(true);

      // 检查传递给实体链接的属性
      const coyObj = entityLink.attributes('coy-obj');
      expect(coyObj).toBeDefined();
    });

    test('当没有KeyNo时应该渲染span元素', () => {
      const wrapper = createWrapper({
        viewData: {
          KeyNo: '',
          Name: '测试企业'
        }
      });

      expect(wrapper.find('q-entity-link-stub').exists()).toBe(false);
      // 检查是否有span元素包含企业名称
      const spans = wrapper.findAll('span');
      let hasNameSpan = false;
      for (let i = 0; i < spans.length; i++) {
        if (spans.at(i).text().includes('测试企业')) {
          hasNameSpan = true;
          break;
        }
      }
      expect(hasNameSpan).toBe(true);
    });
  });

  describe('数据显示', () => {
    test('应该正确显示所有字段', () => {
      const testData = {
        KeyNo: 'TEST123',
        Name: '测试公司',
        ChangeDate: '2023-12-25',
        BeforeContent: '变更前内容',
        AfterContent: '变更后内容'
      };

      const wrapper = createWrapper({ viewData: testData });

      expect(wrapper.text()).toContain('2023-12-25');
      expect(wrapper.text()).toContain('变更前内容');
      expect(wrapper.text()).toContain('变更后内容');
    });

    test('应该正确处理部分字段为空的情况', () => {
      const testData = {
        KeyNo: 'TEST123',
        Name: '测试公司',
        ChangeDate: '2023-12-25',
        BeforeContent: '',
        AfterContent: '变更后内容'
      };

      const wrapper = createWrapper({ viewData: testData });

      expect(wrapper.text()).toContain('2023-12-25');
      expect(wrapper.text()).toContain('变更后内容');
      // BeforeContent为空时应该显示'-'
      const beforeCells = wrapper.findAll('td').filter((cell: any) =>
        cell.text().includes('-') && !cell.text().includes('变更')
      );
      expect(beforeCells.length).toBeGreaterThan(0);
    });
  });

  describe('企业名称显示', () => {
    test('当有Name时应该显示企业名称', () => {
      const wrapper = createWrapper({
        viewData: {
          KeyNo: '',
          Name: '显示的企业名称'
        }
      });

      expect(wrapper.text()).toContain('显示的企业名称');
    });

    test('当Name为空时应该显示横线', () => {
      const wrapper = createWrapper({
        viewData: {
          KeyNo: '',
          Name: ''
        }
      });

      // 检查企业名称对应的位置是否显示横线
      const spans = wrapper.findAll('span');
      let hasDashSpan = false;
      for (let i = 0; i < spans.length; i++) {
        if (spans.at(i).text() === '-') {
          hasDashSpan = true;
          break;
        }
      }
      expect(hasDashSpan).toBe(true);
    });
  });
});
