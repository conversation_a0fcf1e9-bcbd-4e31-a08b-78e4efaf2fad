import { defineComponent, PropType } from 'vue';

import styles from './index.module.less';

interface ViewData {
  KeyNo?: string;
  Name?: string;
  ChangeDate?: string;
  BeforeContent?: string;
  AfterContent?: string;
}

const EntType = defineComponent({
  name: 'EntType',

  props: {
    viewData: {
      type: Object as PropType<ViewData>,
      default: () => ({}),
    },
  },

  render() {
    const { viewData } = this;

    return (
      <div>
        <table class={styles.ntable}>
          <tr>
            <td width="23%" class={styles.tb}>变更企业：</td>
            <td width="27%">
              {viewData.KeyNo ? (
                <q-entity-link coy-obj={{ KeyNo: viewData.KeyNo, Name: viewData.Name }} />
              ) : (
                <span>{viewData.Name || '-'}</span>
              )}
            </td>
            <td width="23%" class={styles.tb}>变更日期：</td>
            <td width="27%">
              {viewData.ChangeDate || '-'}
            </td>
          </tr>
          <tr>
            <td class={styles.tb} width="23%">变更前：</td>
            <td width="27%">
              {viewData.BeforeContent || '-'}
            </td>
            <td class={styles.tb} width="23%">变更后：</td>
            <td width="27%">
              {viewData.AfterContent || '-'}
            </td>
          </tr>
        </table>
      </div>
    );
  },
});

export default EntType;
