import { shallowMount } from '@vue/test-utils';

import ActualController from '..';

describe('ActualController', () => {
  test('renders with empty viewData', () => {
    const wrapper = shallowMount(ActualController, {
      propsData: {
        viewData: {},
      },
    });

    expect(wrapper.find('table').exists()).toBe(true);
    expect(wrapper.text()).toContain('变更企业：');
    expect(wrapper.text()).toContain('变更日期：');
    expect(wrapper.text()).toContain('变更前：');
    expect(wrapper.text()).toContain('变更后：');
  });

  test('renders with complete viewData', () => {
    const viewData = {
      KeyNo: 'test-key',
      Name: 'Test Company',
      ChangeDate: '2023-01-01',
      BeforeObject: {
        KeyNo: 'before-key',
        Name: 'Before Company',
      },
      AfterObject: {
        KeyNo: 'after-key',
        Name: 'After Company',
      },
      AfterControlPath: [
        [
          {
            KeyNo: 'path-key-1',
            Name: 'Path Company 1',
            Percent: '50%',
          },
          {
            KeyNo: 'path-key-2',
            Name: 'Path Company 2',
            Percent: '100%',
          },
        ],
      ],
    };

    const wrapper = shallowMount(ActualController, {
      propsData: {
        viewData,
      },
    });

    expect(wrapper.text()).toContain('Test Company');
    expect(wrapper.text()).toContain('2023-01-01');
    expect(wrapper.text()).toContain('Before Company');
    expect(wrapper.text()).toContain('After Company');
    expect(wrapper.text()).toContain('变更后控制链路：');
    expect(wrapper.text()).toContain('Path Company 1');
    expect(wrapper.text()).toContain('Path Company 2');
    expect(wrapper).toMatchSnapshot();
  });

  test('renders without AfterControlPath', () => {
    const viewData = {
      KeyNo: 'test-key',
      Name: 'Test Company',
      ChangeDate: '2023-01-01',
    };

    const wrapper = shallowMount(ActualController, {
      propsData: {
        viewData,
      },
    });

    expect(wrapper.text()).not.toContain('变更后控制链路：');
  });
});
