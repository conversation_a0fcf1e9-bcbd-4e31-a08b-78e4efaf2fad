// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`ActualController > renders with complete viewData 1`] = `
<div>
  <table class="ntable">
    <tr>
      <td width="23%" class="tb">变更企业：</td>
      <td width="27%"><span class="container q-entity-link"><span class="content ellipsis"><anonymous-stub to="" href="/embed/companyDetail?keyNo=test-key&amp;title=Test Company" display="inline"><span>Test Company</span></anonymous-stub><span class="extra" style="display: none;"></span></span></span></td>
      <td width="23%" class="tb">变更日期：</td>
      <td width="27%">2023-01-01</td>
    </tr>
    <tr>
      <td width="23%" class="tb">变更前：</td>
      <td width="27%"><span class="container q-entity-link"><span class="content ellipsis"><anonymous-stub to="" href="/embed/companyDetail?keyNo=before-key&amp;title=Before Company" display="inline"><span>Before Company</span></anonymous-stub><span class="extra" style="display: none;"></span></span></span></td>
      <td width="23%" class="tb">变更后：</td>
      <td width="27%"><span class="container q-entity-link"><span class="content ellipsis"><anonymous-stub to="" href="/embed/companyDetail?keyNo=after-key&amp;title=After Company" display="inline"><span>After Company</span></anonymous-stub><span class="extra" style="display: none;"></span></span></span></td>
    </tr>
    <tr>
      <td width="23%" class="tb">变更后控制链路：</td>
      <td colspan="3" class="tdPath">
        <div>1.<span><span class="container q-entity-link"><span class="content ellipsis"><anonymous-stub to="" href="/embed/beneficaryDetail?personId=path-key-1&amp;title=Path Company 1" display="inline"><span>Path Company 1</span></anonymous-stub><span class="extra" style="display: none;"></span></span></span><span class="arrow">50%</span></span><span><span class="container q-entity-link"><span class="content ellipsis"><anonymous-stub to="" href="/embed/beneficaryDetail?personId=path-key-2&amp;title=Path Company 2" display="inline"><span>Path Company 2</span></anonymous-stub><span class="extra" style="display: none;"></span></span></span></span></div>
      </td>
    </tr>
  </table>
</div>
`;
