import { defineComponent, PropType } from 'vue';
import _ from 'lodash';

import QEntityLink from '@/components/global/q-entity-link';

import styles from './index.module.less';

interface ViewData {
  KeyNo?: string;
  Name?: string;
  ChangeDate?: string;
  BeforeObject?: {
    KeyNo?: string;
    Name?: string;
  };
  AfterObject?: {
    KeyNo?: string;
    Name?: string;
  };
  AfterControlPath?: Array<
    Array<{
      KeyNo?: string;
      Name?: string;
      Percent?: string;
    }>
  >;
}

const ActualController = defineComponent({
  name: 'ActualController',

  props: {
    viewData: {
      type: Object as PropType<ViewData>,
      default: () => ({}),
    },
  },

  setup() {
    const isPerson = (keyNo: string) => {
      return _.startsWith(keyNo, 'p');
    };

    return {
      isPerson,
    };
  },

  render() {
    const { viewData } = this;

    return (
      <div>
        <table class="ntable">
          <tr>
            <td width="23%" class="tb">
              变更企业：
            </td>
            <td width="27%">{viewData.KeyNo ? <QEntityLink coyObj={{ KeyNo: viewData.KeyNo, Name: viewData.Name }} /> : <span>-</span>}</td>
            <td width="23%" class="tb">
              变更日期：
            </td>
            <td width="27%">{viewData.ChangeDate || '-'}</td>
          </tr>
          <tr>
            <td class="tb" width="23%">
              变更前：
            </td>
            <td width="27%">
              {viewData.BeforeObject ? (
                <QEntityLink
                  coyObj={{
                    KeyNo: viewData.BeforeObject.KeyNo,
                    Name: viewData.BeforeObject.Name,
                  }}
                />
              ) : (
                <span>-</span>
              )}
            </td>
            <td class="tb" width="23%">
              变更后：
            </td>
            <td width="27%">
              {viewData.AfterObject ? (
                <QEntityLink
                  coyObj={{
                    KeyNo: viewData.AfterObject.KeyNo,
                    Name: viewData.AfterObject.Name,
                  }}
                />
              ) : (
                <span>-</span>
              )}
            </td>
          </tr>
          {viewData.AfterControlPath && (
            <tr>
              <td class="tb" width="23%">
                变更后控制链路：
              </td>
              <td colspan="3" class={styles.tdPath}>
                {viewData.AfterControlPath.length > 0 ? (
                  viewData.AfterControlPath.map((item, index) => (
                    <div key={index}>
                      {index + 1}.
                      {item.map((v, k) => (
                        <span key={`${index}-${k}`}>
                          {v.KeyNo ? <QEntityLink coyObj={{ KeyNo: v.KeyNo, Name: v.Name || '-' }} /> : <span>{v.Name || '-'}</span>}
                          {k < item.length - 1 && <span class={[styles.arrow, !v.Percent && styles.arrowNoText]}>{v.Percent}</span>}
                        </span>
                      ))}
                    </div>
                  ))
                ) : (
                  <span>-</span>
                )}
              </td>
            </tr>
          )}
        </table>
      </div>
    );
  },
});

export default ActualController;
