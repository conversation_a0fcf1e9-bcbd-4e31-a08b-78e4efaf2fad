import { shallowMount } from '@vue/test-utils';

import Drc from '../index.tsx';

describe('Drc', () => {
  let wrapper: any;
  let mockOptions: any;

  beforeEach(() => {
    mockOptions = {
      propsData: {
        viewData: [
          {
            CheckPlanNo: 'PLAN001',
            CheckPlanName: '测试计划',
            CheckTaskNo: 'TASK001',
            CheckTaskName: '测试任务',
            CheckBelongOrg: '测试机关',
            CheckDoneDate: '2023-01-01',
            Detail: [
              {
                CheckItem: '检查事项1',
                CheckResult: '检查结果1',
                Status: 1
              },
              {
                CheckItem: '检查事项2',
                CheckResult: '检查结果2',
                Status: 0
              }
            ]
          }
        ],
      },
    };
  });

  const createWrapper = (propsData: any = {}, options: any = {}) => {
    const mergedOptions = {
      ...mockOptions,
      propsData: {
        viewData: propsData.viewData || mockOptions.propsData.viewData,
      },
      ...options,
    };

    wrapper = shallowMount(Drc, mergedOptions);
    return wrapper;
  };

  afterEach(() => {
    if (wrapper) {
      wrapper.destroy();
    }
  });

  describe('基本信息渲染', () => {
    test('应该正确渲染计划信息', () => {
      const wrapper = createWrapper();

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('计划编号');
      expect(wrapper.text()).toContain('计划名称');
      expect(wrapper.text()).toContain('PLAN001');
      expect(wrapper.text()).toContain('测试计划');
    });

    test('应该正确显示任务信息', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('任务编号');
      expect(wrapper.text()).toContain('任务名称');
      expect(wrapper.text()).toContain('TASK001');
      expect(wrapper.text()).toContain('测试任务');
    });

    test('应该正确显示抽查机关和完成日期', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('抽查机关');
      expect(wrapper.text()).toContain('完成日期');
      expect(wrapper.text()).toContain('测试机关');
      expect(wrapper.text()).toContain('2023-01-01');
    });
  });

  describe('详细信息渲染', () => {
    test('应该正确渲染检查事项', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('事项1');
      expect(wrapper.text()).toContain('事项2');
      expect(wrapper.text()).toContain('检查事项1');
      expect(wrapper.text()).toContain('检查事项2');
    });

    test('应该正确显示检查结果', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('结果');
      expect(wrapper.text()).toContain('检查结果1');
      expect(wrapper.text()).toContain('检查结果2');
    });

    test('应该根据Status设置样式', () => {
      const wrapper = createWrapper();

      // 查找所有包含"检查结果2"的td元素
      const allCells = wrapper.findAll('td');
      let statusZeroCell = null;

      for (let i = 0; i < allCells.length; i++) {
        const cell = allCells.at(i);
        if (cell.text().includes('检查结果2')) {
          statusZeroCell = cell;
          break;
        }
      }

      expect(statusZeroCell).not.toBeNull();
      expect(statusZeroCell.classes()).toContain('inQuality');
    });
  });

  describe('边界情况', () => {
    test('应该处理空的viewData', () => {
      const wrapper = createWrapper({ viewData: [] });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理没有Detail的数据', () => {
      const wrapper = createWrapper({
        viewData: [
          {
            CheckPlanNo: 'PLAN001',
            CheckPlanName: '测试计划',
            CheckTaskNo: 'TASK001',
            CheckTaskName: '测试任务',
            CheckBelongOrg: '测试机关',
            CheckDoneDate: '2023-01-01',
            Detail: []
          }
        ]
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('PLAN001');
      expect(wrapper.text()).not.toContain('事项1');
    });

    test('应该处理undefined的字段', () => {
      const wrapper = createWrapper({
        viewData: [
          {
            CheckPlanNo: undefined,
            CheckPlanName: undefined,
            CheckTaskNo: undefined,
            CheckTaskName: undefined,
            CheckBelongOrg: undefined,
            CheckDoneDate: undefined,
            Detail: undefined
          }
        ]
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理null值', () => {
      const wrapper = createWrapper({
        viewData: [
          {
            CheckPlanNo: null,
            CheckPlanName: null,
            CheckTaskNo: null,
            CheckTaskName: null,
            CheckBelongOrg: null,
            CheckDoneDate: null,
            Detail: null
          }
        ]
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });
  });

  describe('组件数据', () => {
    test('应该正确设置viewDetail', () => {
      const wrapper = createWrapper();

      expect(wrapper.vm.viewDetail).toEqual(mockOptions.propsData.viewData[0]);
    });

    test('当viewData为空时，viewDetail应该为空对象', () => {
      const wrapper = createWrapper({ viewData: [] });

      expect(wrapper.vm.viewDetail).toEqual({});
    });
  });

  describe('表格结构', () => {
    test('应该渲染正确的表格结构', () => {
      const wrapper = createWrapper();

      const table = wrapper.find('q-plain-table');
      expect(table.exists()).toBe(true);

      const tbody = wrapper.find('tbody');
      expect(tbody.exists()).toBe(true);

      const rows = wrapper.findAll('tr');
      expect(rows.length).toBeGreaterThan(3); // 基本信息3行 + 详细信息行
    });

    test('应该有正确的列宽设置', () => {
      const wrapper = createWrapper();

      const cells23 = wrapper.findAll('th[width="23%"]');
      expect(cells23.length).toBeGreaterThan(0);

      const cells27 = wrapper.findAll('td[width="27%"]');
      expect(cells27.length).toBeGreaterThan(0);
    });
  });
});
