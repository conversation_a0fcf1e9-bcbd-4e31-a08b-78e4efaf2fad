<template>
  <q-plain-table>
    <tbody>
      <tr>
        <th width="23%">计划编号</th>
        <td width="27%">{{ viewDetail.CheckPlanNo || '-' }}</td>
        <th width="23%">计划名称</th>
        <td width="27%">{{ viewDetail.CheckPlanName || '-' }}</td>
      </tr>
      <tr>
        <th width="23%">任务编号</th>
        <td width="27%">{{ viewDetail.CheckTaskNo || '-' }}</td>
        <th width="23%">任务名称</th>
        <td width="27%">{{ viewDetail.CheckTaskName || '-' }}</td>
      </tr>
      <tr>
        <th width="23%">抽查机关</th>
        <td width="27%">{{ viewDetail.CheckBelongOrg || '-' }}</td>
        <th width="23%">完成日期</th>
        <td width="27%">{{ viewDetail.CheckDoneDate | dateformat('YYYY-MM-DD') }}</td>
      </tr>
      <template v-if="viewDetail.Detail && viewDetail.Detail.length">
        <template v-for="(item, index) in viewDetail.Detail">
          <tr :key="`item-${index}`">
            <th>事项{{ index + 1 }}</th>
            <td colspan="5">{{ item.CheckItem || '-' }}</td>
          </tr>
          <tr :key="`item-result-${index}`">
            <th>结果</th>
            <td colspan="5" :class="[item.Status === 0 && 'in-quality']">{{ item.CheckResult || '-' }}</td>
          </tr>
        </template>
      </template>
    </tbody>
  </q-plain-table>
</template>

<script src="./component.js"></script>
<style lang="less" scoped>
.in-quality {
  color: #f04040;
}
</style>
