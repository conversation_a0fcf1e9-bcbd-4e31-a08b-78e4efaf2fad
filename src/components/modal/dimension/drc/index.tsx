import { defineComponent, PropType, computed } from 'vue';

import dateFormat from '@/utils/format/date';

import styles from './index.module.less';

interface DetailItem {
  CheckItem?: string;
  CheckResult?: string;
  Status?: number;
}

interface ViewDetailItem {
  CheckPlanNo?: string;
  CheckPlanName?: string;
  CheckTaskNo?: string;
  CheckTaskName?: string;
  CheckBelongOrg?: string;
  CheckDoneDate?: string;
  Detail?: DetailItem[];
}

const Drc = defineComponent({
  name: 'Drc',

  props: {
    viewData: {
      type: Array as PropType<ViewDetailItem[]>,
      default: () => [],
    },
  },

  setup(props) {
    const viewDetail = computed(() => {
      return props.viewData.length ? props.viewData[0] : {};
    });

    return {
      viewDetail,
    };
  },

  render() {
    const { viewDetail } = this;

    return (
      <q-plain-table>
        <tbody>
          <tr>
            <th width="23%">计划编号</th>
            <td width="27%">{viewDetail.CheckPlanNo || '-'}</td>
            <th width="23%">计划名称</th>
            <td width="27%">{viewDetail.CheckPlanName || '-'}</td>
          </tr>
          <tr>
            <th width="23%">任务编号</th>
            <td width="27%">{viewDetail.CheckTaskNo || '-'}</td>
            <th width="23%">任务名称</th>
            <td width="27%">{viewDetail.CheckTaskName || '-'}</td>
          </tr>
          <tr>
            <th width="23%">抽查机关</th>
            <td width="27%">{viewDetail.CheckBelongOrg || '-'}</td>
            <th width="23%">完成日期</th>
            <td width="27%">{dateFormat(viewDetail.CheckDoneDate, { pattern: 'YYYY-MM-DD' })}</td>
          </tr>
          {viewDetail.Detail &&
            viewDetail.Detail.length > 0 &&
            viewDetail.Detail.map((item, index) => [
              <tr key={`item-${index}`}>
                <th>事项{index + 1}</th>
                <td colspan="5">{item.CheckItem || '-'}</td>
              </tr>,
              <tr key={`item-result-${index}`}>
                <th>结果</th>
                <td colspan="5" class={[item.Status === 0 && styles.inQuality]}>
                  {item.CheckResult || '-'}
                </td>
              </tr>,
            ])}
        </tbody>
      </q-plain-table>
    );
  },
});

export default Drc;
