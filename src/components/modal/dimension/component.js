import { omit } from 'lodash';

const groupDialog = 'groupDialog';

const ViewComponentsMap = {
  aco: { name: '行政许可详情', component: () => import('./aco') },
  companyContact: { name: '更多号码', component: () => import('./company-contact') },
  companySamePhone: { name: '疑似同电话企业', component: () => import('./company-same-phone') },
  zhixing: { name: '被执行人详情', component: () => import('./zhixing') },
  shixin: { name: '失信被执行人详情', component: () => import('./shixin') },
  zhongben: { name: '终本案件详情', component: () => import('./zhongben') },
  gonggao: { name: '法院公告详情', component: () => import('./gonggao') },
  realIts: { name: '实缴详情', component: () => import('./realIts') },
  ktnotice: { name: '开庭公告详情', component: () => import('./ktnotice') },
  dnotice: { name: '送达公告详情', component: () => import('./dnotice') },
  assistance: { name: '股权冻结详情', component: () => import('./assistance') },
  bankruptcy: { name: '破产重整详情', component: () => import('./bankruptcy') },
  lian: { name: '立案信息详情', component: () => import('./lian') },
  employeeChange: { name: '主要成员变更详情', component: () => import('./employee-change') },
  passportlimit: { name: '限制出境详情', component: () => import('./passportlimit') },
  sameCaseList: { name: '系列案件', component: () => import('./same-case-list') },
  spotCheckList: { name: '抽查检查详情', component: () => import('./spotCheckList') },
  spotCheck: { name: '抽查检查详情', component: () => import('./spot-check') },
  samePenalty: { name: '相似行政处罚', component: () => import('./samePenalty') },
  pn: { name: '公示催告详情', component: () => import('./pn') },
  sameaco: { name: '相似行政许可', component: () => import('./sameaco') },
  competitor: { name: '全部公告', component: () => import('./competitor') },
  video: { name: '庭审视频', component: () => import('./video') },
  pledge: { name: '股权出质详情', component: () => import('./pledge') },
  gdPledges: { name: '股权出质详情', component: () => import('./gdPledges') }, // 股东标签点击的股权出质
  // spledge: { name: '股权质押详情', component: () => import('./spledge') },
  stockPledge: { name: '股权质押详情', component: () => import('./risk-dynamics/spledge') },
  // 行政处罚详情
  antitrustPunish: {
    name: '行政处罚详情',
    component: () => import('./administrative-punish'),
  },
  owenotice: { name: '欠税公告详情', component: () => import('./owenotice') },
  notallowedentry: { name: '未准入境详情', component: () => import('./notallowedentry') },
  // 商业特许经营
  sytxjy: {
    name: '商业特许经营详情',
    component: () => import('./sytxjy'),
  },
  // 持股详情
  partnerDetail: {
    name: '持股详情',
    component: () => import('./stock-detail'),
  },
  // 公安备案
  webcertdetail: {
    name: '公安备案详情',
    component: () => import('./webcertdetail'),
  },
  // 环保处罚
  env: {
    name: '环保处罚详情',
    component: () => import('./env'),
  },
  // 证书详情
  zhengshuView: {
    name: '证书详情',
    component: () => import('./zhengshuView'),
  },
  // 税收违法
  taxIllegal: {
    name: '税收违法详情',
    component: () => import('./taxIllegal'),
  },
  // 动产抵押
  mPledge: {
    name: '动产抵押详情',
    component: () => import('./mPledge'),
  },
  // 土地抵押
  landmortgage: {
    name: '土地抵押详情',
    component: () => import('./landmortgage'),
  },
  // 劳动仲裁
  ldzcDetail: {
    name: '劳动仲裁详情',
    component: () => import('./ldzc-detail'),
  },
  // 投资链
  invest: {
    name: '投资链',
    component: () => import('./invest'),
  },
  ciax: { name: '海关进出口信用信息详情', component: () => import('./ciax') },
  landpub: { name: '地块公示详情', component: () => import('./landpub') },
  landpurchase: { name: '购地信息详情', component: () => import('./landpurchase') },
  landmarket: { name: '土地转让详情', component: () => import('./landmarket') },
  bond: { name: '违约历程', component: () => import('./bond') },
  telecom: { name: '电信许可详情', component: () => import('./telecom') },
  supplier: { name: '全部采购数据', component: () => import('./supplier') },
  customer: { name: '全部销售数据', component: () => import('./customer') },
  advercheck: { name: '广告审查详情', component: () => import('./advercheck') },
  drc: { name: '双随机抽查详情', component: () => import('./drc') },
  foodsafety: { name: '食品安全详情', component: () => import('./foodsafety') },
  relatedBenefitPerson: {
    name: '作为受益所有人企业',
    component: () => import('./relateBenefitPerson'),
  },
  investBenefit: {
    name: '股权链',
    component: () => import('./investBenefit'),
  },
  // 担保信息详情
  guarantor: { name: '担保信息详情', component: () => import('./guarantor') },
  riskGuarantor: { name: '担保信息详情', component: () => import('./guarantor') },
  // 简易注销详情
  jyzx: { name: '简易注销详情', component: () => import('./jyzx') },
  // 简易注销详情
  // jyzxNew: { name: '简易注销详情', component: () => import('./jyzx') },
  // 注销备案详情
  enliqDetail: { name: '注销备案详情', component: () => import('./enliqDetail') },
  // 股权结构
  guQuan: { name: '股权结构', component: () => import('./guQuan') },
  // 全球参控股企业
  overseaall: { name: '全球参控股企业', component: () => import('./overseaall') },

  // 集团对外投资
  investment: { name: '集团对外投资', component: () => import('./investment') },
  // 集团投资方
  invester: { name: '集团对外投资', component: () => import('./invester') },
  // 往期历史欠税公告
  hisOwenotice: { name: '往期欠税公告', component: () => import('./his-owenotice') },
  personJob: { name: '集团人员', component: () => import('./personJob') },
  stockCompany: { name: '参股控股企业详情', component: () => import('./stock-company') },
  relatedTrade: { name: '关联交易详情', component: () => import('./related-trade') },
  // 人员违规处罚
  weiguiDetail: { name: '违规处理详情', component: () => import('./weigui-detail') },
  weiguiRiskDetail: { name: '违规处理详情', component: () => import('./weigui-detail') },
  // 关联司法案件
  relateCaseList: { name: '关联司法案件', component: () => import('./relateCaseList') },
  // 竞争对手详情
  competitor2: { name: '竞争对手', component: () => import('./competitor2') },
  // 股东变更详情
  holderChange: { name: '股东变更详情', component: () => import('./holder-change') },
  // 经营状态变更详情
  operateStatus: { name: '经营状态变更详情', component: () => import('./operate-status') },
  // 询价评估详情
  inquiryEvaluation: { name: '询价评估详情', component: () => import('./inquiry-evaluation') },
  // 债券信息详情
  bondInfo: { name: '债券信息详情', component: () => import('./bond-info') },
  // 企业自主公示详情
  publicity: { name: '股东出资详情', component: () => import('./publicity') },
  spotcheck: { name: '抽查检查详情', component: () => import('./spotcheck') },
  foodSafetyDetail: { name: '食品安全详情', component: () => import('./foodsafetydetail') },
  // 法定代表人变更
  oper: { name: '法定代表人变更', component: () => import('./oper') },
  actualController: { name: '实际控制人变更', component: () => import('./actual-controller') },
  finalBeneficiary: { name: '受益所有人变更', component: () => import('./final-beneficiary') },
  outboundInvest: { name: '对外投资变更', component: () => import('./outbound-invest') },
  registeredCapital: { name: '注册资本变更', component: () => import('./registered-capital') },
  manageScope: { name: '经营范围变更', component: () => import('./manage-scope') },
  entType: { name: '企业类型变更', component: () => import('./ent-type') },
  entAddress: { name: '企业地址变更', component: () => import('./ent-address') },
  majorStock: { name: '股东变更', component: () => import('./major-stock') },
  keyPerson: { name: '主要成员变更详情', component: () => import('./key-person') },
  entNameChange: { name: '企业名称变更详情', component: () => import('./ent-name-change') },
  // 新增药品抽检
  medicine: { name: '药品抽检详情', component: () => import('./medicine') },
  // 产口质量/产品抽查维度
  productChecked: { name: '产品抽检详情', component: () => import('./product-checked') },
  // 票据违约
  billDefaults: { name: '票据违约', component: () => import('./bill-defaults') },
  // 国央企采购黑名单
  govProcurementIllegal: { name: '黑名单详情', component: () => import('./gov-procurement-illegal') },
  // 终本案件
  endExecutionCase: { name: '终本案件详情', component: () => import('./end-execution-case') },
  decreaseCapiNotice: { name: '减资公告详情', component: () => import('./decrease-capi-notice') },
  // 税务催缴
  taxCallNotice: { name: '税务催缴详情', component: () => import('./taxreminder') },
  // 税务催报
  taxReminder: { name: '税务催报详情', component: () => import('./taxreminder') },
  // 动产查封
  chattelSeizure: { name: '动产查封详情', component: () => import('./chattel-seizure') },

  // -----------------------
  // 风险动态维度
  // -----------------------
  businessStatus: { name: '经营状态变更详情', component: () => import('./risk-dynamics/business-status') },
  evaluationAgency: { name: '询价评估详情', component: () => import('./risk-dynamics/evaluation-agency') },
  spledge: { name: '股权质押详情', component: () => import('./risk-dynamics/spledge') },
  partnerChange: { name: '股东变更详情', component: () => import('./risk-dynamics/partner-change') },
  // oweNotice: { name: '', component: () => import('./risk-dynamics/owe-notice') }, // owenotice => oweNotice
  // billdefault: { name: '', component: () => import('./risk-dynamics/billdefault') }, // billdefault => billDefaults

  // enliqDetail: { name: '', component: () => import('./risk-dynamics/enliq-detail') },
  // guarantor: { name: '', component: () => import('./risk-dynamics/guarantor') },
  // jyzx: { name: '', component: () => import('./risk-dynamics/jyzx') },
  // landmortgage: { name: '', component: () => import('./risk-dynamics/landmortgage') },
  // mPledge: { name: '', component: () => import('./risk-dynamics/m-pledge') },
  // notallowedentry: { name: '', component: () => import('./risk-dynamics/notallowedentry') },
  // pledge: { name: '', component: () => import('./risk-dynamics/pledge') },
  // taxIllegal: { name: '', component: () => import('./risk-dynamics/tax-illegal') },
  // zhixing: { name: '', component: () => import('./risk-dynamics/zhixing') },
  // actualController: { name: '实际控制人变更详情', component: () => import('./risk-dynamics/actual-controller') },
  // finalBeneficiary: { name: '最终受益人变更详情', component: () => import('./risk-dynamics/final-beneficiary') },
  bigStockChange: {
    name: '大股东变更',
    component: () => import('./risk-dynamics/bigstock-change'),
  },
  monitorTaxCall: { name: '税务催缴详情', component: () => import('./taxreminder') },
  monitorTaxReminder: { name: '税务催报详情', component: () => import('./taxreminder') },
  // -----------------------
  // 招标排查图谱关系链上的弹窗
  // -----------------------
  ControlRelation: { name: '控制关系', component: () => import('./control-relation') },
  personShareChange: { name: '对外投资', component: () => import('./person-share-change') },
  separationNotice: { name: '分立公告', component: () => import('./separationNotice') },
};

export default {
  name: 'modal-dimension',
  data() {
    return {
      visible: false,
      size: 'extra-large',
      culComponent: null,
      viewDetail: null,
      field: '',
      title: '',
      tooltip: '',
      count: '',
      detailParams: null,
      dialogProps: null,
      parameter: null,
    };
  },
  computed: {
    isModal() {
      return [
        'supplier-investigate-detail',
        'bidding-investigation-detail',
        'interest-investigation-detail',
        'annual-review-investigate-detail',
        'dashboard',
        'risk-trends-overview',
        'risk-trends-specific',
        'external-risk-trends-overview',
        'external-risk-trends-specific',
        'external-risk-investigation-detail',
        'external-bidding-investigation-detail',
        'external-interest-investigation-detail',
      ].includes(this.$route?.name);
    },
  },
  methods: {
    show(field, params = {}, props = {}) {
      this.field = field;
      this.dialogProps = props;
      if (ViewComponentsMap[field]) {
        this.title = ViewComponentsMap[field].name;
        if (params.title) this.title = params.title;
        if (params.tooltip) this.tooltip = params.tooltip;
        if (params.count) this.count = params.count;
        this.size = params?.size ?? 'extra-large';
        this.culComponent = ViewComponentsMap[field].component;
      }
      // 变更dialog的title
      if (field === groupDialog) {
        this.title = props?.info?.name;
        this.culComponent = () => import('./group-dialog');
      }
      this.getDetail(omit(params, ['tooltip']), props)
        .then(() => {
          // 弹窗内表格埋点：点击表格中链接触发
          if (props.trackEvent) {
            this.$el?.addEventListener('click', (ev) => {
              const currentTd = ev.target.closest('td');
              if (!currentTd) return;
              const siblingEl = currentTd.previousElementSibling;
              if (!siblingEl) return;
              const isTh = siblingEl.className.includes('tb') || siblingEl.nodeName === 'TH';
              const thName = siblingEl.innerText.trim();
              if (isTh && thName) {
                props.trackEvent(thName);
              }
            });
          }
        })
        .catch((err) => {
          console.log(err);
        });
      return new Promise((resolve, reject) => {
        this.$promise = { resolve, reject };
      });
    },
    async getDetail(params) {
      if (['guarantor', 'weiguiDetail', 'employeeChange', 'video', 'publicity', 'spotCheck', 'separationNotice'].indexOf(this.field) > -1) {
        // 使用预先请求数据
        this.viewDetail = params;
      } else if (
        [
          'realIts',
          'competitor',
          'sameaco',
          'companyContact',
          'companyReleated',
          'companySamePhone',
          'webcertdetail',
          'invest',
          'supplier',
          'customer',
          // 'drc',
          groupDialog,
          'foodsafety',
          'relatedBenefitPerson',
          'investBenefit',
          'guQuan',
          'overseaall',
          'investment',
          'invester',
          'hisOwenotice',
          'personJob',
          'stockCompany',
          'relatedTrade',
          'relateCaseList',
          'competitor2',
          'sameCaseList',
          'spotCheckList',
          'samePenalty',
        ].includes(this.field)
      ) {
        // 要分页的维度 组件里自己请求接口
        this.detailParams = params;
      } else {
        try {
          let field = this.field;
          // 使用各维度统一详情接口
          if (params.commonDetail) {
            field = 'commonDetail';
          }
          const data = await this.$service.dimensionDetail[field](params);
          // 公安通告额外处理，调用详情接口后直接打开
          if (this.field === 'publicSecurity') {
            window.open(`/embed/news-detail-page?newsId=${data}&keyNo=${params.KeyNo}`);
            return;
          }
          if (data.Category === 68) {
            this.culComponent = ViewComponentsMap.personShareChange.component;
          }
          this.viewDetail = data.Result || data.Detail || data.data;
          this.parameter = params;
        } catch (error) {
          this.viewDetail = null;
        }
      }
      this.visible = true;
    },
    onHide() {
      setTimeout(() => {
        this.$promise.resolve();
      }, 300);
    },
    onVisibleChange(flag) {
      this.visible = flag;
    },
  },
};
