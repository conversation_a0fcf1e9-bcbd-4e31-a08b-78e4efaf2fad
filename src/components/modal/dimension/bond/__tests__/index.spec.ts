import { shallowMount } from '@vue/test-utils';

import <PERSON> from '../index.tsx';

describe('Bond', () => {
  let wrapper: any;
  let mockOptions: any;

  beforeEach(() => {
    mockOptions = {
      propsData: {
        viewData: {
          DefaultCourse: [
            {
              EventDate: '2023-01-01',
              EventCategoryDesc: '发行阶段',
              EventItem: '债券发行',
              Description: '债券成功发行，募集资金10亿元',
              viewType: true
            },
            {
              EventDate: '2023-06-01',
              EventCategoryDesc: '付息阶段',
              EventItem: '利息支付',
              Description: '按期支付利息',
              viewType: true
            },
            {
              EventDate: '2023-12-31',
              EventCategoryDesc: '到期阶段',
              EventItem: '债券到期',
              Description: '债券到期兑付',
              viewType: false
            }
          ]
        },
      },
    };
  });

  const createWrapper = (propsData: any = {}, options: any = {}) => {
    const mergedOptions = {
      ...mockOptions,
      propsData: {
        viewData: {
          ...mockOptions.propsData.viewData,
          ...propsData.viewData,
        },
      },
      ...options,
    };

    wrapper = shallowMount(Bond, mergedOptions);
    return wrapper;
  };

  afterEach(() => {
    if (wrapper) {
      wrapper.destroy();
    }
  });

  describe('基本渲染', () => {
    test('应该正确渲染债券时间线', () => {
      const wrapper = createWrapper();

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.find('[class*="bondDetail"]').exists()).toBe(true);

      // 检查是否包含预期的文本内容
      expect(wrapper.text()).toContain('发行阶段');
      expect(wrapper.text()).toContain('付息阶段');
      expect(wrapper.text()).toContain('债券到期');
    });

    test('应该正确显示日期', () => {
      const wrapper = createWrapper();

      // 检查日期是否在文本中出现
      expect(wrapper.text()).toContain('2023-01-01');
      expect(wrapper.text()).toContain('2023-06-01');
      expect(wrapper.text()).toContain('2023-12-31');
    });

    test('应该正确显示事件描述', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('发行阶段');
      expect(wrapper.text()).toContain('债券发行');
      expect(wrapper.text()).toContain('债券成功发行，募集资金10亿元');
      expect(wrapper.text()).toContain('付息阶段');
      expect(wrapper.text()).toContain('利息支付');
    });
  });

  describe('时间线样式', () => {
    test('应该正确渲染时间线点', () => {
      const wrapper = createWrapper();

      // 检查是否有时间线点相关的元素
      expect(wrapper.findAll('[class*="breachHistoryListPointWrap"]').length).toBeGreaterThan(0);
      expect(wrapper.findAll('[class*="breachHistoryListPointIcon"]').length).toBeGreaterThan(0);
    });

    test('应该正确处理第一个点的样式', () => {
      const wrapper = createWrapper();

      const firstPointIcon = wrapper.find('[class*="breachHistoryListPointIcon"]');
      expect(firstPointIcon.exists()).toBe(true);
    });

    test('应该正确处理连接线', () => {
      const wrapper = createWrapper();

      const bottomLines = wrapper.findAll('[class*="breachHistoryListPointBottomLine"]');
      // 应该有连接线
      expect(bottomLines.length).toBeGreaterThan(0);
    });
  });

  describe('不同的viewType处理', () => {
    test('当viewType为true时应该显示特定格式', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('发行阶段');
      expect(wrapper.text()).toContain('[债券发行]');
    });

    test('当viewType为false时应该显示不同格式', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('债券到期');
      expect(wrapper.text()).toContain('债券到期兑付');
    });
  });

  describe('边界情况', () => {
    test('应该处理空的DefaultCourse', () => {
      const wrapper = createWrapper({
        viewData: {
          DefaultCourse: []
        }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.findAll('[class*="breachHistoryList"]').length).toBe(0);
    });

    test('应该处理undefined的字段', () => {
      const wrapper = createWrapper({
        viewData: {
          DefaultCourse: [
            {
              EventDate: '2023-01-01',
              EventCategoryDesc: undefined,
              EventItem: undefined,
              Description: undefined,
              viewType: true
            }
          ]
        }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理null值', () => {
      const wrapper = createWrapper({
        viewData: {
          DefaultCourse: [
            {
              EventDate: '2023-01-01',
              EventCategoryDesc: null,
              EventItem: null,
              Description: null,
              viewType: true
            }
          ]
        }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理单个项目', () => {
      const wrapper = createWrapper({
        viewData: {
          DefaultCourse: [
            {
              EventDate: '2023-01-01',
              EventCategoryDesc: '发行阶段',
              EventItem: '债券发行',
              Description: '债券成功发行',
              viewType: true
            }
          ]
        }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('发行阶段');
      expect(wrapper.text()).toContain('债券成功发行');
      // 单个项目不应该有连接线
      expect(wrapper.findAll('[class*="breachHistoryListPointBottomLine"]').length).toBe(0);
    });
  });

  describe('方法测试', () => {
    test('handleAmount方法应该存在', () => {
      const wrapper = createWrapper();

      expect(wrapper.vm.handleAmount).toBeDefined();
      expect(typeof wrapper.vm.handleAmount).toBe('function');
    });

    test('handleAmount方法应该正确处理数值', () => {
      const wrapper = createWrapper();

      // 测试大于0.01的值
      expect(wrapper.vm.handleAmount(1.234)).toBeDefined();

      // 测试小于等于0.01的值
      expect(wrapper.vm.handleAmount(0.005)).toBe(0.005);
    });
  });
});
