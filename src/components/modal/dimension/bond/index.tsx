import { defineComponent, PropType } from 'vue';

import dateFormat from '@/utils/format/date';
import { numberFormat } from '@/utils/number-formatter';

import styles from './index.module.less';

interface DefaultCourseItem {
  EventDate?: string;
  EventCategoryDesc?: string;
  EventItem?: string;
  Description?: string;
  viewType?: boolean;
}

interface ViewData {
  DefaultCourse?: DefaultCourseItem[];
}

const Bond = defineComponent({
  name: 'Bond',

  props: {
    viewData: {
      type: Object as PropType<ViewData>,
      default: () => ({}),
    },
  },

  setup() {
    const handleAmount = (price: number): number | string => {
      if (price > 0.01) {
        return numberFormat(price, 2);
      }
      return price;
    };

    return {
      handleAmount,
    };
  },

  render() {
    const { viewData } = this;
    const defaultCourse = viewData.DefaultCourse || [];

    return (
      <div class={styles.bondDetail}>
        {defaultCourse.map((item, index) => (
          <div key={`item-${index}`} class={[styles.breachHistoryList, 'clearfix']}>
            <div class={styles.breachHistoryListDate}>{dateFormat(item.EventDate, { pattern: 'YYYY-MM-DD' })}</div>
            <div class={styles.breachHistoryListPointWrap}>
              <div class={[styles.breachHistoryListPointIcon, index === 0 ? '' : styles.lintTop]}></div>
              {index < defaultCourse.length - 1 && <div class={styles.breachHistoryListPointBottomLine}></div>}
            </div>
            {item.viewType ? (
              <div class={styles.breachHistoryListContent}>
                <div class={styles.breachHistoryListContentStage}>{item.EventCategoryDesc || '-'}</div>
                <div class={styles.breachHistoryListContentText}>
                  <span>{item.EventItem ? `[${item.EventItem}]` : ''}</span>
                  {item.Description || '-'}
                </div>
              </div>
            ) : (
              <div class={styles.breachHistoryListContent}>
                <div class={[styles.breachHistoryListContentStage, styles.fontBold]}>{item.EventItem || '-'}</div>
                <div class={styles.breachHistoryListContentText}>{item.Description || '-'}</div>
              </div>
            )}
          </div>
        ))}
      </div>
    );
  },
});

export default Bond;
