<template>
  <div>
    <div class="assistance-container" v-if="viewData.EquityUnFreezeDetail">股权解冻信息</div>
    <div class="assistance-container" v-else-if="viewData.JudicialPartnersChangeDetail">股东变更信息</div>
    <div class="assistance-container" v-else-if="viewData.EquityFreezeDetail">股权冻结信息</div>
    <!--    <div class="assistance-container" v-else>-->
    <!--      <q-no-data></q-no-data>-->
    <!--    </div>-->
    <q-plain-table v-if="viewData.EquityUnFreezeDetail">
      <tbody>
        <tr>
          <td class="tb" width="20%">执行通知书文号</td>
          <td v-if="viewData.CaseSearchId && viewData.ExecutionNoticeNum">
            <a :href="`/embed/courtCaseDetail?caseId=${viewData.CaseSearchId[0]}&title=${viewData.ExecutionNoticeNum}`" target="_blank">
              <span v-html="viewData.ExecutionNoticeNum || '-'"></span>
            </a>
          </td>
          <td width="30%" v-else v-html="viewData.ExecutionNoticeNum || '-'"></td>
          <td class="tb" width="20%">执行事项</td>
          <td width="30%">
            {{ (viewData.EquityUnFreezeDetail && viewData.EquityUnFreezeDetail.ExecutionMatters) || '-' }}
          </td>
        </tr>
        <tr>
          <td class="tb" width="20%">执行法院</td>
          <td width="30%">{{ viewData.EnforcementCourt || '-' }}</td>
          <td class="tb" width="20%">执行文书文号</td>

          <td v-if="viewData.CaseSearchId && viewData.EquityUnFreezeDetail.ExecutionDocNum">
            <a
              :href="`/embed/courtCaseDetail?caseId=${viewData.CaseSearchId[0]}&title=${viewData.EquityUnFreezeDetail.ExecutionDocNum}`"
              target="_blank"
            >
              <span v-html="viewData.EquityUnFreezeDetail.ExecutionDocNum || '-'"></span>
            </a>
          </td>
          <td width="30%" v-else v-html="viewData.EquityUnFreezeDetail.ExecutionDocNum || '-'"></td>
        </tr>
        <tr>
          <td class="tb" width="20%">执行裁定书文号</td>
          <td v-if="viewData.CaseSearchId && viewData.EquityUnFreezeDetail.ExecutionVerdictNum">
            <a
              :href="`/embed/courtCaseDetail?caseId=${viewData.CaseSearchId[0]}&title=${viewData.EquityUnFreezeDetail.ExecutionVerdictNum}`"
              target="_blank"
            >
              <span v-html="viewData.EquityUnFreezeDetail.ExecutionVerdictNum || '-'"></span>
            </a>
          </td>
          <td width="30%" v-else v-html="viewData.EquityUnFreezeDetail.ExecutionVerdictNum || '-'"></td>
          <td class="tb" width="20%">被执行人</td>
          <td width="30%">
            <q-entity-link :coy-obj="{ Name: viewData.ExecutedBy, KeyNo: viewData.KeyNo }"></q-entity-link>
          </td>
        </tr>
        <tr>
          <td class="tb" width="20%">被执行人持有股权、其他投资权益数额</td>
          <td width="30%">{{ viewData.EquityAmount || '-' }}</td>
          <td class="tb" width="20%">冻结股权标的企业</td>
          <td width="30%">
            <q-entity-link :coy-obj="{ Name: viewData.RelatedCompanyInfo.Name, KeyNo: viewData.RelatedCompanyInfo.KeyNo }"></q-entity-link>
          </td>
        </tr>
        <tr>
          <td class="tb" width="20%">被执行人证件种类</td>
          <td width="30%">
            {{ (viewData.EquityUnFreezeDetail && viewData.EquityUnFreezeDetail.ExecutedPersonDocType) || '-' }}
          </td>
          <td class="tb" width="20%">被执行人证件号码</td>
          <td width="30%">
            {{ (viewData.EquityUnFreezeDetail && viewData.EquityUnFreezeDetail.ExecutedPersonDocNum) || '-' }}
          </td>
        </tr>
        <tr>
          <td class="tb" width="20%">解冻机关</td>
          <td width="30%">
            {{ (viewData.EquityUnFreezeDetail && viewData.EquityUnFreezeDetail.ThawOrgan) || '-' }}
          </td>
          <td class="tb" width="20%">解冻文书号</td>
          <td width="30%">
            {{ (viewData.EquityUnFreezeDetail && viewData.EquityUnFreezeDetail.ThawDocNo) || '-' }}
          </td>
        </tr>
        <tr>
          <td class="tb" width="20%">解除冻结日期</td>
          <td width="30%">
            {{ (viewData.EquityUnFreezeDetail && viewData.EquityUnFreezeDetail.UnFreezeDate) || '-' }}
          </td>
          <td class="tb" width="20%">公示日期</td>
          <td width="30%">
            {{ (viewData.EquityUnFreezeDetail && viewData.EquityUnFreezeDetail.PublicDate) || '-' }}
          </td>
        </tr>
      </tbody>
    </q-plain-table>
    <q-plain-table v-else-if="viewData.JudicialPartnersChangeDetail">
      <tbody>
        <tr>
          <td class="tb" width="20%">执行通知书文号</td>
          <td width="30%" colspan="3">{{ viewData.ExecutionNoticeNum || '-' }}</td>
        </tr>
        <tr>
          <td class="tb" width="20%">执行法院</td>
          <td width="30%">{{ viewData.EnforcementCourt || '-' }}</td>
          <td class="tb" width="20%">执行事项</td>
          <td width="30%">
            {{ (viewData.JudicialPartnersChangeDetail && viewData.JudicialPartnersChangeDetail.ExecutionMatters) || '-' }}
          </td>
        </tr>
        <tr>
          <td class="tb" width="20%">执行裁定书文号</td>
          <td width="30%">
            {{ (viewData.JudicialPartnersChangeDetail && viewData.JudicialPartnersChangeDetail.ExecutionVerdictNum) || '-' }}
          </td>
          <td class="tb" width="20%">被执行人</td>
          <td width="30%">
            <q-entity-link :coy-obj="{ Name: viewData.ExecutedBy, KeyNo: viewData.KeyNo }"></q-entity-link>
          </td>
        </tr>
        <tr>
          <td class="tb" width="20%">被执行人持有股权、其他投资权益数额</td>
          <td width="30%">{{ viewData.EquityAmount || '-' }}</td>
          <td class="tb" width="20%">冻结股权标的企业</td>
          <td width="30%">
            <q-entity-link :coy-obj="viewData.RelatedCompanyInfo"></q-entity-link>
          </td>
        </tr>
        <tr>
          <td class="tb" width="20%">被执行人证件种类</td>
          <td width="30%">
            {{ (viewData.JudicialPartnersChangeDetail && viewData.JudicialPartnersChangeDetail.ExecutedPersonDocType) || '-' }}
          </td>
          <td class="tb" width="20%">被执行人证件号码</td>
          <td width="30%">
            {{ (viewData.JudicialPartnersChangeDetail && viewData.JudicialPartnersChangeDetail.ExecutedPersonDocNum) || '-' }}
          </td>
        </tr>
        <tr>
          <td class="tb" width="20%">受让人</td>
          <td width="30%">
            {{ (viewData.JudicialPartnersChangeDetail && viewData.JudicialPartnersChangeDetail.Assignee) || '-' }}
          </td>
          <td class="tb" width="20%">协助执行日期</td>
          <td width="30%">
            {{ (viewData.JudicialPartnersChangeDetail && viewData.JudicialPartnersChangeDetail.AssistExecDate) || '-' }}
          </td>
        </tr>
        <tr>
          <td class="tb" width="20%">受让人证照种类</td>
          <td width="30%">
            {{ (viewData.JudicialPartnersChangeDetail && viewData.JudicialPartnersChangeDetail.AssigneeDocKind) || '-' }}
          </td>
          <td class="tb" width="20%">受让人证照号码</td>
          <td width="30%">
            {{ (viewData.JudicialPartnersChangeDetail && viewData.JudicialPartnersChangeDetail.AssigneeRegNo) || '-' }}
          </td>
        </tr>
      </tbody>
    </q-plain-table>
    <q-plain-table v-else-if="viewData.EquityFreezeDetail">
      <tbody>
        <tr>
          <td class="tb" width="20%">执行通知书文号</td>
          <td v-if="viewData.CaseSearchId && viewData.ExecutionNoticeNum">
            <a :href="`/embed/courtCaseDetail?caseId=${viewData.CaseSearchId[0]}&title=${viewData.ExecutionNoticeNum}`" target="_blank">
              <span v-html="viewData.ExecutionNoticeNum || '-'"></span>
            </a>
          </td>
          <td width="30%" v-else v-html="viewData.ExecutionNoticeNum || '-'"></td>
          <td class="tb" width="20%">执行事项</td>
          <td width="30%">
            {{ (viewData.EquityFreezeDetail && viewData.EquityFreezeDetail.ExecutionMatters) || '-' }}
          </td>
        </tr>
        <tr>
          <td class="tb" width="20%">执行法院</td>
          <td width="30%">{{ viewData.EnforcementCourt || '-' }}</td>
          <td class="tb" width="20%">执行文书文号</td>
          <td width="30%">{{ viewData.ExecutionDocNum || '-' }}</td>
        </tr>
        <tr>
          <td class="tb" width="20%">执行裁定书文号</td>
          <td v-if="viewData.CaseSearchId && viewData.EquityFreezeDetail.ExecutionVerdictNum">
            <a
              :href="`/embed/courtCaseDetail?caseId=${viewData.CaseSearchId[0]}&title=${viewData.EquityFreezeDetail.ExecutionVerdictNum}`"
              target="_blank"
            >
              <span v-html="viewData.EquityFreezeDetail.ExecutionVerdictNum || '-'"></span>
            </a>
          </td>
          <td width="30%" v-else v-html="viewData.EquityFreezeDetail.ExecutionVerdictNum || '-'"></td>
          <td class="tb" width="20%">被执行人</td>
          <td width="30%">
            <q-entity-link :coy-obj="{ Name: viewData.ExecutedBy, KeyNo: viewData.KeyNo }"></q-entity-link>
          </td>
        </tr>
        <tr>
          <td class="tb" width="20%">被执行人持有股权、其他投资权益数额</td>
          <td width="30%">{{ numberToHumanWithUnit(viewData.EquityAmount) || '-' }}</td>
          <td class="tb" width="20%">冻结股权标的企业</td>
          <td width="30%">
            <q-entity-link :coy-obj="viewData.RelatedCompanyInfo"></q-entity-link>
          </td>
        </tr>
        <tr>
          <td class="tb" width="20%">被执行人证件种类</td>
          <td width="30%">
            {{ (viewData.EquityFreezeDetail && viewData.EquityFreezeDetail.ExecutedPersonDocType) || '-' }}
          </td>
          <td class="tb" width="20%">被执行人证件号码</td>
          <td width="30%">
            {{ (viewData.EquityFreezeDetail && viewData.EquityFreezeDetail.ExecutedPersonDocNum) || '-' }}
          </td>
        </tr>
        <tr>
          <td class="tb" width="20%">冻结日期自</td>
          <td width="30%">
            {{ (viewData.EquityFreezeDetail && viewData.EquityFreezeDetail.FreezeStartDate) || '-' }}
          </td>
          <td class="tb" width="20%">冻结日期至</td>
          <td width="30%">
            {{ (viewData.EquityFreezeDetail && viewData.EquityFreezeDetail.FreezeEndDate) || '-' }}
          </td>
        </tr>
        <tr>
          <td class="tb" width="20%">冻结期限</td>
          <td width="30%">
            {{ (viewData.EquityFreezeDetail && viewData.EquityFreezeDetail.FreezeTerm) || '-' }}
          </td>
          <td class="tb" width="20%">公示日期</td>
          <td width="30%">
            {{ (viewData.EquityFreezeDetail && viewData.EquityFreezeDetail.PublicDate) || '-' }}
          </td>
        </tr>
      </tbody>
    </q-plain-table>
    <q-plain-table v-else>
      <tbody>
        <tr>
          <td class="tb" width="20%">执行通知书文号</td>
          <td width="30%" colspan="3">{{ viewData.ExecutionNoticeNum || '-' }}</td>
        </tr>
        <tr>
          <td class="tb" width="20%">执行法院</td>
          <td width="30%">{{ viewData.EnforcementCourt || '-' }}</td>
          <td class="tb" width="20%">执行事项</td>
          <td width="30%">
            {{ (viewData.JudicialPartnersChangeDetail && viewData.JudicialPartnersChangeDetail.ExecutionMatters) || '-' }}
          </td>
        </tr>
        <tr>
          <td class="tb" width="20%">执行裁定书文号</td>
          <td width="30%">
            {{ (viewData.JudicialPartnersChangeDetail && viewData.JudicialPartnersChangeDetail.ExecutionVerdictNum) || '-' }}
          </td>
          <td class="tb" width="20%">被执行人</td>
          <td width="30%">
            <q-entity-link :coy-obj="{ Name: viewData.ExecutedBy, KeyNo: viewData.KeyNo }"></q-entity-link>
          </td>
        </tr>
        <tr>
          <td class="tb" width="20%">被执行人持有股权、其他投资权益数额</td>
          <td width="30%">{{ viewData.EquityAmount || '-' }}</td>
          <td class="tb" width="20%">冻结股权标的企业</td>
          <td width="30%">
            <q-entity-link :coy-obj="viewData.RelatedCompanyInfo"></q-entity-link>
          </td>
        </tr>
        <!--        <tr>-->
        <!--          <td class="tb" width="20%">被执行人证件种类</td>-->
        <!--          <td width="30%">-->
        <!--            {{-->
        <!--              (viewData.JudicialPartnersChangeDetail &&-->
        <!--                viewData.JudicialPartnersChangeDetail.ExecutedPersonDocType) ||-->
        <!--              '-'-->
        <!--            }}-->
        <!--          </td>-->
        <!--          <td class="tb" width="20%">被执行人证件号码</td>-->
        <!--          <td width="30%">-->
        <!--            {{-->
        <!--              (viewData.JudicialPartnersChangeDetail &&-->
        <!--                viewData.JudicialPartnersChangeDetail.ExecutedPersonDocNum) ||-->
        <!--              '-'-->
        <!--            }}-->
        <!--          </td>-->
        <!--        </tr>-->
        <!--        <tr>-->
        <!--          <td class="tb" width="20%">受让人</td>-->
        <!--          <td width="30%">-->
        <!--            {{-->
        <!--              (viewData.JudicialPartnersChangeDetail && viewData.JudicialPartnersChangeDetail.Assignee) || '-'-->
        <!--            }}-->
        <!--          </td>-->
        <!--          <td class="tb" width="20%">协助执行日期</td>-->
        <!--          <td width="30%">-->
        <!--            {{-->
        <!--              (viewData.JudicialPartnersChangeDetail &&-->
        <!--                viewData.JudicialPartnersChangeDetail.AssistExecDate) ||-->
        <!--              '-'-->
        <!--            }}-->
        <!--          </td>-->
        <!--        </tr>-->
        <!--        <tr>-->
        <!--          <td class="tb" width="20%">受让人证照种类</td>-->
        <!--          <td width="30%">-->
        <!--            {{-->
        <!--              (viewData.JudicialPartnersChangeDetail &&-->
        <!--                viewData.JudicialPartnersChangeDetail.AssigneeDocKind) ||-->
        <!--              '-'-->
        <!--            }}-->
        <!--          </td>-->
        <!--          <td class="tb" width="20%">受让人证照号码</td>-->
        <!--          <td width="30%">-->
        <!--            {{-->
        <!--              (viewData.JudicialPartnersChangeDetail &&-->
        <!--                viewData.JudicialPartnersChangeDetail.AssigneeRegNo) ||-->
        <!--              '-'-->
        <!--            }}-->
        <!--          </td>-->
        <!--        </tr>-->
      </tbody>
    </q-plain-table>
    <q-relate-cases :search-params="viewData"></q-relate-cases>
  </div>
</template>

<script src="./component.js"></script>
<style lang="less">
.assistance-container {
  margin-bottom: 10px;
}
</style>
