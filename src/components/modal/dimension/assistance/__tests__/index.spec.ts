import { shallowMount } from '@vue/test-utils';

import Assistance from '../index.tsx';

describe('Assistance', () => {
  let wrapper: any;
  let mockOptions: any;

  beforeEach(() => {
    mockOptions = {
      propsData: {
        viewData: {
          ExecutionNoticeNum: 'ZX123456',
          EnforcementCourt: '北京市第一中级人民法院',
          ExecutedBy: '张三',
          KeyNo: 'c123456',
          EquityAmount: '1000000',
          RelatedCompanyInfo: {
            Name: '测试公司',
            KeyNo: 'c789012'
          },
          CaseSearchId: ['case123']
        },
      },
    };
  });

  const createWrapper = (propsData: any = {}, options: any = {}) => {
    const mergedOptions = {
      ...mockOptions,
      propsData: {
        viewData: {
          ...mockOptions.propsData.viewData,
          ...propsData.viewData,
        },
      },
      ...options,
    };

    wrapper = shallowMount(Assistance, mergedOptions);
    return wrapper;
  };

  afterEach(() => {
    if (wrapper) {
      wrapper.destroy();
    }
  });

  describe('股权解冻信息', () => {
    test('应该正确渲染股权解冻信息', () => {
      const wrapper = createWrapper({
        viewData: {
          EquityUnFreezeDetail: {
            ExecutionMatters: '股权解冻',
            ExecutionDocNum: 'ZX789012',
            ExecutionVerdictNum: 'ZX345678',
            ExecutedPersonDocType: '身份证',
            ExecutedPersonDocNum: '110101199001011234',
            ThawOrgan: '北京市第一中级人民法院',
            ThawDocNo: 'TH123456',
            UnFreezeDate: '2023-01-01',
            PublicDate: '2023-01-02'
          }
        }
      });

      expect(wrapper.text()).toContain('股权解冻信息');
      expect(wrapper.text()).toContain('执行通知书文号');
      expect(wrapper.text()).toContain('执行事项');
      expect(wrapper.text()).toContain('股权解冻');
      expect(wrapper.text()).toContain('解冻机关');
      expect(wrapper.text()).toContain('北京市第一中级人民法院');
    });

    test('应该正确处理案件链接', () => {
      const wrapper = createWrapper({
        viewData: {
          EquityUnFreezeDetail: {
            ExecutionDocNum: 'ZX789012',
            ExecutionVerdictNum: 'ZX345678'
          },
          CaseSearchId: ['case123'],
          ExecutionNoticeNum: 'ZX123456'
        }
      });

      const links = wrapper.findAll('a');
      expect(links.length).toBeGreaterThan(0);
    });
  });

  describe('股东变更信息', () => {
    test('应该正确渲染股东变更信息', () => {
      const wrapper = createWrapper({
        viewData: {
          JudicialPartnersChangeDetail: {
            ExecutionMatters: '股东变更',
            ExecutionVerdictNum: 'ZX345678',
            ExecutedPersonDocType: '身份证',
            ExecutedPersonDocNum: '110101199001011234',
            Assignee: '李四',
            AssistExecDate: '2023-01-01',
            AssigneeDocKind: '身份证',
            AssigneeRegNo: '110101199001011235'
          }
        }
      });

      expect(wrapper.text()).toContain('股东变更信息');
      expect(wrapper.text()).toContain('股东变更');
      expect(wrapper.text()).toContain('受让人');
      expect(wrapper.text()).toContain('李四');
      expect(wrapper.text()).toContain('协助执行日期');
    });
  });

  describe('股权冻结信息', () => {
    test('应该正确渲染股权冻结信息', () => {
      const wrapper = createWrapper({
        viewData: {
          EquityFreezeDetail: {
            ExecutionMatters: '股权冻结',
            ExecutionVerdictNum: 'ZX345678',
            ExecutedPersonDocType: '身份证',
            ExecutedPersonDocNum: '110101199001011234',
            FreezeStartDate: '2023-01-01',
            FreezeEndDate: '2023-12-31',
            FreezeTerm: '12个月',
            PublicDate: '2023-01-02'
          },
          ExecutionDocNum: 'ZX789012'
        }
      });

      expect(wrapper.text()).toContain('股权冻结信息');
      expect(wrapper.text()).toContain('股权冻结');
      expect(wrapper.text()).toContain('冻结日期自');
      expect(wrapper.text()).toContain('冻结日期至');
      expect(wrapper.text()).toContain('冻结期限');
      expect(wrapper.text()).toContain('12个月');
    });

    test('应该正确格式化股权数额', () => {
      const wrapper = createWrapper({
        viewData: {
          EquityFreezeDetail: {
            ExecutionMatters: '股权冻结'
          },
          EquityAmount: '1000000'
        }
      });

      // 检查是否调用了numberToHumanWithUnit方法
      expect(wrapper.vm.numberToHumanWithUnit).toBeDefined();
    });
  });

  describe('默认情况', () => {
    test('应该渲染默认表格', () => {
      const wrapper = createWrapper({
        viewData: {
          // 没有特定的详情字段
        }
      });

      expect(wrapper.text()).toContain('执行通知书文号');
      expect(wrapper.text()).toContain('执行法院');
      expect(wrapper.text()).toContain('被执行人');
    });
  });

  describe('方法测试', () => {
    test('numberToHumanWithUnit方法应该存在', () => {
      const wrapper = createWrapper();

      expect(wrapper.vm.numberToHumanWithUnit).toBeDefined();
      expect(typeof wrapper.vm.numberToHumanWithUnit).toBe('function');
    });
  });

  describe('边界情况', () => {
    test('应该处理空的viewData', () => {
      const wrapper = createWrapper({ viewData: {} });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理undefined的字段', () => {
      const wrapper = createWrapper({
        viewData: {
          ExecutionNoticeNum: undefined,
          EnforcementCourt: undefined,
          ExecutedBy: undefined,
        }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理空的RelatedCompanyInfo', () => {
      const wrapper = createWrapper({
        viewData: {
          RelatedCompanyInfo: null
        }
      });

      expect(wrapper.exists()).toBe(true);
    });
  });
});
