import { defineComponent, PropType } from 'vue';

import QEntityLink from '@/components/global/q-entity-link';
import QPlainTable from '@/components/global/q-plain-table';
import QRelateCases from '@/components/global/q-relate-cases';
import { numberToHumanWithUnit } from '@/utils/number-formatter';

import styles from './index.module.less';

interface CompanyInfo {
  Name?: string;
  KeyNo?: string;
}

interface EquityUnFreezeDetail {
  ExecutionMatters?: string;
  ExecutionDocNum?: string;
  ExecutionVerdictNum?: string;
  ExecutedPersonDocType?: string;
  ExecutedPersonDocNum?: string;
  ThawOrgan?: string;
  ThawDocNo?: string;
  UnFreezeDate?: string;
  PublicDate?: string;
}

interface JudicialPartnersChangeDetail {
  ExecutionMatters?: string;
  ExecutionVerdictNum?: string;
  ExecutedPersonDocType?: string;
  ExecutedPersonDocNum?: string;
  Assignee?: string;
  AssistExecDate?: string;
  AssigneeDocKind?: string;
  AssigneeRegNo?: string;
}

interface EquityFreezeDetail {
  ExecutionMatters?: string;
  ExecutionVerdictNum?: string;
  ExecutedPersonDocType?: string;
  ExecutedPersonDocNum?: string;
  FreezeStartDate?: string;
  FreezeEndDate?: string;
  FreezeTerm?: string;
  PublicDate?: string;
}

interface ViewData {
  EquityUnFreezeDetail?: EquityUnFreezeDetail;
  JudicialPartnersChangeDetail?: JudicialPartnersChangeDetail;
  EquityFreezeDetail?: EquityFreezeDetail;
  ExecutionNoticeNum?: string;
  EnforcementCourt?: string;
  ExecutionDocNum?: string;
  ExecutedBy?: string;
  KeyNo?: string;
  EquityAmount?: string;
  RelatedCompanyInfo?: CompanyInfo;
  CaseSearchId?: string[];
}

const Assistance = defineComponent({
  name: 'Assistance',

  props: {
    viewData: {
      type: Object as PropType<ViewData>,
      default: () => ({}),
    },
  },

  setup() {
    return {
      numberToHumanWithUnit,
    };
  },

  render() {
    const { viewData } = this;

    const renderTitle = () => {
      if (viewData.EquityUnFreezeDetail) {
        return <div class={styles.assistanceContainer}>股权解冻信息</div>;
      } else if (viewData.JudicialPartnersChangeDetail) {
        return <div class={styles.assistanceContainer}>股东变更信息</div>;
      } else if (viewData.EquityFreezeDetail) {
        return <div class={styles.assistanceContainer}>股权冻结信息</div>;
      }
      return null;
    };

    const renderCaseLink = (docNum?: string) => {
      if (viewData.CaseSearchId && docNum) {
        return (
          <a href={`/embed/courtCaseDetail?caseId=${viewData.CaseSearchId[0]}&title=${docNum}`} target="_blank">
            <span domPropsInnerHTML={docNum || '-'}></span>
          </a>
        );
      }
      return <span domPropsInnerHTML={docNum || '-'}></span>;
    };

    return (
      <div>
        {renderTitle()}

        {/* 股权解冻信息表格 */}
        {viewData.EquityUnFreezeDetail && (
          <QPlainTable>
            <tbody>
              <tr>
                <td class="tb" width="20%">
                  执行通知书文号
                </td>
                <td width="30%">{renderCaseLink(viewData.ExecutionNoticeNum)}</td>
                <td class="tb" width="20%">
                  执行事项
                </td>
                <td width="30%">{viewData.EquityUnFreezeDetail.ExecutionMatters || '-'}</td>
              </tr>
              <tr>
                <td class="tb" width="20%">
                  执行法院
                </td>
                <td width="30%">{viewData.EnforcementCourt || '-'}</td>
                <td class="tb" width="20%">
                  执行文书文号
                </td>
                <td width="30%">{renderCaseLink(viewData.EquityUnFreezeDetail.ExecutionDocNum)}</td>
              </tr>
              <tr>
                <td class="tb" width="20%">
                  执行裁定书文号
                </td>
                <td width="30%">{renderCaseLink(viewData.EquityUnFreezeDetail.ExecutionVerdictNum)}</td>
                <td class="tb" width="20%">
                  被执行人
                </td>
                <td width="30%">
                  <QEntityLink coyObj={{ Name: viewData.ExecutedBy, KeyNo: viewData.KeyNo }} />
                </td>
              </tr>
              <tr>
                <td class="tb" width="20%">
                  被执行人持有股权、其他投资权益数额
                </td>
                <td width="30%">{viewData.EquityAmount || '-'}</td>
                <td class="tb" width="20%">
                  冻结股权标的企业
                </td>
                <td width="30%">
                  <QEntityLink coyObj={viewData.RelatedCompanyInfo} />
                </td>
              </tr>
              <tr>
                <td class="tb" width="20%">
                  被执行人证件种类
                </td>
                <td width="30%">{viewData.EquityUnFreezeDetail.ExecutedPersonDocType || '-'}</td>
                <td class="tb" width="20%">
                  被执行人证件号码
                </td>
                <td width="30%">{viewData.EquityUnFreezeDetail.ExecutedPersonDocNum || '-'}</td>
              </tr>
              <tr>
                <td class="tb" width="20%">
                  解冻机关
                </td>
                <td width="30%">{viewData.EquityUnFreezeDetail.ThawOrgan || '-'}</td>
                <td class="tb" width="20%">
                  解冻文书号
                </td>
                <td width="30%">{viewData.EquityUnFreezeDetail.ThawDocNo || '-'}</td>
              </tr>
              <tr>
                <td class="tb" width="20%">
                  解除冻结日期
                </td>
                <td width="30%">{viewData.EquityUnFreezeDetail.UnFreezeDate || '-'}</td>
                <td class="tb" width="20%">
                  公示日期
                </td>
                <td width="30%">{viewData.EquityUnFreezeDetail.PublicDate || '-'}</td>
              </tr>
            </tbody>
          </QPlainTable>
        )}

        {/* 股东变更信息表格 */}
        {viewData.JudicialPartnersChangeDetail && !viewData.EquityUnFreezeDetail && (
          <QPlainTable>
            <tbody>
              <tr>
                <td class="tb" width="20%">
                  执行通知书文号
                </td>
                <td width="30%" colspan="3">
                  {viewData.ExecutionNoticeNum || '-'}
                </td>
              </tr>
              <tr>
                <td class="tb" width="20%">
                  执行法院
                </td>
                <td width="30%">{viewData.EnforcementCourt || '-'}</td>
                <td class="tb" width="20%">
                  执行事项
                </td>
                <td width="30%">{viewData.JudicialPartnersChangeDetail.ExecutionMatters || '-'}</td>
              </tr>
              <tr>
                <td class="tb" width="20%">
                  执行裁定书文号
                </td>
                <td width="30%">{viewData.JudicialPartnersChangeDetail.ExecutionVerdictNum || '-'}</td>
                <td class="tb" width="20%">
                  被执行人
                </td>
                <td width="30%">
                  <QEntityLink coyObj={{ Name: viewData.ExecutedBy, KeyNo: viewData.KeyNo }} />
                </td>
              </tr>
              <tr>
                <td class="tb" width="20%">
                  被执行人持有股权、其他投资权益数额
                </td>
                <td width="30%">{viewData.EquityAmount || '-'}</td>
                <td class="tb" width="20%">
                  冻结股权标的企业
                </td>
                <td width="30%">
                  <QEntityLink coyObj={viewData.RelatedCompanyInfo} />
                </td>
              </tr>
              <tr>
                <td class="tb" width="20%">
                  被执行人证件种类
                </td>
                <td width="30%">{viewData.JudicialPartnersChangeDetail.ExecutedPersonDocType || '-'}</td>
                <td class="tb" width="20%">
                  被执行人证件号码
                </td>
                <td width="30%">{viewData.JudicialPartnersChangeDetail.ExecutedPersonDocNum || '-'}</td>
              </tr>
              <tr>
                <td class="tb" width="20%">
                  受让人
                </td>
                <td width="30%">{viewData.JudicialPartnersChangeDetail.Assignee || '-'}</td>
                <td class="tb" width="20%">
                  协助执行日期
                </td>
                <td width="30%">{viewData.JudicialPartnersChangeDetail.AssistExecDate || '-'}</td>
              </tr>
              <tr>
                <td class="tb" width="20%">
                  受让人证照种类
                </td>
                <td width="30%">{viewData.JudicialPartnersChangeDetail.AssigneeDocKind || '-'}</td>
                <td class="tb" width="20%">
                  受让人证照号码
                </td>
                <td width="30%">{viewData.JudicialPartnersChangeDetail.AssigneeRegNo || '-'}</td>
              </tr>
            </tbody>
          </QPlainTable>
        )}

        {/* 股权冻结信息表格 */}
        {viewData.EquityFreezeDetail && !viewData.EquityUnFreezeDetail && !viewData.JudicialPartnersChangeDetail && (
          <QPlainTable>
            <tbody>
              <tr>
                <td class="tb" width="20%">
                  执行通知书文号
                </td>
                <td width="30%">{renderCaseLink(viewData.ExecutionNoticeNum)}</td>
                <td class="tb" width="20%">
                  执行事项
                </td>
                <td width="30%">{viewData.EquityFreezeDetail.ExecutionMatters || '-'}</td>
              </tr>
              <tr>
                <td class="tb" width="20%">
                  执行法院
                </td>
                <td width="30%">{viewData.EnforcementCourt || '-'}</td>
                <td class="tb" width="20%">
                  执行文书文号
                </td>
                <td width="30%">{viewData.ExecutionDocNum || '-'}</td>
              </tr>
              <tr>
                <td class="tb" width="20%">
                  执行裁定书文号
                </td>
                <td width="30%">{renderCaseLink(viewData.EquityFreezeDetail.ExecutionVerdictNum)}</td>
                <td class="tb" width="20%">
                  被执行人
                </td>
                <td width="30%">
                  <QEntityLink coyObj={{ Name: viewData.ExecutedBy, KeyNo: viewData.KeyNo }} />
                </td>
              </tr>
              <tr>
                <td class="tb" width="20%">
                  被执行人持有股权、其他投资权益数额
                </td>
                <td width="30%">{this.numberToHumanWithUnit(viewData.EquityAmount) || '-'}</td>
                <td class="tb" width="20%">
                  冻结股权标的企业
                </td>
                <td width="30%">
                  <QEntityLink coyObj={viewData.RelatedCompanyInfo} />
                </td>
              </tr>
              <tr>
                <td class="tb" width="20%">
                  被执行人证件种类
                </td>
                <td width="30%">{viewData.EquityFreezeDetail.ExecutedPersonDocType || '-'}</td>
                <td class="tb" width="20%">
                  被执行人证件号码
                </td>
                <td width="30%">{viewData.EquityFreezeDetail.ExecutedPersonDocNum || '-'}</td>
              </tr>
              <tr>
                <td class="tb" width="20%">
                  冻结日期自
                </td>
                <td width="30%">{viewData.EquityFreezeDetail.FreezeStartDate || '-'}</td>
                <td class="tb" width="20%">
                  冻结日期至
                </td>
                <td width="30%">{viewData.EquityFreezeDetail.FreezeEndDate || '-'}</td>
              </tr>
              <tr>
                <td class="tb" width="20%">
                  冻结期限
                </td>
                <td width="30%">{viewData.EquityFreezeDetail.FreezeTerm || '-'}</td>
                <td class="tb" width="20%">
                  公示日期
                </td>
                <td width="30%">{viewData.EquityFreezeDetail.PublicDate || '-'}</td>
              </tr>
            </tbody>
          </QPlainTable>
        )}

        {/* 默认表格 */}
        {!viewData.EquityUnFreezeDetail && !viewData.JudicialPartnersChangeDetail && !viewData.EquityFreezeDetail && (
          <QPlainTable>
            <tbody>
              <tr>
                <td class="tb" width="20%">
                  执行通知书文号
                </td>
                <td width="30%" colspan="3">
                  {viewData.ExecutionNoticeNum || '-'}
                </td>
              </tr>
              <tr>
                <td class="tb" width="20%">
                  执行法院
                </td>
                <td width="30%">{viewData.EnforcementCourt || '-'}</td>
                <td class="tb" width="20%">
                  执行事项
                </td>
                <td width="30%">
                  {(viewData.JudicialPartnersChangeDetail && viewData.JudicialPartnersChangeDetail.ExecutionMatters) || '-'}
                </td>
              </tr>
              <tr>
                <td class="tb" width="20%">
                  执行裁定书文号
                </td>
                <td width="30%">
                  {(viewData.JudicialPartnersChangeDetail && viewData.JudicialPartnersChangeDetail.ExecutionVerdictNum) || '-'}
                </td>
                <td class="tb" width="20%">
                  被执行人
                </td>
                <td width="30%">
                  <QEntityLink coyObj={{ Name: viewData.ExecutedBy, KeyNo: viewData.KeyNo }} />
                </td>
              </tr>
              <tr>
                <td class="tb" width="20%">
                  被执行人持有股权、其他投资权益数额
                </td>
                <td width="30%">{viewData.EquityAmount || '-'}</td>
                <td class="tb" width="20%">
                  冻结股权标的企业
                </td>
                <td width="30%">
                  <QEntityLink coyObj={viewData.RelatedCompanyInfo} />
                </td>
              </tr>
            </tbody>
          </QPlainTable>
        )}

        <QRelateCases searchParams={viewData} />
      </div>
    );
  },
});

export default Assistance;
