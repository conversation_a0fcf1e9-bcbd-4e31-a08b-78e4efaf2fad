<template>
  <div>
    <table class="ntable">
      <tr>
        <td class="tb" width="20%">当事人</td>
        <td width="30%" v-if="viewData.OwnerInfo">
          <template v-if="viewData.OwnerInfo">
            <q-entity-link :coy-arr="viewData.OwnerInfo"></q-entity-link>
          </template>
          <template v-else>-</template>
        </td>
        <td class="tb" width="20%">案号</td>
        <td width="30%">{{ viewData.CaseNo || '-' }}</td>
      </tr>
      <tr>
        <td class="tb" width="20%">标的物</td>
        <td width="30%">{{ viewData.SubjectName || '-' }}</td>
        <td class="tb" width="20%">财产类型</td>
        <td width="30%">{{ viewData.SubjectType || '-' }}</td>
      </tr>
      <tr>
        <td class="tb" width="20%">法院名称</td>
        <td width="30%">{{ viewData.CourtName || '-' }}</td>
        <td class="tb" width="20%">摇号日期</td>
        <td width="30%">{{ viewData.LotteryDate | dateformat }}</td>
      </tr>
      <tr>
        <td width="20%" class="tb">选定评估机构</td>
        <td colspan="3">
          <template v-if="viewData.AgencyInfo">
            <q-entity-link :coy-arr="viewData.AgencyInfo"></q-entity-link>
          </template>
          <template v-else>-</template>
        </td>
      </tr>
    </table>
  </div>
</template>
<script>
export default {
  props: {
    viewData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
};
</script>
