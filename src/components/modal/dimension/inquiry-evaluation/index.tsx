import { defineComponent, PropType } from 'vue';

import { dateFormat } from '@/utils/format';

import styles from './index.module.less';

interface EntityItem {
  name?: string;
  id?: string;
}

interface ViewData {
  OwnerInfo?: EntityItem[];
  CaseNo?: string;
  SubjectName?: string;
  SubjectType?: string;
  CourtName?: string;
  LotteryDate?: string;
  AgencyInfo?: EntityItem[];
}

const InquiryEvaluation = defineComponent({
  name: 'InquiryEvaluation',

  props: {
    viewData: {
      type: Object as PropType<ViewData>,
      default: () => ({}),
    },
  },

  render() {
    const { viewData } = this;

    return (
      <div>
        <table class={styles.ntable}>
          <tr>
            <td class={styles.tb} width="20%">当事人</td>
            {viewData.OwnerInfo && (
              <td width="30%">
                {viewData.OwnerInfo ? (
                  <q-entity-link coy-arr={viewData.OwnerInfo}></q-entity-link>
                ) : (
                  '-'
                )}
              </td>
            )}
            <td class={styles.tb} width="20%">案号</td>
            <td width="30%">{viewData.CaseNo || '-'}</td>
          </tr>
          <tr>
            <td class={styles.tb} width="20%">标的物</td>
            <td width="30%">{viewData.SubjectName || '-'}</td>
            <td class={styles.tb} width="20%">财产类型</td>
            <td width="30%">{viewData.SubjectType || '-'}</td>
          </tr>
          <tr>
            <td class={styles.tb} width="20%">法院名称</td>
            <td width="30%">{viewData.CourtName || '-'}</td>
            <td class={styles.tb} width="20%">摇号日期</td>
            <td width="30%">{dateFormat(viewData.LotteryDate)}</td>
          </tr>
          <tr>
            <td width="20%" class={styles.tb}>选定评估机构</td>
            <td colspan="3">
              {viewData.AgencyInfo ? (
                <q-entity-link coy-arr={viewData.AgencyInfo}></q-entity-link>
              ) : (
                '-'
              )}
            </td>
          </tr>
        </table>
      </div>
    );
  },
});

export default InquiryEvaluation;
