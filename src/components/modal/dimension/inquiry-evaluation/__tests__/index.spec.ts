import { shallowMount } from '@vue/test-utils';

import InquiryEvaluation from '../index.tsx';

describe('InquiryEvaluation', () => {
  let wrapper: any;
  let mockOptions: any;

  beforeEach(() => {
    mockOptions = {
      propsData: {
        viewData: {
          OwnerInfo: [
            { name: '当事人公司', id: '123' }
          ],
          CaseNo: '(2023)京01执123号',
          SubjectName: '房产',
          SubjectType: '不动产',
          CourtName: '北京市第一中级人民法院',
          LotteryDate: '2023-01-01',
          AgencyInfo: [
            { name: '评估机构', id: '456' }
          ]
        },
      },
      stubs: {
        'q-entity-link': true
      }
    };
  });

  const createWrapper = (propsData: any = {}, options: any = {}) => {
    const mergedOptions = {
      ...mockOptions,
      propsData: {
        viewData: {
          ...mockOptions.propsData.viewData,
          ...propsData.viewData,
        },
      },
      ...options,
    };

    wrapper = shallowMount(InquiryEvaluation, mergedOptions);
    return wrapper;
  };

  afterEach(() => {
    if (wrapper) {
      wrapper.destroy();
    }
  });

  describe('基本信息渲染', () => {
    test('应该正确渲染当事人信息', () => {
      const wrapper = createWrapper();

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('当事人');
      expect(wrapper.find('q-entity-link-stub').exists()).toBe(true);
    });

    test('应该正确显示案号', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('案号');
      expect(wrapper.text()).toContain('(2023)京01执123号');
    });

    test('应该正确显示标的物信息', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('标的物');
      expect(wrapper.text()).toContain('房产');
      expect(wrapper.text()).toContain('财产类型');
      expect(wrapper.text()).toContain('不动产');
    });

    test('应该正确显示法院和摇号日期', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('法院名称');
      expect(wrapper.text()).toContain('北京市第一中级人民法院');
      expect(wrapper.text()).toContain('摇号日期');
      expect(wrapper.text()).toContain('2023-01-01');
    });

    test('应该正确显示评估机构', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('选定评估机构');
      const entityLinks = wrapper.findAll('q-entity-link-stub');
      expect(entityLinks.length).toBe(2); // 当事人 + 评估机构
    });
  });

  describe('边界情况', () => {
    test('应该处理空的viewData', () => {
      const wrapper = createWrapper({ viewData: {} });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理没有当事人信息的情况', () => {
      const wrapper = createWrapper({
        viewData: {
          OwnerInfo: null,
          CaseNo: '(2023)京01执123号'
        }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
      expect(wrapper.text()).toContain('(2023)京01执123号');
    });

    test('应该处理没有评估机构的情况', () => {
      const wrapper = createWrapper({
        viewData: {
          OwnerInfo: [{ name: '当事人公司', id: '123' }],
          AgencyInfo: null
        }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('选定评估机构');
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理undefined的字段', () => {
      const wrapper = createWrapper({
        viewData: {
          CaseNo: undefined,
          SubjectName: undefined,
          SubjectType: undefined,
          CourtName: undefined,
          LotteryDate: undefined
        }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理null值', () => {
      const wrapper = createWrapper({
        viewData: {
          CaseNo: null,
          SubjectName: null,
          SubjectType: null,
          CourtName: null,
          LotteryDate: null
        }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });
  });

  describe('表格结构', () => {
    test('应该渲染正确的表格结构', () => {
      const wrapper = createWrapper();

      const table = wrapper.find('table');
      expect(table.exists()).toBe(true);
      expect(table.classes()).toContain('ntable');

      const rows = wrapper.findAll('tr');
      expect(rows.length).toBe(4);
    });

    test('应该有正确的列宽设置', () => {
      const wrapper = createWrapper();

      const cells20 = wrapper.findAll('td[width="20%"]');
      expect(cells20.length).toBeGreaterThan(0);

      const cells30 = wrapper.findAll('td[width="30%"]');
      expect(cells30.length).toBeGreaterThan(0);
    });

    test('应该有正确的样式类', () => {
      const wrapper = createWrapper();

      const tbCells = wrapper.findAll('td.tb');
      expect(tbCells.length).toBeGreaterThan(0);
    });
  });

  describe('组件属性', () => {
    test('应该正确接收viewData属性', () => {
      const wrapper = createWrapper();

      expect(wrapper.vm.viewData).toEqual(mockOptions.propsData.viewData);
    });

    test('应该有默认的viewData', () => {
      const wrapper = shallowMount(InquiryEvaluation);

      expect(wrapper.vm.viewData).toEqual({});
    });
  });

  describe('条件渲染', () => {
    test('当有OwnerInfo时应该显示实体链接', () => {
      const wrapper = createWrapper();

      // 检查是否有实体链接组件
      const entityLinks = wrapper.findAll('q-entity-link-stub');
      expect(entityLinks.length).toBeGreaterThan(0);
    });

    test('当没有OwnerInfo时应该不显示当事人值的td', () => {
      const wrapper = createWrapper({
        viewData: {
          OwnerInfo: null
        }
      });

      // 当没有OwnerInfo时，对应的td不会渲染（因为v-if="viewData.OwnerInfo"）
      const firstRow = wrapper.findAll('tr').at(0);
      const cells = firstRow.findAll('td');
      // 第一行应该只有3个td：当事人、案号、案号值
      expect(cells.length).toBe(3);
      expect(cells.at(0).text()).toContain('当事人');
      expect(cells.at(1).text()).toContain('案号');
    });

    test('当有AgencyInfo时应该显示实体链接', () => {
      const wrapper = createWrapper();

      const agencyLinks = wrapper.findAll('q-entity-link-stub');
      expect(agencyLinks.length).toBe(2); // 当事人 + 评估机构
    });

    test('当没有AgencyInfo时应该显示横线', () => {
      const wrapper = createWrapper({
        viewData: {
          AgencyInfo: null
        }
      });

      // 检查评估机构对应的td是否包含横线
      const lastRow = wrapper.findAll('tr').at(3);
      const agencyCell = lastRow.findAll('td').at(1);
      expect(agencyCell.text()).toContain('-');
    });
  });
});
