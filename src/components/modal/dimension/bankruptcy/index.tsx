import { defineComponent, PropType } from 'vue';

import QBankruptcyRelat from '@/components/global/q-bankruptcy-relat';
import QCcxs from '@/components/global/q-ccxs';
import QEntityLink from '@/components/global/q-entity-link';
import QPlainTable from '@/components/global/q-plain-table';
import QRelateCases from '@/components/global/q-relate-cases';
import dateFormat from '@/utils/format/date';

import styles from './index.module.less';

interface CompanyInfo {
  Name?: string;
  KeyNo?: string;
}

interface DialogProps {
  ccxsCount?: number;
  keyNo?: string;
}

interface ViewData {
  CaseNo?: string;
  CaseType?: string;
  Respondent?: string;
  RespondentNameAndKeyNo?: CompanyInfo[];
  Applicant?: string;
  ApplicantNameAndKeyNo?: CompanyInfo[];
  ManagementOrganization?: string;
  ResponsiblePerson?: string;
  CourtName?: string;
  RiskDate?: string;
  CaseSearchId?: string[];
}

const Bankruptcy = defineComponent({
  name: 'Bankruptcy',

  props: {
    viewData: {
      type: Object as PropType<ViewData>,
      default: () => ({}),
    },
    dialogProps: {
      type: Object as PropType<DialogProps>,
      default: () => ({}),
    },
  },

  setup() {
    return {};
  },

  render() {
    const { viewData, dialogProps } = this;

    const renderCaseLink = (caseNo?: string) => {
      if (viewData.CaseSearchId && viewData.CaseSearchId.length > 0 && caseNo) {
        return (
          <a
            href={`/embed/courtCaseDetail?caseId=${viewData.CaseSearchId[0]}&title=${caseNo}`}
            target="_blank"
          >
            <span domPropsInnerHTML={caseNo}></span>
          </a>
        );
      }
      return <span domPropsInnerHTML={caseNo || '-'}></span>;
    };

    return (
      <div>
        <QPlainTable>
          <tbody>
            <tr>
              <td class="tb" width="20%">案号</td>
              <td width="30%">{renderCaseLink(viewData.CaseNo)}</td>
              <td class="tb" width="20%">破产类型</td>
              <td width="30%">{viewData.CaseType || '-'}</td>
            </tr>
            <tr>
              <td class="tb" width="20%">被申请人</td>
              <td width="30%">
                {viewData.RespondentNameAndKeyNo ? (
                  <QEntityLink coyArr={viewData.RespondentNameAndKeyNo} />
                ) : (
                  <span>{viewData.Respondent || '-'}</span>
                )}
              </td>
              <td class="tb" width="20%">申请人</td>
              <td width="30%">
                {viewData.ApplicantNameAndKeyNo ? (
                  <QEntityLink coyArr={viewData.ApplicantNameAndKeyNo} />
                ) : (
                  <span>{viewData.Applicant || '-'}</span>
                )}
              </td>
            </tr>
            <tr>
              <td class="tb" width="20%">管理人机构</td>
              <td width="30%">{viewData.ManagementOrganization || '-'}</td>
              <td class="tb" width="20%">管理人主要负责人</td>
              <td width="30%">{viewData.ResponsiblePerson || '-'}</td>
            </tr>
            <tr>
              <td class="tb" width="20%">经办法院</td>
              <td width="30%">{viewData.CourtName || '-'}</td>
              <td class="tb" width="20%">公开日期</td>
              <td width="30%">{dateFormat(viewData.RiskDate)}</td>
            </tr>
          </tbody>
        </QPlainTable>

        {(dialogProps.ccxsCount || dialogProps.keyNo) && (
          <QCcxs ccxsCount={dialogProps.ccxsCount} keyNo={dialogProps.keyNo} />
        )}

        <QBankruptcyRelat searchParams={viewData} />
        <QRelateCases searchParams={viewData} />
      </div>
    );
  },
});

export default Bankruptcy;
