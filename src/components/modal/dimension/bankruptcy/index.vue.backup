<template>
  <div>
    <q-plain-table>
      <tbody>
        <tr>
          <td width="23%" class="tb">案号</td>
          <td width="27%" v-if="viewData.CaseSearchId && viewData.CaseSearchId.length !== 0">
            <a rel="nofollow" :href="`/embed/courtCaseDetail?caseId=${viewData.CaseSearchId[0]}&title=${viewData.CaseNo}`" target="_blank">
              <span v-html="viewData.CaseNo || '-'"></span>
            </a>
          </td>
          <td width="27%" v-else>{{ viewData.CaseNo || '-' }}</td>
          <td width="23%" class="tb">破产类型</td>
          <td width="27%">{{ viewData.CaseType || '-' }}</td>
        </tr>
        <tr>
          <td width="23%" class="tb">被申请人</td>
          <td width="27%">
            <q-entity-link v-if="viewData.Respondent" :coy-arr="viewData.RespondentNameAndKeyNo"></q-entity-link>
            <q-ccxs
              v-if="dialogProps.ccxsCount || dialogProps.keyNo"
              :ccxs-count="dialogProps.ccxsCount"
              :key-no="dialogProps.keyNo"
            ></q-ccxs>
          </td>
          <td width="23%" class="tb">申请人</td>
          <td width="27%">
            <q-entity-link v-if="viewData.Applicant" :coy-arr="viewData.ApplicantNameAndKeyNo"></q-entity-link>
          </td>
        </tr>
        <tr>
          <td width="23%" class="tb">管理人机构</td>
          <td width="27%">{{ viewData.ManagementOrganization || '-' }}</td>
          <td width="23%" class="tb">管理人主要负责人</td>
          <td width="27%">{{ viewData.ResponsiblePerson || '-' }}</td>
        </tr>
        <tr>
          <td width="23%" class="tb">经办法院</td>
          <td width="27%">{{ viewData.CourtName }}</td>
          <td width="23%" class="tb">公开日期</td>
          <td width="27%" v-if="viewData.RiskDate">{{ viewData.RiskDate | dateformat('YYYY-MM-DD') }}</td>
          <td width="27%" v-else>-</td>
        </tr>
      </tbody>
    </q-plain-table>
    <q-bankruptcy-relat :search-params="viewData"></q-bankruptcy-relat>
    <q-relate-cases :search-params="viewData"></q-relate-cases>
  </div>
</template>

<script src="./component.js"></script>
