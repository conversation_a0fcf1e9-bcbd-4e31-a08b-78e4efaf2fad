import { shallowMount } from '@vue/test-utils';

import Bankruptcy from '../index.tsx';

describe('Bankruptcy', () => {
  let wrapper: any;
  let mockOptions: any;

  beforeEach(() => {
    mockOptions = {
      propsData: {
        viewData: {
          CaseNo: 'AN123456',
          CaseType: '破产清算',
          Respondent: '测试公司',
          RespondentNameAndKeyNo: [{ Name: '测试公司', KeyNo: 'c123456' }],
          Applicant: '申请人公司',
          ApplicantNameAndKeyNo: [{ Name: '申请人公司', KeyNo: 'c789012' }],
          ManagementOrganization: '管理人机构',
          ResponsiblePerson: '张三',
          CourtName: '北京市第一中级人民法院',
          RiskDate: '2023-01-01',
          CaseSearchId: ['case123']
        },
        dialogProps: {
          ccxsCount: 5,
          keyNo: 'c123456'
        }
      },
    };
  });

  const createWrapper = (propsData: any = {}, options: any = {}) => {
    const mergedOptions = {
      ...mockOptions,
      propsData: {
        viewData: {
          ...mockOptions.propsData.viewData,
          ...propsData.viewData,
        },
        dialogProps: {
          ...mockOptions.propsData.dialogProps,
          ...propsData.dialogProps,
        },
      },
      ...options,
    };

    wrapper = shallowMount(Bankruptcy, mergedOptions);
    return wrapper;
  };

  afterEach(() => {
    if (wrapper) {
      wrapper.destroy();
    }
  });

  describe('基本渲染', () => {
    test('应该正确渲染基本信息', () => {
      const wrapper = createWrapper();

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('案号');
      expect(wrapper.text()).toContain('破产类型');
      expect(wrapper.text()).toContain('AN123456');
      expect(wrapper.text()).toContain('破产清算');
    });

    test('应该正确显示被申请人和申请人', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('被申请人');
      expect(wrapper.text()).toContain('申请人');
      expect(wrapper.text()).toContain('管理人机构');
      expect(wrapper.text()).toContain('管理人主要负责人');
    });

    test('应该正确显示法院和日期信息', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('经办法院');
      expect(wrapper.text()).toContain('公开日期');
      expect(wrapper.text()).toContain('北京市第一中级人民法院');
    });
  });

  describe('案件链接处理', () => {
    test('当有CaseSearchId时应该显示案件链接', () => {
      const wrapper = createWrapper({
        viewData: {
          CaseSearchId: ['case123'],
          CaseNo: 'AN123456'
        }
      });

      const links = wrapper.findAll('a');
      expect(links.length).toBeGreaterThan(0);
    });

    test('当没有CaseSearchId时应该显示普通文本', () => {
      const wrapper = createWrapper({
        viewData: {
          CaseSearchId: [],
          CaseNo: 'AN123456'
        }
      });

      expect(wrapper.text()).toContain('AN123456');
    });
  });

  describe('实体链接组件', () => {
    test('应该渲染被申请人实体链接', () => {
      const wrapper = createWrapper();

      // 检查是否有实体链接相关内容
      expect(wrapper.html()).toContain('测试公司');
      expect(wrapper.html()).toContain('c123456');
    });

    test('应该渲染申请人实体链接', () => {
      const wrapper = createWrapper({
        viewData: {
          Applicant: '申请人公司',
          ApplicantNameAndKeyNo: [{ Name: '申请人公司', KeyNo: 'c789012' }]
        }
      });

      // 检查是否有实体链接相关内容
      expect(wrapper.html()).toContain('申请人公司');
      expect(wrapper.html()).toContain('c789012');
    });
  });

  describe('CCXS组件', () => {
    test('当有ccxsCount或keyNo时应该显示CCXS组件', () => {
      const wrapper = createWrapper({
        dialogProps: {
          ccxsCount: 5,
          keyNo: 'c123456'
        }
      });

      // 检查是否有财产线索相关内容
      expect(wrapper.html()).toContain('财产线索');
    });

    test('当没有ccxsCount和keyNo时不应该显示CCXS组件', () => {
      const wrapper = createWrapper({
        dialogProps: {
          ccxsCount: null,
          keyNo: null
        }
      });

      // 检查是否没有财产线索相关内容
      expect(wrapper.html()).not.toContain('财产线索');
    });
  });

  describe('日期格式化', () => {
    test('应该正确格式化日期', () => {
      const wrapper = createWrapper({
        viewData: {
          RiskDate: '2023-01-01'
        }
      });

      // 检查日期是否存在（具体格式化由过滤器处理）
      expect(wrapper.vm.viewData.RiskDate).toBe('2023-01-01');
    });

    test('当没有日期时应该显示-', () => {
      const wrapper = createWrapper({
        viewData: {
          RiskDate: null
        }
      });

      expect(wrapper.text()).toContain('-');
    });
  });

  describe('边界情况', () => {
    test('应该处理空的viewData', () => {
      const wrapper = createWrapper({ viewData: {} });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理undefined的字段', () => {
      const wrapper = createWrapper({
        viewData: {
          CaseNo: undefined,
          CaseType: undefined,
          Respondent: undefined,
          Applicant: undefined,
        }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理空的dialogProps', () => {
      const wrapper = createWrapper({ dialogProps: {} });

      expect(wrapper.exists()).toBe(true);
    });
  });

  describe('相关组件', () => {
    test('应该渲染破产相关组件', () => {
      const wrapper = createWrapper();

      // 检查组件是否正常渲染
      expect(wrapper.exists()).toBe(true);
    });

    test('应该渲染相关案件组件', () => {
      const wrapper = createWrapper();

      // 检查组件是否正常渲染
      expect(wrapper.exists()).toBe(true);
    });
  });
});
