import { defineComponent, PropType } from 'vue';

import dimensionMixin from '@/shared/mixins/dimension.mixin';
import { createMixin } from '@/shared/mixins/list.mixin';

import styles from './index.module.less';

interface DetailParams {
  [key: string]: any;
}

const listMixin = createMixin(['current'], {
  rowKey: 'SourceId',
});

const Supplier = defineComponent({
  name: 'Supplier',

  mixins: [dimensionMixin, listMixin],

  props: {
    detailParams: {
      type: Object as PropType<DetailParams>,
      default: () => ({}),
    },
  },

  computed: {
    columns() {
      return [
        { title: '供应商', scopedSlots: { customRender: 'supplier' } },
        {
          title: '采购占比',
          dataIndex: 'Proportion',
          align: 'center',
          customRender: (text: string) => {
            return text ? `${text}%` : '-';
          },
        },
        {
          title: '采购金额(万元)',
          align: 'center',
          dataIndex: 'Quota',
        },
        {
          title: '报告期/公开时间',
          align: 'center',
          customRender: (text: any, record: any) => {
            return record.ReportYear ? `${record.ReportYear}${record.Month ? `-${record.Month}` : ''}` : '-';
          },
        },
        {
          title: '数据来源',
          align: 'center',
          customRender: (text: any, record: any) => {
            if (record.SourceId && record.SourceCode === 6) {
              return <q-link to={`/tender/${record.SourceId}`}>{record.Source || '-'}</q-link>;
            }
            if (record.SourceId && record.SourceCode === 7) {
              const sourceId = record.SourceId.slice(0, record.SourceId.length - 1);
              return <q-link to={`/wenshuDetail/${sourceId}`}>{record.Source || '-'}</q-link>;
            }
            return record.Source || '-';
          },
        },
        {
          title: '关联关系',
          align: 'center',
          dataIndex: 'Relationship',
        },
      ];
    },
  },

  methods: {
    fetchDataSource({ pagination }: { pagination: any }) {
      return this.mDimenGetList(
        {
          dataType: 0,
          ...this.detailParams,
          ...pagination,
        },
        'supplierCustomer'
      );
    },
  },

  render() {
    return (
      <div>
        <div class="q-m-b-sm">
          关联企业：
          <q-entity-link 
            coy-obj={{ 
              Name: this.detailParams.relatedName, 
              KeyNo: this.detailParams.relatedKeyNo 
            }}
          />
        </div>
        <q-rich-table 
          v-data={this.mListGetTableData('current')} 
          columns={this.columns}
          scopedSlots={{
            supplier: (record: any) => (
              <q-td-coy 
                key-no={record.KeyNo} 
                name={record.CompanyName} 
                image={record.ImageUrl}
              />
            ),
            footnote: () => (
              <span>
                企业的供应商和客户数据，是企查查基于公开信息分析生成，仅供参考，并不代表企查查任何明示、暗示之观点或保证。
              </span>
            )
          }}
        />
      </div>
    );
  },
});

export default Supplier;
