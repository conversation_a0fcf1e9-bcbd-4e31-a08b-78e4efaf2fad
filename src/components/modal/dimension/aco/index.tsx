import { defineComponent, PropType } from 'vue';
import { dateFormat } from '@/utils/format';
import QPlainTable from '@/components/global/q-plain-table';

interface ViewData {
  DocumentNo?: string;
  DocumentName?: string;
  PermissionNo?: string;
  CertificateName?: string;
  StartDate?: string | number;
  EndDate?: string | number;
  DecideDate?: string | number;
  PermissionType?: string;
  PermissionGov?: string;
  CreditCodeGov?: string;
  SourceUnit?: string;
  CreditCodeUnit?: string;
  PermissionContent?: string;
}

const AcoDimension = defineComponent({
  name: 'AcoDimension',
  props: {
    viewData: {
      type: Object as PropType<ViewData>,
      default: () => ({}),
    },
  },
  setup() {
    const formatDate = (date: string | number | undefined, options?: any) => {
      return dateFormat(date, options);
    };

    return {
      formatDate,
    };
  },
  render() {
    return (
      <QPlainTable>
        <tbody>
          <tr>
            <th width="23%">行政许可决定文书号</th>
            <td width="27%">{this.viewData.DocumentNo || '-'}</td>
            <th width="23%">行政许可决定文书名称</th>
            <td width="27%">{this.viewData.DocumentName || '-'}</td>
          </tr>
          <tr>
            <th width="23%">许可编号</th>
            <td width="27%" domPropsInnerHTML={this.viewData.PermissionNo || '-'}></td>
            <th width="23%">许可证书名称</th>
            <td width="27%" domPropsInnerHTML={this.viewData.CertificateName || '-'}></td>
          </tr>
          <tr>
            <th width="23%">有效期自</th>
            <td width="27%">{this.formatDate(this.viewData.StartDate)}</td>
            <th width="23%">有效期至</th>
            <td width="27%">{this.formatDate(this.viewData.EndDate, { pattern: 'YYYY-MM-DD', defaultVal: '-', x1000: true })}</td>
          </tr>
          <tr>
            <th width="23%">许可决定日期</th>
            <td width="27%">{this.formatDate(this.viewData.DecideDate)}</td>
            <th width="23%">许可类别</th>
            <td width="27%" domPropsInnerHTML={this.viewData.PermissionType || '-'}></td>
          </tr>
          <tr>
            <th width="23%">许可机关</th>
            <td width="27%" domPropsInnerHTML={this.viewData.PermissionGov || '-'}></td>
            <th width="23%">许可机关统一社会信用代码</th>
            <td width="27%">{this.viewData.CreditCodeGov || '-'}</td>
          </tr>
          <tr>
            <th width="23%">数据来源单位</th>
            <td width="27%" domPropsInnerHTML={this.viewData.SourceUnit || '-'}></td>
            <th width="23%">数据来源单位统一社会信用代码</th>
            <td width="27%">{this.viewData.CreditCodeUnit || '-'}</td>
          </tr>
          <tr>
            <th>许可内容</th>
            <td colspan="5" domPropsInnerHTML={this.viewData.PermissionContent || '-'}></td>
          </tr>
        </tbody>
      </QPlainTable>
    );
  },
});

export default AcoDimension;
