import { shallowMount, mount } from '@vue/test-utils';
import { vi } from 'vitest';
import AcoDimensionDetail from '..';
import { dateFormat } from '@/utils/format';
import QPlainTable from '@/components/global/q-plain-table';

vi.mock('@/utils/format', () => ({
  dateFormat: vi.fn((date, options) => {
    if (!date) return '-';
    if (options?.defaultVal && (!date || date === '0')) return options.defaultVal;
    return '2023-01-01'; // Mock formatted date
  }),
}));

describe('AcoDimensionDetail', () => {
  const mockViewData = {
    DocumentNo: 'DOC123456',
    DocumentName: '行政许可决定书',
    PermissionNo: 'PERM789',
    CertificateName: '许可证书',
    StartDate: '1672531200', // 2023-01-01 timestamp
    EndDate: '1704067200', // 2024-01-01 timestamp
    DecideDate: '1672531200',
    PermissionType: '建筑施工',
    PermissionGov: '市建设局',
    CreditCodeGov: '12345678901234567X',
    SourceUnit: '数据来源单位',
    CreditCodeUnit: '98765432109876543Y',
    PermissionContent: '允许进行建筑施工活动',
  };

  describe('基础渲染', () => {
    it('应该正确渲染组件', () => {
      const wrapper = shallowMount(AcoDimensionDetail, {
        propsData: {
          viewData: {},
        },
      });
      expect(wrapper.exists()).toBe(true);
      expect(wrapper.find(QPlainTable).exists()).toBe(true);
    });

    it('应该渲染正确的表格结构', () => {
      const wrapper = mount(AcoDimensionDetail, {
        propsData: {
          viewData: mockViewData,
        },
      });

      const rows = wrapper.findAll('tr');
      expect(rows).toHaveLength(7); // 7行数据

      // 检查表头
      const headers = wrapper.findAll('th');
      expect(headers).toHaveLength(13); // 6行 * 2列 + 1行单列 = 13个表头
      expect(headers.at(0).text()).toBe('行政许可决定文书号');
      expect(headers.at(1).text()).toBe('行政许可决定文书名称');
    });
  });

  describe('数据显示', () => {
    it('应该正确显示所有字段数据', async () => {
      const wrapper = mount(AcoDimensionDetail, {
        propsData: {
          viewData: mockViewData,
        },
      });
      // 检查文本字段
      expect(wrapper.text()).toContain('DOC123456');
      expect(wrapper.text()).toContain('行政许可决定书');
      expect(wrapper.text()).toContain('市建设局');
      expect(wrapper.text()).toContain('12345678901234567X');
      expect(wrapper.text()).toContain('数据来源单位');
      expect(wrapper.text()).toContain('98765432109876543Y');
      expect(wrapper).toMatchSnapshot();
    });

    it('应该正确处理HTML内容字段', () => {
      const htmlData = {
        PermissionNo: '<strong>PERM789</strong>',
        CertificateName: '<em>许可证书</em>',
        PermissionType: '<span>建筑施工</span>',
        PermissionGov: '<div>市建设局</div>',
        SourceUnit: '<p>数据来源单位</p>',
        PermissionContent: '<div>允许进行建筑施工活动</div>',
      };

      const wrapper = mount(AcoDimensionDetail, {
        propsData: {
          viewData: htmlData,
        },
      });

      // 检查HTML内容是否正确渲染
      const cells = wrapper.findAll('td');
      let hasStrongTag = false;
      let hasEmTag = false;
      let hasSpanTag = false;

      for (let i = 0; i < cells.length; i++) {
        const cellHtml = cells.at(i).html();
        if (cellHtml.includes('<strong>PERM789</strong>')) hasStrongTag = true;
        if (cellHtml.includes('<em>许可证书</em>')) hasEmTag = true;
        if (cellHtml.includes('<span>建筑施工</span>')) hasSpanTag = true;
      }

      expect(hasStrongTag).toBe(true);
      expect(hasEmTag).toBe(true);
      expect(hasSpanTag).toBe(true);
      expect(wrapper).toMatchSnapshot();
    });

    it('应该在数据为空时显示默认值', () => {
      const wrapper = mount(AcoDimensionDetail, {
        propsData: {
          viewData: {},
        },
      });

      const cells = wrapper.findAll('td');
      // 大部分空字段应该显示 '-'
      let dashCount = 0;
      for (let i = 0; i < cells.length; i++) {
        if (cells.at(i).text().trim() === '-') {
          dashCount++;
        }
      }
      expect(dashCount).toBeGreaterThan(0);
    });
  });

  describe('日期格式化', () => {
    it('应该调用dateFormat方法格式化日期', () => {
      const mockedDateFormat = vi.mocked(dateFormat);

      mount(AcoDimensionDetail, {
        propsData: {
          viewData: mockViewData,
        },
      });

      // 验证dateFormat被调用
      expect(mockedDateFormat).toHaveBeenCalled();
    });

    it('应该为EndDate传递正确的格式化选项', () => {
      const mockedDateFormat = vi.mocked(dateFormat);

      mount(AcoDimensionDetail, {
        propsData: {
          viewData: mockViewData,
        },
      });

      // 验证EndDate的特殊格式化选项
      expect(mockedDateFormat).toHaveBeenCalledWith(mockViewData.EndDate, { pattern: 'YYYY-MM-DD', defaultVal: '-', x1000: true });
    });

    it('应该处理空日期值', () => {
      const wrapper = mount(AcoDimensionDetail, {
        propsData: {
          viewData: {
            StartDate: null,
            EndDate: '',
            DecideDate: undefined,
          },
        },
      });

      expect(wrapper.exists()).toBe(true);
      // 空日期应该显示为 '-'
      expect(wrapper.text()).toContain('-');
    });
  });

  describe('组件方法', () => {
    it('应该正确调用dateFormat方法', () => {
      const wrapper = mount(AcoDimensionDetail, {
        propsData: {
          viewData: mockViewData,
        },
      });

      const vm = wrapper.vm as any;
      const result = vm.formatDate('1672531200');
      expect(result).toBe('2023-01-01');
    });

    it('应该正确传递options参数给dateFormat', () => {
      const wrapper = mount(AcoDimensionDetail, {
        propsData: {
          viewData: mockViewData,
        },
      });

      const vm = wrapper.vm as any;
      const options = { pattern: 'YYYY-MM-DD', defaultVal: '-', x1000: true };
      vm.formatDate('1672531200', options);

      const mockedDateFormat = vi.mocked(dateFormat);
      expect(mockedDateFormat).toHaveBeenCalledWith('1672531200', options);
    });
  });

  describe('Props验证', () => {
    it('应该接受viewData prop', () => {
      const wrapper = mount(AcoDimensionDetail, {
        propsData: {
          viewData: mockViewData,
        },
      });

      expect(wrapper.props('viewData')).toEqual(mockViewData);
    });

    it('应该为viewData提供默认值', () => {
      const wrapper = mount(AcoDimensionDetail);
      expect(wrapper.props('viewData')).toEqual({});
    });
  });
});
