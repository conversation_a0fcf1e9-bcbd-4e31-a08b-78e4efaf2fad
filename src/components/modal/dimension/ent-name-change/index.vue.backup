<template>
  <table class="ntable">
    <tr>
      <td width="20%" class="tb">变更企业：</td>
      <td width="30%">
        <q-entity-link :coy-obj="{ Name: viewData.Name, KeyNo: viewData.KeyNo }"></q-entity-link>
      </td>
      <td width="20%" class="tb">变更日期：</td>
      <td width="30%">{{ viewData.ChangeDate }}</td>
    </tr>
    <tr>
      <td width="20%" class="tb">变更前名称：</td>
      <td colspan="3">
        {{ viewData.BeforeContent || '-' }}
      </td>
    </tr>
  </table>
</template>

<script>
export default {
  name: 'entNameDetail',
  props: ['viewData'],
};
</script>
