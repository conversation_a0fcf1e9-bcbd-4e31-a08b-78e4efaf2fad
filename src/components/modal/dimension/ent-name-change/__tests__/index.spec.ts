import { shallowMount } from '@vue/test-utils';

import EntNameChange from '../index.tsx';

describe('EntNameChange', () => {
  let wrapper: any;
  let mockOptions: any;

  beforeEach(() => {
    mockOptions = {
      propsData: {
        viewData: {
          KeyNo: 'KEY123',
          Name: '测试企业有限公司',
          ChangeDate: '2023-01-01',
          BeforeContent: '原测试企业有限公司'
        },
      },
      stubs: {
        'q-entity-link': true
      }
    };
  });

  const createWrapper = (propsData: any = {}, options: any = {}) => {
    const mergedOptions = {
      ...mockOptions,
      propsData: {
        viewData: {
          ...mockOptions.propsData.viewData,
          ...propsData.viewData,
        },
      },
      ...options,
    };

    wrapper = shallowMount(EntNameChange, mergedOptions);
    return wrapper;
  };

  afterEach(() => {
    if (wrapper) {
      wrapper.destroy();
    }
  });

  describe('基本信息渲染', () => {
    test('应该正确渲染变更企业信息', () => {
      const wrapper = createWrapper();

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('变更企业');
      expect(wrapper.find('q-entity-link-stub').exists()).toBe(true);
    });

    test('应该正确显示变更日期', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('变更日期');
      expect(wrapper.text()).toContain('2023-01-01');
    });

    test('应该正确显示变更前名称', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('变更前名称');
      expect(wrapper.text()).toContain('原测试企业有限公司');
    });
  });

  describe('实体链接处理', () => {
    test('应该显示实体链接', () => {
      const wrapper = createWrapper();

      expect(wrapper.find('q-entity-link-stub').exists()).toBe(true);
      expect(wrapper.find('q-entity-link-stub').attributes('coy-obj')).toBeDefined();
    });

    test('应该传递正确的企业信息给实体链接', () => {
      const wrapper = createWrapper();

      const entityLink = wrapper.find('q-entity-link-stub');
      expect(entityLink.exists()).toBe(true);

      // 检查传递给实体链接的属性
      const coyObj = entityLink.attributes('coy-obj');
      expect(coyObj).toBeDefined();
    });
  });

  describe('边界情况', () => {
    test('应该处理空的viewData', () => {
      const wrapper = createWrapper({ viewData: {} });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理undefined的字段', () => {
      const wrapper = createWrapper({
        viewData: {
          KeyNo: undefined,
          Name: undefined,
          ChangeDate: undefined,
          BeforeContent: undefined
        }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理null值字段', () => {
      const wrapper = createWrapper({
        viewData: {
          KeyNo: null,
          Name: null,
          ChangeDate: null,
          BeforeContent: null
        }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理空字符串', () => {
      const wrapper = createWrapper({
        viewData: {
          KeyNo: '',
          Name: '',
          ChangeDate: '',
          BeforeContent: ''
        }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理部分字段为空的情况', () => {
      const wrapper = createWrapper({
        viewData: {
          KeyNo: 'KEY123',
          Name: '测试企业',
          ChangeDate: '',
          BeforeContent: '原企业名称'
        }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('原企业名称');
    });
  });

  describe('表格结构', () => {
    test('应该渲染正确的表格结构', () => {
      const wrapper = createWrapper();

      const table = wrapper.find('table');
      expect(table.exists()).toBe(true);
      expect(table.classes()).toContain('ntable');

      const rows = wrapper.findAll('tr');
      expect(rows.length).toBe(2);
    });

    test('应该有正确的列宽设置', () => {
      const wrapper = createWrapper();

      const cells20 = wrapper.findAll('td[width="20%"]');
      expect(cells20.length).toBe(3);

      const cells30 = wrapper.findAll('td[width="30%"]');
      expect(cells30.length).toBe(2);
    });

    test('应该有正确的样式类', () => {
      const wrapper = createWrapper();

      const tbCells = wrapper.findAll('td.tb');
      expect(tbCells.length).toBe(3);
    });

    test('应该有正确的colspan设置', () => {
      const wrapper = createWrapper();

      const colspanCells = wrapper.findAll('td[colspan="3"]');
      expect(colspanCells.length).toBe(1);
    });
  });

  describe('组件属性', () => {
    test('应该正确接收viewData属性', () => {
      const wrapper = createWrapper();

      expect(wrapper.vm.viewData).toEqual(mockOptions.propsData.viewData);
    });

    test('应该有默认的viewData', () => {
      // TSX组件有默认值，不会报错
      const wrapper = shallowMount(EntNameChange);
      expect(wrapper.vm.viewData).toEqual({});
    });
  });

  describe('数据显示', () => {
    test('应该正确显示所有字段', () => {
      const testData = {
        KeyNo: 'TEST123',
        Name: '测试公司',
        ChangeDate: '2023-12-25',
        BeforeContent: '原测试公司名称'
      };

      const wrapper = createWrapper({ viewData: testData });

      expect(wrapper.text()).toContain('2023-12-25');
      expect(wrapper.text()).toContain('原测试公司名称');
    });

    test('应该正确处理BeforeContent为空的情况', () => {
      const testData = {
        KeyNo: 'TEST123',
        Name: '测试公司',
        ChangeDate: '2023-12-25',
        BeforeContent: ''
      };

      const wrapper = createWrapper({ viewData: testData });

      expect(wrapper.text()).toContain('2023-12-25');
      expect(wrapper.text()).toContain('-');
    });

    test('应该正确处理BeforeContent为null的情况', () => {
      const testData = {
        KeyNo: 'TEST123',
        Name: '测试公司',
        ChangeDate: '2023-12-25',
        BeforeContent: null
      };

      const wrapper = createWrapper({ viewData: testData });

      expect(wrapper.text()).toContain('2023-12-25');
      expect(wrapper.text()).toContain('-');
    });
  });

  describe('企业名称变更特殊情况', () => {
    test('应该正确显示长企业名称', () => {
      const longName = '北京市海淀区中关村科技发展有限责任公司';
      const wrapper = createWrapper({
        viewData: {
          KeyNo: 'KEY123',
          Name: '新企业名称',
          ChangeDate: '2023-01-01',
          BeforeContent: longName
        }
      });

      expect(wrapper.text()).toContain(longName);
    });

    test('应该正确处理特殊字符', () => {
      const specialName = '测试企业（集团）有限公司';
      const wrapper = createWrapper({
        viewData: {
          KeyNo: 'KEY123',
          Name: '新企业名称',
          ChangeDate: '2023-01-01',
          BeforeContent: specialName
        }
      });

      expect(wrapper.text()).toContain(specialName);
    });

    test('应该正确处理英文企业名称', () => {
      const englishName = 'Test Company Limited';
      const wrapper = createWrapper({
        viewData: {
          KeyNo: 'KEY123',
          Name: '新企业名称',
          ChangeDate: '2023-01-01',
          BeforeContent: englishName
        }
      });

      expect(wrapper.text()).toContain(englishName);
    });
  });

  describe('组件名称', () => {
    test('应该有正确的组件名称', () => {
      const wrapper = createWrapper();

      expect(wrapper.vm.$options.name).toBe('entNameDetail');
    });
  });
});
