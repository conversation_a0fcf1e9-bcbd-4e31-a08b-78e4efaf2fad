import { defineComponent, PropType } from 'vue';

import styles from './index.module.less';

interface ViewData {
  KeyNo?: string;
  Name?: string;
  ChangeDate?: string;
  BeforeContent?: string;
}

const EntNameChange = defineComponent({
  name: 'entNameDetail',

  props: {
    viewData: {
      type: Object as PropType<ViewData>,
      default: () => ({}),
    },
  },

  render() {
    const { viewData } = this;

    return (
      <table class={styles.ntable}>
        <tr>
          <td width="20%" class={styles.tb}>变更企业：</td>
          <td width="30%">
            <q-entity-link coy-obj={{ Name: viewData.Name, KeyNo: viewData.KeyNo }} />
          </td>
          <td width="20%" class={styles.tb}>变更日期：</td>
          <td width="30%">{viewData.ChangeDate}</td>
        </tr>
        <tr>
          <td width="20%" class={styles.tb}>变更前名称：</td>
          <td colspan="3">
            {viewData.BeforeContent || '-'}
          </td>
        </tr>
      </table>
    );
  },
});

export default EntNameChange;
