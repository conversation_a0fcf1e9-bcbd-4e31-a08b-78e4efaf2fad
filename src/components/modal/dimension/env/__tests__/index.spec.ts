import { shallowMount } from '@vue/test-utils';

import Env from '../index.tsx';

describe('Env', () => {
  let wrapper: any;
  let mockOptions: any;

  beforeEach(() => {
    mockOptions = {
      propsData: {
        viewData: {
          CaseNo: 'ENV2023001',
          CompanyNameAndKeyNo: [{ Name: '测试企业有限公司', KeyNo: 'KEY123' }],
          PunishReason: '违反环保法规',
          IllegalType: '废水超标排放',
          PunishBasis: '环境保护法第六十条',
          PunishmentResult: '罚款10万元',
          PunishGov: '北京市环保局',
          PunishDate: '2023-01-01',
          Implementation: '已执行'
        },
      },
      stubs: {
        'q-entity-link': true,
        'q-relate-cases': true
      },
      filters: {
        dateformat: (value: string) => value
      }
    };
  });

  const createWrapper = (propsData: any = {}, options: any = {}) => {
    const mergedOptions = {
      ...mockOptions,
      propsData: {
        viewData: {
          ...mockOptions.propsData.viewData,
          ...propsData.viewData,
        },
      },
      ...options,
    };

    wrapper = shallowMount(Env, mergedOptions);
    return wrapper;
  };

  afterEach(() => {
    if (wrapper) {
      wrapper.destroy();
    }
  });

  describe('基本信息渲染', () => {
    test('应该正确渲染决定书文号', () => {
      const wrapper = createWrapper();

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('决定书文号');
      expect(wrapper.text()).toContain('ENV2023001');
    });

    test('应该正确显示行政相对人名称', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('行政相对人名称');
      expect(wrapper.find('q-entity-link-stub').exists()).toBe(true);
    });

    test('应该正确显示处罚事由', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('处罚事由');
      expect(wrapper.text()).toContain('违反环保法规');
    });

    test('应该正确显示违法类型', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('违法类型');
      expect(wrapper.text()).toContain('废水超标排放');
    });

    test('应该正确显示处罚依据', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('处罚依据');
      expect(wrapper.text()).toContain('环境保护法第六十条');
    });

    test('应该正确显示处罚结果', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('处罚结果');
      expect(wrapper.text()).toContain('罚款10万元');
    });

    test('应该正确显示处罚单位', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('处罚单位');
      expect(wrapper.text()).toContain('北京市环保局');
    });

    test('应该正确显示处罚日期', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('处罚日期');
      expect(wrapper.text()).toContain('2023-01-01');
    });

    test('应该正确显示执行情况', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('执行情况');
      expect(wrapper.text()).toContain('已执行');
    });
  });

  describe('实体链接处理', () => {
    test('应该显示实体链接', () => {
      const wrapper = createWrapper();

      expect(wrapper.find('q-entity-link-stub').exists()).toBe(true);
      expect(wrapper.find('q-entity-link-stub').attributes('coy-arr')).toBeDefined();
      expect(wrapper.find('q-entity-link-stub').attributes('target')).toBe('_blank');
    });

    test('当没有CompanyNameAndKeyNo时应该正常处理', () => {
      const wrapper = createWrapper({
        viewData: {
          CompanyNameAndKeyNo: null
        }
      });

      expect(wrapper.find('q-entity-link-stub').exists()).toBe(true);
    });
  });

  describe('HTML内容渲染', () => {
    test('应该正确渲染处罚事由的HTML内容', () => {
      const wrapper = createWrapper({
        viewData: {
          PunishReason: '<strong>严重违反环保法规</strong>'
        }
      });

      const punishReasonCell = wrapper.findAll('td').filter((cell: any) =>
        cell.attributes('colspan') === '3'
      );
      expect(punishReasonCell.length).toBeGreaterThan(0);
    });

    test('应该正确渲染处罚结果的HTML内容', () => {
      const wrapper = createWrapper({
        viewData: {
          PunishmentResult: '<em>罚款20万元</em>'
        }
      });

      const spans = wrapper.findAll('span');
      expect(spans.length).toBeGreaterThan(0);
    });
  });

  describe('边界情况', () => {
    test('应该处理空的viewData', () => {
      const wrapper = createWrapper({ viewData: {} });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理undefined的字段', () => {
      const wrapper = createWrapper({
        viewData: {
          CaseNo: undefined,
          CompanyNameAndKeyNo: undefined,
          PunishReason: undefined,
          IllegalType: undefined,
          PunishBasis: undefined,
          PunishmentResult: undefined,
          PunishGov: undefined,
          PunishDate: undefined,
          Implementation: undefined
        }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理null值字段', () => {
      const wrapper = createWrapper({
        viewData: {
          CaseNo: null,
          CompanyNameAndKeyNo: null,
          PunishReason: null,
          IllegalType: null,
          PunishBasis: null,
          PunishmentResult: null,
          PunishGov: null,
          PunishDate: null,
          Implementation: null
        }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理空字符串', () => {
      const wrapper = createWrapper({
        viewData: {
          CaseNo: '',
          CompanyNameAndKeyNo: [],
          PunishReason: '',
          IllegalType: '',
          PunishBasis: '',
          PunishmentResult: '',
          PunishGov: '',
          PunishDate: '',
          Implementation: ''
        }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });
  });

  describe('表格结构', () => {
    test('应该渲染正确的表格结构', () => {
      const wrapper = createWrapper();

      const table = wrapper.find('table');
      expect(table.exists()).toBe(true);
      expect(table.classes()).toContain('ntable');

      const rows = wrapper.findAll('tr');
      expect(rows.length).toBe(5);
    });

    test('应该有正确的列宽设置', () => {
      const wrapper = createWrapper();

      const cells20 = wrapper.findAll('td[width="20%"]');
      expect(cells20.length).toBeGreaterThan(0);

      const cells30 = wrapper.findAll('td[width="30%"]');
      expect(cells30.length).toBeGreaterThan(0);
    });

    test('应该有正确的样式类', () => {
      const wrapper = createWrapper();

      const tbCells = wrapper.findAll('td.tb');
      expect(tbCells.length).toBeGreaterThan(0);
    });

    test('应该有正确的colspan设置', () => {
      const wrapper = createWrapper();

      const colspanCells = wrapper.findAll('td[colspan="3"]');
      expect(colspanCells.length).toBe(1);
    });
  });

  describe('组件属性', () => {
    test('应该正确接收viewData属性', () => {
      const wrapper = createWrapper();

      expect(wrapper.vm.viewData).toEqual(mockOptions.propsData.viewData);
    });

    test('应该有默认的viewData', () => {
      const wrapper = shallowMount(Env);

      expect(wrapper.vm.viewData).toEqual({});
    });
  });

  describe('关联案例组件', () => {
    test('应该渲染关联案例组件', () => {
      const wrapper = createWrapper();

      expect(wrapper.find('q-relate-cases-stub').exists()).toBe(true);
      expect(wrapper.find('q-relate-cases-stub').attributes('search-params')).toBeDefined();
      expect(wrapper.find('q-relate-cases-stub').attributes('stitle')).toBe('关联');
    });
  });

  describe('日期格式化', () => {
    test('当有处罚日期时应该显示格式化后的日期', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('2023-01-01');
    });

    test('当没有处罚日期时应该显示横线', () => {
      const wrapper = createWrapper({
        viewData: {
          PunishDate: null
        }
      });

      // 检查处罚日期对应的单元格
      const rows = wrapper.findAll('tr');
      const dateRow = rows.at(4); // 第5行是处罚日期行
      expect(dateRow.text()).toContain('-');
    });

    test('当处罚日期为空字符串时应该显示横线', () => {
      const wrapper = createWrapper({
        viewData: {
          PunishDate: ''
        }
      });

      const rows = wrapper.findAll('tr');
      const dateRow = rows.at(4);
      expect(dateRow.text()).toContain('-');
    });
  });

  describe('条件渲染', () => {
    test('当有PunishDate时应该渲染日期单元格', () => {
      const wrapper = createWrapper();

      const dateCells = wrapper.findAll('td').filter((cell: any) =>
        cell.text().includes('2023-01-01')
      );
      expect(dateCells.length).toBeGreaterThan(0);
    });

    test('当没有PunishDate时应该渲染横线单元格', () => {
      const wrapper = createWrapper({
        viewData: {
          PunishDate: null
        }
      });

      // v-else 会渲染一个包含'-'的td
      const dashCells = wrapper.findAll('td').filter((cell: any) =>
        cell.text() === '-'
      );
      expect(dashCells.length).toBeGreaterThan(0);
    });
  });

  describe('数据显示', () => {
    test('应该正确显示所有字段', () => {
      const testData = {
        CaseNo: 'TEST001',
        CompanyNameAndKeyNo: [{ Name: '测试公司', KeyNo: 'TEST123' }],
        PunishReason: '测试处罚事由',
        IllegalType: '测试违法类型',
        PunishBasis: '测试处罚依据',
        PunishmentResult: '测试处罚结果',
        PunishGov: '测试处罚单位',
        PunishDate: '2023-12-25',
        Implementation: '测试执行情况'
      };

      const wrapper = createWrapper({ viewData: testData });

      expect(wrapper.text()).toContain('TEST001');
      expect(wrapper.text()).toContain('测试处罚事由');
      expect(wrapper.text()).toContain('测试违法类型');
      expect(wrapper.text()).toContain('测试处罚依据');
      expect(wrapper.text()).toContain('测试处罚结果');
      expect(wrapper.text()).toContain('测试处罚单位');
      expect(wrapper.text()).toContain('2023-12-25');
      expect(wrapper.text()).toContain('测试执行情况');
    });
  });
});
