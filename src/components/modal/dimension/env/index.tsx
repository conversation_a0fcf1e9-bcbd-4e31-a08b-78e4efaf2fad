import { defineComponent, PropType } from 'vue';

import formatDate from '@/utils/format/date';

import styles from './index.module.less';

interface CompanyInfo {
  Name?: string;
  KeyNo?: string;
}

interface ViewData {
  CaseNo?: string;
  CompanyNameAndKeyNo?: CompanyInfo[];
  PunishReason?: string;
  IllegalType?: string;
  PunishBasis?: string;
  PunishmentResult?: string;
  PunishGov?: string;
  PunishDate?: string;
  Implementation?: string;
}

const Env = defineComponent({
  name: 'Env',

  props: {
    viewData: {
      type: Object as PropType<ViewData>,
      default: () => ({}),
    },
  },

  setup(props) {
    const dateformat = (date: string | undefined) => {
      return formatDate(date, { pattern: 'YYYY-MM-DD', defaultVal: '-' });
    };

    return {
      dateformat,
    };
  },

  render() {
    const { viewData, dateformat } = this;

    return (
      <div>
        <table class={styles.ntable}>
          <tr>
            <td class={styles.tb} width="20%">决定书文号</td>
            <td width="30%">{viewData.CaseNo || '-'}</td>
            <td class={styles.tb} width="20%">行政相对人名称</td>
            <td width="30%">
              <q-entity-link coy-arr={viewData.CompanyNameAndKeyNo} target="_blank" />
            </td>
          </tr>
          <tr>
            <td class={styles.tb}>处罚事由</td>
            <td colspan="3" domPropsInnerHTML={viewData.PunishReason || '-'}></td>
          </tr>
          <tr>
            <td class={styles.tb} width="20%">违法类型</td>
            <td width="30%">{viewData.IllegalType || '-'}</td>
            <td class={styles.tb} width="20%">处罚依据</td>
            <td width="30%">{viewData.PunishBasis || '-'}</td>
          </tr>
          <tr>
            <td class={styles.tb} width="20%">处罚结果</td>
            <td width="30%">
              <span domPropsInnerHTML={viewData.PunishmentResult || '-'}></span>
            </td>
            <td class={styles.tb} width="20%">处罚单位</td>
            <td width="30%">{viewData.PunishGov || '-'}</td>
          </tr>
          <tr>
            <td class={styles.tb} width="20%">处罚日期</td>
            {viewData.PunishDate ? (
              <td width="30%">{dateformat(viewData.PunishDate)}</td>
            ) : (
              <td>-</td>
            )}
            <td class={styles.tb} width="20%">执行情况</td>
            <td width="30%">{viewData.Implementation || '-'}</td>
          </tr>
        </table>
        <q-relate-cases search-params={viewData} stitle="关联" />
      </div>
    );
  },
});

export default Env;
