import { defineComponent, PropType } from 'vue';
import { Icon, Tooltip } from 'ant-design-vue';
import { isNil } from 'lodash';

import { numberToHuman } from '@/utils/number-formatter';
import formatDate from '@/utils/format/date';

import styles from './index.module.less';

interface EntityObject {
  KeyNo?: string;
  Name?: string;
}

interface ViewData {
  NameKeyNoCollection?: EntityObject[];
  Name?: string;
  Orgno?: string;
  SqrInfo?: EntityObject[];
  Amount?: string | number;
  Executegov?: string;
  Executestatus?: string;
  Province?: string;
  Executeno?: string;
  Liandate?: string;
  Anno?: string;
  CaseSearchId?: string[];
  Executeunite?: string;
  Publicdate?: string;
  Actionremark?: string;
  Yiwu?: string;
}

interface DialogProps {
  ccxsCount?: number;
  keyNo?: string;
}

const Shixin = defineComponent({
  name: 'Shixin',

  components: {
    Icon,
    Tooltip,
  },

  props: {
    viewData: {
      type: Object as PropType<ViewData>,
      default: () => ({}),
    },
    dialogProps: {
      type: Object as PropType<DialogProps>,
      default: () => ({}),
    },
  },

  methods: {
    formatAmount(val: string | number | null | undefined): string {
      if (isNil(val) || val === '') {
        return '-';
      }
      return numberToHuman(Number(val), { precision: 2 });
    },

    dateformat(value: string | null | undefined): string {
      return formatDate(value, { pattern: 'YYYY-MM-DD', defaultVal: '-' });
    },
  },

  render() {
    const { viewData, dialogProps } = this;

    return (
      <div>
        <q-plain-table>
          <tbody>
            <tr>
              <td width="23%" class={styles.tb}>
                失信被执行人
              </td>
              <td>
                {viewData.NameKeyNoCollection ? (
                  <q-entity-link coy-arr={viewData.NameKeyNoCollection} />
                ) : (
                  <span>{viewData.Name || '-'}</span>
                )}
                {(dialogProps.ccxsCount || dialogProps.keyNo) && <q-ccxs ccxs-count={dialogProps.ccxsCount} key-no={dialogProps.keyNo} />}
              </td>
              <td width="23%" class={styles.tb}>
                证件号码/组织机构代码
              </td>
              <td>{viewData.Orgno || '-'}</td>
            </tr>
            <tr>
              <td width="23%" class={styles.tb}>
                疑似申请执行人
                <Tooltip placement="bottom">
                  <div slot="title">该数据由企查查基于公开数据分析得出，仅供参考，不代表企查查任何明示、暗示之观点或保证。</div>
                  <Icon style="color: #d6d6d6" class={styles.icon} type="info-circle" />
                </Tooltip>
              </td>
              <td>{viewData.SqrInfo && viewData.SqrInfo.length ? <q-entity-link coy-obj={viewData.SqrInfo[0]} /> : <span>-</span>}</td>

              <td width="23%" class={styles.tb}>
                涉案金额(元)
                <Tooltip placement="bottom">
                  <div slot="title">该数据由企查查基于公开数据分析得出，仅供参考，不代表企查查任何明示、暗示之观点或保证。</div>
                  <Icon style="color: #d6d6d6" class={styles.icon} type="info-circle" />
                </Tooltip>
              </td>
              <td>{this.formatAmount(viewData.Amount)}</td>
            </tr>
            <tr>
              <td width="23%" class={styles.tb}>
                执行法院
              </td>
              <td>{viewData.Executegov || '-'}</td>
              <td width="23%" class={styles.tb}>
                被执行人的履行情况
              </td>
              <td>{viewData.Executestatus || '-'}</td>
            </tr>
            <tr>
              <td width="23%" class={styles.tb}>
                省份
              </td>
              <td>{viewData.Province || '-'}</td>
              <td width="23%" class={styles.tb}>
                执行依据文号
              </td>
              <td>{viewData.Executeno || '-'}</td>
            </tr>
            <tr>
              <td width="23%" class={styles.tb}>
                立案日期
              </td>
              <td>{this.dateformat(viewData.Liandate)}</td>
              <td width="23%" class={styles.tb}>
                案号
              </td>
              <td>
                {viewData.CaseSearchId && viewData.CaseSearchId.length !== 0 ? (
                  <a
                    class={styles.no}
                    href={`/embed/courtCaseDetail?caseId=${viewData.CaseSearchId[0]}&title=${viewData.Executegov}${viewData.Anno}`}
                    target="_blank"
                  >
                    {viewData.Anno || '-'}
                  </a>
                ) : (
                  <span>{viewData.Anno || '-'}</span>
                )}
              </td>
            </tr>
            <tr>
              <td width="23%" class={styles.tb}>
                做出执行依据单位
              </td>
              <td>{viewData.Executeunite || '-'}</td>
              <td width="23%" class={styles.tb}>
                发布日期
              </td>
              <td>{this.dateformat(viewData.Publicdate)}</td>
            </tr>
            <tr>
              <td class={styles.tb} width="23%">
                失信被执行人行为具体情形
              </td>
              <td colspan="3">{viewData.Actionremark || '-'}</td>
            </tr>
            <tr>
              <td class={styles.tb} width="23%">
                生效法律文书确定的义务
              </td>
              {viewData.Yiwu ? <td colspan="3" v-entity-click domPropsInnerHTML={viewData.Yiwu}></td> : <td colspan="3">-</td>}
            </tr>
          </tbody>
        </q-plain-table>
        <q-relate-cases search-params={viewData} />
      </div>
    );
  },
});

export default Shixin;
