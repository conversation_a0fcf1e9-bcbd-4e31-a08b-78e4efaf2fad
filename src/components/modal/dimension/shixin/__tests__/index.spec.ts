import { shallowMount } from '@vue/test-utils';

import Shixin from '../index.tsx';

describe('Shixin', () => {
  let wrapper: any;
  let mockOptions: any;

  beforeEach(() => {
    mockOptions = {
      propsData: {
        viewData: {
          NameKeyNoCollection: [{ Name: '测试企业有限公司', KeyNo: 'KEY123' }],
          Name: '测试企业有限公司',
          Orgno: '91110000123456789X',
          SqrInfo: [{ Name: '申请执行人', KeyNo: 'SQR123' }],
          Amount: '1000000',
          Executegov: '北京市第一中级人民法院',
          Executestatus: '全部未履行',
          Province: '北京',
          Executeno: '(2023)京01执123号',
          Liandate: '2023-01-01',
          Anno: '(2023)京01民初123号',
          CaseSearchId: ['CASE123'],
          Executeunite: '北京市第一中级人民法院',
          Publicdate: '2023-01-15',
          Actionremark: '有履行能力而拒不履行生效法律文书确定的义务',
          Yiwu: '支付货款<strong>100万元</strong>及利息'
        },
        dialogProps: {
          ccxsCount: 5,
          keyNo: 'KEY123'
        }
      },
      stubs: {
        'q-plain-table': true,
        'q-entity-link': true,
        'q-ccxs': true,
        'q-relate-cases': true,
        'Icon': true,
        'Tooltip': true
      },
      filters: {
        dateformat: (value: string) => value || '-'
      },
      directives: {
        'entity-click': {}
      }
    };
  });

  const createWrapper = (propsData: any = {}, options: any = {}) => {
    const mergedOptions = {
      ...mockOptions,
      propsData: {
        viewData: {
          ...mockOptions.propsData.viewData,
          ...propsData.viewData,
        },
        dialogProps: {
          ...mockOptions.propsData.dialogProps,
          ...propsData.dialogProps,
        },
      },
      ...options,
    };

    wrapper = shallowMount(Shixin, mergedOptions);
    return wrapper;
  };

  afterEach(() => {
    if (wrapper) {
      wrapper.destroy();
    }
  });

  describe('基本信息渲染', () => {
    test('应该正确渲染失信被执行人信息', () => {
      const wrapper = createWrapper();

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('失信被执行人');
      expect(wrapper.find('q-entity-link-stub').exists()).toBe(true);
    });

    test('应该正确显示证件号码', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('证件号码/组织机构代码');
      expect(wrapper.text()).toContain('91110000123456789X');
    });

    test('应该正确显示执行法院', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('执行法院');
      expect(wrapper.text()).toContain('北京市第一中级人民法院');
    });

    test('应该正确显示被执行人的履行情况', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('被执行人的履行情况');
      expect(wrapper.text()).toContain('全部未履行');
    });
  });

  describe('实体链接处理', () => {
    test('当有NameKeyNoCollection时应该显示实体链接', () => {
      const wrapper = createWrapper();

      const entityLinks = wrapper.findAll('q-entity-link-stub');
      expect(entityLinks.length).toBeGreaterThan(0);
    });

    test('当没有NameKeyNoCollection时应该显示Name', () => {
      const wrapper = createWrapper({
        viewData: {
          NameKeyNoCollection: null,
          Name: '测试企业'
        }
      });

      expect(wrapper.text()).toContain('测试企业');
    });

    test('应该显示疑似申请执行人链接', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('疑似申请执行人');
      const entityLinks = wrapper.findAll('q-entity-link-stub');
      expect(entityLinks.length).toBeGreaterThan(1);
    });

    test('当没有SqrInfo时应该显示横线', () => {
      const wrapper = createWrapper({
        viewData: {
          SqrInfo: null
        }
      });

      expect(wrapper.text()).toContain('疑似申请执行人');
      expect(wrapper.text()).toContain('-');
    });
  });

  describe('金额格式化', () => {
    test('应该正确格式化金额', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('涉案金额(元)');
      // 金额会被格式化，具体格式取决于numberToHuman函数
    });

    test('当金额为空时应该显示横线', () => {
      const wrapper = createWrapper({
        viewData: {
          Amount: null
        }
      });

      // 检查金额对应的单元格
      expect(wrapper.text()).toContain('涉案金额(元)');
    });
  });

  describe('日期格式化', () => {
    test('应该正确显示立案日期', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('立案日期');
      expect(wrapper.text()).toContain('2023-01-01');
    });

    test('应该正确显示发布日期', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('发布日期');
      expect(wrapper.text()).toContain('2023-01-15');
    });
  });

  describe('案号链接处理', () => {
    test('当有CaseSearchId时应该显示链接', () => {
      const wrapper = createWrapper();

      const links = wrapper.findAll('a.no');
      expect(links.length).toBe(1);
      expect(links.at(0).attributes('href')).toContain('/embed/courtCaseDetail');
      expect(links.at(0).attributes('target')).toBe('_blank');
    });

    test('当没有CaseSearchId时应该显示普通文本', () => {
      const wrapper = createWrapper({
        viewData: {
          CaseSearchId: null
        }
      });

      const links = wrapper.findAll('a.no');
      expect(links.length).toBe(0);
      expect(wrapper.text()).toContain('(2023)京01民初123号');
    });

    test('当CaseSearchId为空数组时应该显示普通文本', () => {
      const wrapper = createWrapper({
        viewData: {
          CaseSearchId: []
        }
      });

      const links = wrapper.findAll('a.no');
      expect(links.length).toBe(0);
    });
  });

  describe('HTML内容渲染', () => {
    test('应该正确渲染义务的HTML内容', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('生效法律文书确定的义务');
      // HTML内容会被渲染，但在测试中可能不会显示具体的HTML标签
    });

    test('当没有义务内容时应该显示横线', () => {
      const wrapper = createWrapper({
        viewData: {
          Yiwu: null
        }
      });

      expect(wrapper.text()).toContain('生效法律文书确定的义务');
      expect(wrapper.text()).toContain('-');
    });
  });

  describe('边界情况', () => {
    test('应该处理空的viewData', () => {
      const wrapper = createWrapper({ viewData: {} });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理undefined的字段', () => {
      const wrapper = createWrapper({
        viewData: {
          NameKeyNoCollection: undefined,
          Name: undefined,
          Orgno: undefined,
          SqrInfo: undefined,
          Amount: undefined,
          Executegov: undefined,
          Executestatus: undefined,
          Province: undefined,
          Executeno: undefined,
          Liandate: undefined,
          Anno: undefined,
          CaseSearchId: undefined,
          Executeunite: undefined,
          Publicdate: undefined,
          Actionremark: undefined,
          Yiwu: undefined
        }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });
  });

  describe('组件属性', () => {
    test('应该正确接收viewData属性', () => {
      const wrapper = createWrapper();

      expect(wrapper.vm.viewData).toEqual(mockOptions.propsData.viewData);
    });

    test('应该正确接收dialogProps属性', () => {
      const wrapper = createWrapper();

      expect(wrapper.vm.dialogProps).toEqual(mockOptions.propsData.dialogProps);
    });

    test('应该有默认的viewData', () => {
      const wrapper = shallowMount(Shixin);

      expect(wrapper.vm.viewData).toEqual({});
    });

    test('应该有默认的dialogProps', () => {
      const wrapper = shallowMount(Shixin);

      expect(wrapper.vm.dialogProps).toEqual({});
    });
  });

  describe('ccxs组件', () => {
    test('当有ccxsCount或keyNo时应该显示ccxs组件', () => {
      const wrapper = createWrapper();

      expect(wrapper.find('q-ccxs-stub').exists()).toBe(true);
      expect(wrapper.find('q-ccxs-stub').attributes('ccxs-count')).toBe('5');
      expect(wrapper.find('q-ccxs-stub').attributes('key-no')).toBe('KEY123');
    });

    test('当没有ccxsCount和keyNo时不应该显示ccxs组件', () => {
      const wrapper = createWrapper({
        dialogProps: {
          ccxsCount: null,
          keyNo: null
        }
      });

      expect(wrapper.find('q-ccxs-stub').exists()).toBe(false);
    });
  });

  describe('关联案例组件', () => {
    test('应该渲染关联案例组件', () => {
      const wrapper = createWrapper();

      expect(wrapper.find('q-relate-cases-stub').exists()).toBe(true);
      expect(wrapper.find('q-relate-cases-stub').attributes('search-params')).toBeDefined();
    });
  });

  describe('表格结构', () => {
    test('应该渲染正确的表格结构', () => {
      const wrapper = createWrapper();

      expect(wrapper.find('q-plain-table-stub').exists()).toBe(true);
      const rows = wrapper.findAll('tr');
      expect(rows.length).toBe(8);
    });

    test('应该有正确的列宽设置', () => {
      const wrapper = createWrapper();

      const cells23 = wrapper.findAll('td[width="23%"]');
      expect(cells23.length).toBeGreaterThan(0);
    });

    test('应该有正确的样式类', () => {
      const wrapper = createWrapper();

      const tbCells = wrapper.findAll('td.tb');
      expect(tbCells.length).toBeGreaterThan(0);
    });

    test('应该有正确的colspan设置', () => {
      const wrapper = createWrapper();

      const colspanCells = wrapper.findAll('td[colspan="3"]');
      expect(colspanCells.length).toBe(2); // 实际只有2个colspan="3"的单元格
    });
  });

  describe('工具提示', () => {
    test('应该显示疑似申请执行人的工具提示', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('疑似申请执行人');
      // Tooltip组件被stub了，所以检查文本内容即可
    });

    test('应该显示涉案金额的工具提示', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('涉案金额(元)');
      // Icon组件被stub了，所以检查文本内容即可
    });
  });

  describe('数据显示', () => {
    test('应该正确显示所有字段', () => {
      const testData = {
        NameKeyNoCollection: [{ Name: '测试公司', KeyNo: 'TEST123' }],
        Orgno: '测试证件号',
        SqrInfo: [{ Name: '测试申请人', KeyNo: 'SQR123' }],
        Amount: '500000',
        Executegov: '测试法院',
        Executestatus: '测试履行情况',
        Province: '测试省份',
        Executeno: '测试执行依据文号',
        Liandate: '2023-12-25',
        Anno: '测试案号',
        Executeunite: '测试执行依据单位',
        Publicdate: '2023-12-30',
        Actionremark: '测试行为具体情形',
        Yiwu: '测试义务内容'
      };

      const wrapper = createWrapper({ viewData: testData });

      expect(wrapper.text()).toContain('测试证件号');
      expect(wrapper.text()).toContain('测试法院');
      expect(wrapper.text()).toContain('测试履行情况');
      expect(wrapper.text()).toContain('测试省份');
      expect(wrapper.text()).toContain('测试执行依据文号');
      expect(wrapper.text()).toContain('2023-12-25');
      expect(wrapper.text()).toContain('测试案号');
      expect(wrapper.text()).toContain('测试执行依据单位');
      expect(wrapper.text()).toContain('2023-12-30');
      expect(wrapper.text()).toContain('测试行为具体情形');
    });
  });
});
