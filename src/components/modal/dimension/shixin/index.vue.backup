<template>
  <div>
    <q-plain-table>
      <tbody>
        <tr>
          <td width="23%" class="tb">失信被执行人</td>
          <td>
            <q-entity-link v-if="viewData.NameKeyNoCollection" :coy-arr="viewData.NameKeyNoCollection"></q-entity-link>
            <template v-else>{{ viewData.Name || '-' }}</template>
            <q-ccxs
              v-if="dialogProps.ccxsCount || dialogProps.keyNo"
              :ccxs-count="dialogProps.ccxsCount"
              :key-no="dialogProps.keyNo"
            ></q-ccxs>
          </td>
          <td width="23%" class="tb">证件号码/组织机构代码</td>
          <td>{{ viewData.Orgno || '-' }}</td>
        </tr>
        <tr>
          <td width="23%" class="tb">
            疑似申请执行人
            <Tooltip placement="bottom">
              <div slot="title">该数据由企查查基于公开数据分析得出，仅供参考，不代表企查查任何明示、暗示之观点或保证。</div>
              <Icon style="color: #d6d6d6" class="icon" type="info-circle" />
            </Tooltip>
          </td>
          <td>
            <template v-if="viewData.SqrInfo && viewData.SqrInfo.length">
              <q-entity-link :coy-obj="viewData.SqrInfo[0]"></q-entity-link>
            </template>
            <template v-else>-</template>
          </td>

          <td width="23%" class="tb">
            涉案金额(元)
            <Tooltip placement="bottom">
              <div slot="title">该数据由企查查基于公开数据分析得出，仅供参考，不代表企查查任何明示、暗示之观点或保证。</div>
              <Icon style="color: #d6d6d6" class="icon" type="info-circle" />
            </Tooltip>
          </td>
          <td>{{ formatAmount(viewData.Amount) }}</td>
        </tr>
        <tr>
          <td width="23%" class="tb">执行法院</td>
          <td>{{ viewData.Executegov || '-' }}</td>
          <td width="23%" class="tb">被执行人的履行情况</td>
          <td>{{ viewData.Executestatus || '-' }}</td>
        </tr>
        <tr>
          <td width="23%" class="tb">省份</td>
          <td>{{ viewData.Province || '-' }}</td>
          <td width="23%" class="tb">执行依据文号</td>
          <td>{{ viewData.Executeno || '-' }}</td>
        </tr>
        <tr>
          <td width="23%" class="tb">立案日期</td>
          <td>{{ viewData.Liandate | dateformat }}</td>
          <td width="23%" class="tb">案号</td>
          <td v-if="viewData.CaseSearchId && viewData.CaseSearchId.length !== 0">
            <a
              class="no"
              :href="`/embed/courtCaseDetail?caseId=${viewData.CaseSearchId[0]}&title=${viewData.Executegov}${viewData.Anno}`"
              target="_blank"
            >
              {{ viewData.Anno || '-' }}</a
            >
          </td>
          <td v-else>{{ viewData.Anno || '-' }}</td>
        </tr>
        <tr>
          <td width="23%" class="tb">做出执行依据单位</td>
          <td>{{ viewData.Executeunite || '-' }}</td>
          <td width="23%" class="tb">发布日期</td>
          <td>{{ viewData.Publicdate | dateformat }}</td>
        </tr>
        <tr>
          <td class="tb" width="23%">失信被执行人行为具体情形</td>
          <td colspan="3">{{ viewData.Actionremark || '-' }}</td>
        </tr>
        <tr>
          <td class="tb" width="23%">生效法律文书确定的义务</td>
          <td colspan="3" v-entity-click v-html="viewData.Yiwu" v-if="viewData.Yiwu"></td>
          <td colspan="3" v-else>-</td>
        </tr>
      </tbody>
    </q-plain-table>
    <q-relate-cases :search-params="viewData"></q-relate-cases>
  </div>
</template>

<script src="./component.js"></script>
<style lang="less" scoped>
.no {
  cursor: pointer;
  color: #128bed;

  &:hover {
    color: #0069bf;
  }
}
</style>
