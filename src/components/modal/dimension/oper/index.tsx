import { defineComponent, PropType } from 'vue';

import styles from './index.module.less';

interface EntityObject {
  KeyNo?: string;
  Name?: string;
}

interface ViewData {
  KeyNo?: string;
  Name?: string;
  ChangeDate?: string;
  BeforeObject?: EntityObject;
  AfterObject?: EntityObject;
  Extend?: string;
}

const Oper = defineComponent({
  name: 'Oper',

  props: {
    viewData: {
      type: Object as PropType<ViewData>,
      default: () => ({}),
    },
  },

  render() {
    const { viewData } = this;

    return (
      <div>
        <table class={styles.ntable}>
          <tr>
            <td width="23%" class={styles.tb}>变更企业：</td>
            <td width="27%">
              {viewData.KeyNo ? (
                <q-entity-link coy-obj={{ KeyNo: viewData.KeyNo, Name: viewData.Name }} />
              ) : (
                <span>{viewData.Name || '-'}</span>
              )}
            </td>
            <td width="23%" class={styles.tb}>变更日期：</td>
            <td width="27%">
              {viewData.ChangeDate || '-'}
            </td>
          </tr>
          {viewData.BeforeObject && (
            <tr>
              <td class={styles.tb} width="23%">变更前：</td>
              <td colspan="3">
                {viewData.BeforeObject.KeyNo ? (
                  <q-entity-link
                    coy-obj={{
                      KeyNo: viewData.BeforeObject.KeyNo,
                      Name: viewData.BeforeObject.Name,
                    }}
                  />
                ) : (
                  <span>{viewData.BeforeObject.Name || '-'}</span>
                )}
                {viewData.Extend && <span>，{viewData.Extend}</span>}
              </td>
            </tr>
          )}
          {viewData.AfterObject && (
            <tr>
              <td class={styles.tb} width="23%">变更后：</td>
              <td colspan="3">
                {viewData.AfterObject.KeyNo ? (
                  <q-entity-link
                    coy-obj={{
                      KeyNo: viewData.AfterObject.KeyNo,
                      Name: viewData.AfterObject.Name,
                    }}
                  />
                ) : (
                  <span>{viewData.AfterObject.Name || '-'}</span>
                )}
              </td>
            </tr>
          )}
        </table>
      </div>
    );
  },
});

export default Oper;
