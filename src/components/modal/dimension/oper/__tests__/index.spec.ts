import { shallowMount } from '@vue/test-utils';

import Oper from '../index.tsx';

describe('Oper', () => {
  let wrapper: any;
  let mockOptions: any;

  beforeEach(() => {
    mockOptions = {
      propsData: {
        viewData: {
          KeyNo: 'KEY123',
          Name: '测试企业有限公司',
          ChangeDate: '2023-01-01',
          BeforeObject: {
            KeyNo: 'BEFORE123',
            Name: '原经营者'
          },
          AfterObject: {
            KeyNo: 'AFTER123',
            Name: '新经营者'
          },
          Extend: '附加信息'
        },
      },
      stubs: {
        'q-entity-link': true
      }
    };
  });

  const createWrapper = (propsData: any = {}, options: any = {}) => {
    const mergedOptions = {
      ...mockOptions,
      propsData: {
        viewData: {
          ...mockOptions.propsData.viewData,
          ...propsData.viewData,
        },
      },
      ...options,
    };

    wrapper = shallowMount(Oper, mergedOptions);
    return wrapper;
  };

  afterEach(() => {
    if (wrapper) {
      wrapper.destroy();
    }
  });

  describe('基本信息渲染', () => {
    test('应该正确渲染变更企业信息', () => {
      const wrapper = createWrapper();

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('变更企业');
      expect(wrapper.find('q-entity-link-stub').exists()).toBe(true);
    });

    test('应该正确显示变更日期', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('变更日期');
      expect(wrapper.text()).toContain('2023-01-01');
    });

    test('应该正确显示变更前信息', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('变更前');
      // 实体链接被stub了，所以不会显示实际名称，但会显示附加信息
      expect(wrapper.text()).toContain('附加信息');
    });

    test('应该正确显示变更后信息', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('变更后');
      // 实体链接被stub了，所以不会显示实际名称
    });
  });

  describe('实体链接处理', () => {
    test('当有KeyNo时应该显示实体链接', () => {
      const wrapper = createWrapper();

      const entityLinks = wrapper.findAll('q-entity-link-stub');
      expect(entityLinks.length).toBe(3); // 主企业 + 变更前 + 变更后
    });

    test('当没有KeyNo时应该显示文本', () => {
      const wrapper = createWrapper({
        viewData: {
          KeyNo: null,
          Name: '测试企业',
          BeforeObject: {
            KeyNo: null,
            Name: '原经营者'
          },
          AfterObject: {
            KeyNo: null,
            Name: '新经营者'
          }
        }
      });

      const entityLinks = wrapper.findAll('q-entity-link-stub');
      expect(entityLinks.length).toBe(0);
      expect(wrapper.text()).toContain('测试企业');
      expect(wrapper.text()).toContain('原经营者');
      expect(wrapper.text()).toContain('新经营者');
    });

    test('应该传递正确的企业信息给实体链接', () => {
      const wrapper = createWrapper();

      const entityLinks = wrapper.findAll('q-entity-link-stub');
      expect(entityLinks.at(0).attributes('coy-obj')).toBeDefined();
      expect(entityLinks.at(1).attributes('coy-obj')).toBeDefined();
      expect(entityLinks.at(2).attributes('coy-obj')).toBeDefined();
    });
  });

  describe('条件渲染', () => {
    test('当有BeforeObject时应该显示变更前行', () => {
      const wrapper = createWrapper();

      const rows = wrapper.findAll('tr');
      expect(rows.length).toBe(3);
      expect(wrapper.text()).toContain('变更前');
    });

    test('当没有BeforeObject时不应该显示变更前行', () => {
      const wrapper = createWrapper({
        viewData: {
          BeforeObject: null
        }
      });

      expect(wrapper.text()).not.toContain('变更前');
    });

    test('当有AfterObject时应该显示变更后行', () => {
      const wrapper = createWrapper();

      const rows = wrapper.findAll('tr');
      expect(rows.length).toBe(3);
      expect(wrapper.text()).toContain('变更后');
    });

    test('当没有AfterObject时不应该显示变更后行', () => {
      const wrapper = createWrapper({
        viewData: {
          AfterObject: null
        }
      });

      expect(wrapper.text()).not.toContain('变更后');
    });

    test('当有Extend时应该显示附加信息', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('附加信息');
    });

    test('当没有Extend时不应该显示附加信息', () => {
      const wrapper = createWrapper({
        viewData: {
          Extend: null
        }
      });

      // 检查是否没有逗号和附加信息
      const text = wrapper.text();
      expect(text).not.toContain('，');
    });
  });

  describe('边界情况', () => {
    test('应该处理空的viewData', () => {
      const wrapper = createWrapper({ viewData: {} });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理undefined的字段', () => {
      const wrapper = createWrapper({
        viewData: {
          KeyNo: undefined,
          Name: undefined,
          ChangeDate: undefined,
          BeforeObject: undefined,
          AfterObject: undefined,
          Extend: undefined
        }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理null值字段', () => {
      const wrapper = createWrapper({
        viewData: {
          KeyNo: null,
          Name: null,
          ChangeDate: null,
          BeforeObject: null,
          AfterObject: null,
          Extend: null
        }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理空字符串', () => {
      const wrapper = createWrapper({
        viewData: {
          KeyNo: '',
          Name: '',
          ChangeDate: '',
          BeforeObject: null,
          AfterObject: null,
          Extend: ''
        }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });
  });

  describe('表格结构', () => {
    test('应该渲染正确的表格结构', () => {
      const wrapper = createWrapper();

      const table = wrapper.find('table');
      expect(table.exists()).toBe(true);
      expect(table.classes()).toContain('ntable');

      const rows = wrapper.findAll('tr');
      expect(rows.length).toBe(3); // 基本信息 + 变更前 + 变更后
    });

    test('应该有正确的列宽设置', () => {
      const wrapper = createWrapper();

      const cells23 = wrapper.findAll('td[width="23%"]');
      expect(cells23.length).toBeGreaterThan(0);

      const cells27 = wrapper.findAll('td[width="27%"]');
      expect(cells27.length).toBeGreaterThan(0);
    });

    test('应该有正确的样式类', () => {
      const wrapper = createWrapper();

      const tbCells = wrapper.findAll('td.tb');
      expect(tbCells.length).toBeGreaterThan(0);
    });

    test('应该有正确的colspan设置', () => {
      const wrapper = createWrapper();

      const colspanCells = wrapper.findAll('td[colspan="3"]');
      expect(colspanCells.length).toBe(2); // 变更前和变更后各一个
    });
  });

  describe('组件属性', () => {
    test('应该正确接收viewData属性', () => {
      const wrapper = createWrapper();

      expect(wrapper.vm.viewData).toEqual(mockOptions.propsData.viewData);
    });

    test('应该有默认的viewData', () => {
      const wrapper = shallowMount(Oper);

      expect(wrapper.vm.viewData).toEqual({});
    });
  });

  describe('数据显示', () => {
    test('应该正确显示所有字段', () => {
      const testData = {
        KeyNo: 'TEST123',
        Name: '测试公司',
        ChangeDate: '2023-12-25',
        BeforeObject: {
          KeyNo: 'BEFORE123',
          Name: '原测试经营者'
        },
        AfterObject: {
          KeyNo: 'AFTER123',
          Name: '新测试经营者'
        },
        Extend: '测试附加信息'
      };

      const wrapper = createWrapper({ viewData: testData });

      expect(wrapper.text()).toContain('2023-12-25');
      // 实体链接被stub了，所以不会显示实际名称，但会显示附加信息
      expect(wrapper.text()).toContain('测试附加信息');
    });
  });

  describe('嵌套对象处理', () => {
    test('应该正确处理BeforeObject的KeyNo和Name', () => {
      const wrapper = createWrapper();

      const beforeEntityLink = wrapper.findAll('q-entity-link-stub').at(1);
      expect(beforeEntityLink.exists()).toBe(true);
    });

    test('应该正确处理AfterObject的KeyNo和Name', () => {
      const wrapper = createWrapper();

      const afterEntityLink = wrapper.findAll('q-entity-link-stub').at(2);
      expect(afterEntityLink.exists()).toBe(true);
    });

    test('当BeforeObject没有KeyNo时应该显示文本', () => {
      const wrapper = createWrapper({
        viewData: {
          BeforeObject: {
            KeyNo: null,
            Name: '无KeyNo的经营者'
          }
        }
      });

      expect(wrapper.text()).toContain('无KeyNo的经营者');
    });

    test('当AfterObject没有KeyNo时应该显示文本', () => {
      const wrapper = createWrapper({
        viewData: {
          AfterObject: {
            KeyNo: null,
            Name: '无KeyNo的新经营者'
          }
        }
      });

      expect(wrapper.text()).toContain('无KeyNo的新经营者');
    });
  });

  describe('特殊情况', () => {
    test('应该处理只有BeforeObject的情况', () => {
      const wrapper = createWrapper({
        viewData: {
          BeforeObject: {
            KeyNo: 'BEFORE123',
            Name: '原经营者'
          },
          AfterObject: null
        }
      });

      expect(wrapper.text()).toContain('变更前');
      expect(wrapper.text()).not.toContain('变更后');
    });

    test('应该处理只有AfterObject的情况', () => {
      const wrapper = createWrapper({
        viewData: {
          BeforeObject: null,
          AfterObject: {
            KeyNo: 'AFTER123',
            Name: '新经营者'
          }
        }
      });

      expect(wrapper.text()).not.toContain('变更前');
      expect(wrapper.text()).toContain('变更后');
    });

    test('应该处理BeforeObject和AfterObject都为空的情况', () => {
      const wrapper = createWrapper({
        viewData: {
          BeforeObject: null,
          AfterObject: null
        }
      });

      const rows = wrapper.findAll('tr');
      expect(rows.length).toBe(1); // 只有基本信息行
    });
  });
});
