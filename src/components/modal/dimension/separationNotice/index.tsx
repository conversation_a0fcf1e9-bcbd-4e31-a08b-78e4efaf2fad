import { PropType, defineComponent } from 'vue';
import { dateFormat } from '@/utils/format';

const SeparationNotice = defineComponent({
  props: {
    viewData: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}),
    },
  },
  render() {
    console.log(this);
    const { viewData } = this;
    return (
      <table class="ntable">
        <tr>
          <td width="180" class="tb">
            做出决定日期
          </td>
          <td>{dateFormat(viewData.DecideDate)}</td>
          <td width="180" class="tb">
            公告日期
          </td>
          <td>{dateFormat(viewData.NoticeDate)}</td>
        </tr>
        <tr>
          <td width="180" class="tb">
            公告期限
          </td>
          <td colspan="3">
            {viewData.StartDate || viewData.EndDate ? (
              <span>
                {dateFormat(viewData.StartDate)} 至 {dateFormat(viewData.EndDate)}
              </span>
            ) : (
              '-'
            )}
          </td>
        </tr>
        <tr>
          <td width="180" class="tb">
            公告内容
          </td>
          <td colspan="3">{viewData.Content || '-'}</td>
        </tr>
        <tr>
          <td width="180" class="tb">
            地址
          </td>
          <td colspan="3">{viewData.Address || '-'}</td>
        </tr>
        <tr>
          <td width="180" class="tb">
            联系人
          </td>
          <td>{viewData.Contactor || '-'}</td>
          <td width="180" class="tb">
            联系电话
          </td>
          <td>{viewData.Tel || '-'}</td>
        </tr>
      </table>
    );
  },
});

export default SeparationNotice;
