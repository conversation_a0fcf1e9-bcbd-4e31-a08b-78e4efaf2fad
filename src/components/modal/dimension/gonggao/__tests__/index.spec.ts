import { shallowMount } from '@vue/test-utils';

import Gonggao from '../index.tsx';

describe('Gonggao', () => {
  let wrapper: any;
  let mockOptions: any;

  beforeEach(() => {
    mockOptions = {
      propsData: {
        viewData: {
          ProsecutorList: [
            { name: '原告公司', id: '123' }
          ],
          DefendantList: [
            { name: '被告公司', id: '456' }
          ],
          ThirdPartyList: [
            { name: '第三人公司', id: '789' }
          ],
          OtherPartyList: [
            { name: '其他当事人', id: '101' }
          ],
          CaseReason: '合同纠纷',
          CaseReasonDescription: '详细案由说明',
          Category: '开庭公告',
          PublishDate: '2023-01-01',
          PublishPage: 'A01版',
          Court: '北京市第一中级人民法院',
          SubmitDate: '2023-01-02',
          CaseSearchId: ['case123'],
          Ano: '(2023)京01民初123号',
          Content: '公告内容详情'
        },
      },
      stubs: {
        'q-entity-link': true,
        'q-link': true,
        'q-relate-cases': true,
        'Icon': true,
        'Tooltip': true
      }
    };
  });

  const createWrapper = (propsData: any = {}, options: any = {}) => {
    const mergedOptions = {
      ...mockOptions,
      propsData: {
        viewData: {
          ...mockOptions.propsData.viewData,
          ...propsData.viewData,
        },
      },
      ...options,
    };

    wrapper = shallowMount(Gonggao, mergedOptions);
    return wrapper;
  };

  afterEach(() => {
    if (wrapper) {
      wrapper.destroy();
    }
  });

  describe('基本信息渲染', () => {
    test('应该正确渲染当事人信息', () => {
      const wrapper = createWrapper();

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('当事人');
      // 检查haspartinfo状态
      expect(wrapper.vm.haspartinfo).toBe(true);
    });

    test('应该正确显示案由信息', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('案由');
      expect(wrapper.text()).toContain('合同纠纷');
    });

    test('应该正确显示公告基本信息', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('公告类型');
      expect(wrapper.text()).toContain('开庭公告');
      expect(wrapper.text()).toContain('刊登日期');
      expect(wrapper.text()).toContain('2023-01-01');
    });

    test('应该正确显示法院和版面信息', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('刊登版面');
      expect(wrapper.text()).toContain('A01版');
      expect(wrapper.text()).toContain('公告人');
      expect(wrapper.text()).toContain('北京市第一中级人民法院');
    });

    test('应该正确显示案号和上传日期', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('上传日期');
      expect(wrapper.text()).toContain('案号');
      expect(wrapper.text()).toContain('(2023)京01民初123号');
    });

    test('应该正确显示内容', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('内容');
      expect(wrapper.text()).toContain('公告内容详情');
    });
  });

  describe('当事人信息处理', () => {
    test('当有当事人信息时应该显示haspartinfo为true', () => {
      const wrapper = createWrapper();

      expect(wrapper.vm.haspartinfo).toBe(true);
    });

    test('当没有当事人信息时应该显示haspartinfo为false', () => {
      const wrapper = createWrapper({
        viewData: {
          ProsecutorList: [],
          DefendantList: [],
          ThirdPartyList: [],
          OtherPartyList: []
        }
      });

      expect(wrapper.vm.haspartinfo).toBe(false);
      expect(wrapper.text()).toContain('-');
    });

    test('应该正确处理部分当事人信息', () => {
      const wrapper = createWrapper({
        viewData: {
          ProsecutorList: [{ name: '原告公司', id: '123' }],
          DefendantList: [],
          ThirdPartyList: [],
          OtherPartyList: []
        }
      });

      expect(wrapper.vm.haspartinfo).toBe(true);
      // 检查viewData是否正确设置
      expect(wrapper.vm.viewData.ProsecutorList.length).toBe(1);
    });
  });

  describe('案由信息处理', () => {
    test('当有案由描述时应该显示提示图标', () => {
      const wrapper = createWrapper();

      // 检查viewData中是否有案由描述
      expect(wrapper.vm.viewData.CaseReasonDescription).toBeTruthy();
      expect(wrapper.vm.viewData.CaseReasonDescription).toBe('详细案由说明');
    });

    test('当没有案由描述时不应该显示提示图标', () => {
      const wrapper = createWrapper({
        viewData: {
          CaseReason: '合同纠纷',
          CaseReasonDescription: ''
        }
      });

      expect(wrapper.find('Icon-stub').exists()).toBe(false);
      expect(wrapper.find('Tooltip-stub').exists()).toBe(false);
    });
  });

  describe('案号链接处理', () => {
    test('当有CaseSearchId时应该显示链接', () => {
      const wrapper = createWrapper();

      expect(wrapper.find('q-link-stub').exists()).toBe(true);
    });

    test('当没有CaseSearchId时应该显示普通文本', () => {
      const wrapper = createWrapper({
        viewData: {
          CaseSearchId: [],
          Ano: '(2023)京01民初123号'
        }
      });

      expect(wrapper.find('q-link-stub').exists()).toBe(false);
    });
  });

  describe('边界情况', () => {
    test('应该处理空的viewData', () => {
      const wrapper = createWrapper({ viewData: {} });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理undefined的字段', () => {
      const wrapper = createWrapper({
        viewData: {
          CaseReason: undefined,
          Category: undefined,
          PublishDate: undefined,
          Court: undefined
        }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理null值', () => {
      const wrapper = createWrapper({
        viewData: {
          CaseReason: null,
          Category: null,
          PublishDate: null,
          Court: null
        }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理没有内容的情况', () => {
      const wrapper = createWrapper({
        viewData: {
          Content: ''
        }
      });

      expect(wrapper.exists()).toBe(true);
      // 当没有内容时，内容行不应该渲染
      const contentRows = wrapper.findAll('tr').filter((row: any) =>
        row.text().includes('内容') && row.findAll('td').length > 1
      );
      expect(contentRows.length).toBe(0);
    });
  });

  describe('方法测试', () => {
    test('checkHasData方法应该存在', () => {
      const wrapper = createWrapper();

      expect(wrapper.vm.checkHasData).toBeDefined();
      expect(typeof wrapper.vm.checkHasData).toBe('function');
    });

    test('dateformat方法应该存在', () => {
      const wrapper = createWrapper();

      expect(wrapper.vm.dateformat).toBeDefined();
      expect(typeof wrapper.vm.dateformat).toBe('function');
    });
  });

  describe('表格结构', () => {
    test('应该渲染正确的表格结构', () => {
      const wrapper = createWrapper();

      const table = wrapper.find('q-plain-table');
      expect(table.exists()).toBe(true);

      const tbody = wrapper.find('tbody');
      expect(tbody.exists()).toBe(true);

      const rows = wrapper.findAll('tr');
      expect(rows.length).toBeGreaterThan(5);
    });

    test('应该有正确的列宽设置', () => {
      const wrapper = createWrapper();

      const cells23 = wrapper.findAll('td[width="23%"]');
      expect(cells23.length).toBeGreaterThan(0);

      const cells27 = wrapper.findAll('td[width="27%"]');
      expect(cells27.length).toBeGreaterThan(0);
    });
  });

  describe('组件集成', () => {
    test('应该包含相关案件组件', () => {
      const wrapper = createWrapper();

      expect(wrapper.find('q-relate-cases-stub').exists()).toBe(true);
    });
  });
});
