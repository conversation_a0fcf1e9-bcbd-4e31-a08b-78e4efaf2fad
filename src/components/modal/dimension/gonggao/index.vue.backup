<template>
  <div>
    <q-plain-table>
      <tbody>
        <tr>
          <td class="tb" style="width: 15%">当事人</td>
          <td colspan="3">
            <span v-if="haspartinfo">
              <template v-if="viewData.ProsecutorList && viewData.ProsecutorList.length > 0">
                上诉人/原告-<q-entity-link :coy-arr="viewData.ProsecutorList"></q-entity-link>
                <br />
              </template>
              <template v-if="viewData.DefendantList && viewData.DefendantList.length > 0">
                被上诉人/被告-<q-entity-link :coy-arr="viewData.DefendantList"></q-entity-link>
                <br />
              </template>
              <template v-if="viewData.ThirdPartyList && viewData.ThirdPartyList.length > 0">
                第三人-<q-entity-link :coy-arr="viewData.ThirdPartyList"></q-entity-link>
                <br />
              </template>
              <template v-if="viewData.OtherPartyList && viewData.OtherPartyList.length > 0">
                <span
                  v-if="
                    (viewData.ProsecutorList && viewData.ProsecutorList.length > 0) ||
                    (viewData.DefendantList && viewData.DefendantList.length > 0) ||
                    (viewData.ThirdPartyList && viewData.ThirdPartyList.length > 0)
                  "
                  >其他当事人-</span
                >
                <q-entity-link :coy-arr="viewData.OtherPartyList"></q-entity-link>
              </template>
            </span>
            <span v-else>
              <span>-</span>
            </span>
          </td>
        </tr>
        <tr>
          <td class="tb" width="23%">案由</td>
          <td colspan="3">
            {{ viewData.CaseReason || '-' }}
            <span v-if="viewData.CaseReasonDescription">
              <Tooltip placement="bottom">
                <div slot="title">{{ viewData.CaseReasonDescription }}</div>
                <Icon style="color: #d6d6d6" class="icon" type="info-circle" />
              </Tooltip>
            </span>
          </td>
        </tr>
        <tr>
          <td width="23%" class="tb">公告类型</td>
          <td width="27%">{{ viewData.Category || '-' }}</td>
          <td width="23%" class="tb">刊登日期</td>
          <td v-if="viewData.PublishDate">{{ viewData.PublishDate | dateformat }}</td>
          <td v-else>-</td>
        </tr>
        <tr>
          <td width="23%" class="tb">刊登版面</td>
          <td>{{ viewData.PublishPage || '-' }}</td>
          <td width="23%" class="tb">公告人</td>
          <td>{{ viewData.Court || '-' }}</td>
        </tr>
        <tr>
          <td class="tb" width="23%">上传日期</td>
          <td>{{ viewData.SubmitDate | dateformat }}</td>
          <td width="23%" class="tb">案号</td>
          <td v-if="viewData.CaseSearchId && viewData.CaseSearchId.length !== 0">
            <q-link :to="`/caseDetail/${viewData.CaseSearchId[0]}`">
              <span v-html="viewData.Ano || '-'"></span>
            </q-link>
          </td>
          <td v-else v-html="viewData.Ano || '-'"></td>
        </tr>
        <tr>
          <td class="tb" width="23%">内容</td>
          <td colspan="3" v-html="viewData.Content" v-entity-click v-if="viewData.Content"></td>
          <!-- <td v-else>-</td> -->
        </tr>
      </tbody>
    </q-plain-table>
    <q-relate-cases :search-params="viewData"></q-relate-cases>
  </div>
</template>

<script src="./component.js"></script>
