import { defineComponent, PropType, ref, onMounted } from 'vue';
import { Icon, Tooltip } from 'ant-design-vue';

import { dateFormat } from '@/utils/format';

import styles from './index.module.less';

interface EntityItem {
  name?: string;
  id?: string;
}

interface ViewData {
  ProsecutorList?: EntityItem[];
  DefendantList?: EntityItem[];
  ThirdPartyList?: EntityItem[];
  OtherPartyList?: EntityItem[];
  CaseReason?: string;
  CaseReasonDescription?: string;
  Category?: string;
  PublishDate?: string;
  PublishPage?: string;
  Court?: string;
  SubmitDate?: string;
  CaseSearchId?: string[];
  Ano?: string;
  Content?: string;
}

const Gonggao = defineComponent({
  name: 'Gonggao',

  components: {
    Icon,
    Tooltip,
  },

  props: {
    viewData: {
      type: Object as PropType<ViewData>,
      default: () => ({}),
    },
  },

  setup(props) {
    const haspartinfo = ref(false);

    const checkHasData = () => {
      if (
        (props.viewData.ProsecutorList && props.viewData.ProsecutorList.length !== 0) ||
        (props.viewData.DefendantList && props.viewData.DefendantList.length !== 0) ||
        (props.viewData.ThirdPartyList && props.viewData.ThirdPartyList.length !== 0) ||
        (props.viewData.OtherPartyList && props.viewData.OtherPartyList.length !== 0)
      ) {
        haspartinfo.value = true;
      }
    };

    const dateformat = (date: string, options?: any) => {
      return dateFormat(date, options);
    };

    onMounted(() => {
      checkHasData();
    });

    return {
      haspartinfo,
      checkHasData,
      dateformat,
    };
  },

  render() {
    const { viewData, haspartinfo, dateformat } = this;

    const renderPartyInfo = () => {
      if (haspartinfo) {
        const elements = [];

        if (viewData.ProsecutorList && viewData.ProsecutorList.length > 0) {
          elements.push(
            <span key="prosecutor">
              上诉人/原告-<q-entity-link coy-arr={viewData.ProsecutorList}></q-entity-link>
              <br />
            </span>
          );
        }

        if (viewData.DefendantList && viewData.DefendantList.length > 0) {
          elements.push(
            <span key="defendant">
              被上诉人/被告-<q-entity-link coy-arr={viewData.DefendantList}></q-entity-link>
              <br />
            </span>
          );
        }

        if (viewData.ThirdPartyList && viewData.ThirdPartyList.length > 0) {
          elements.push(
            <span key="third-party">
              第三人-<q-entity-link coy-arr={viewData.ThirdPartyList}></q-entity-link>
              <br />
            </span>
          );
        }

        if (viewData.OtherPartyList && viewData.OtherPartyList.length > 0) {
          const hasOtherParties =
            (viewData.ProsecutorList && viewData.ProsecutorList.length > 0) ||
            (viewData.DefendantList && viewData.DefendantList.length > 0) ||
            (viewData.ThirdPartyList && viewData.ThirdPartyList.length > 0);

          elements.push(
            <span key="other-party">
              {hasOtherParties && <span>其他当事人-</span>}
              <q-entity-link coy-arr={viewData.OtherPartyList}></q-entity-link>
            </span>
          );
        }

        return <span>{elements}</span>;
      } else {
        return <span>-</span>;
      }
    };

    return (
      <div>
        <q-plain-table>
          <tbody>
            <tr>
              <td class={styles.tb} style="width: 15%">
                当事人
              </td>
              <td colspan="3">{renderPartyInfo()}</td>
            </tr>
            <tr>
              <td class={styles.tb} width="23%">
                案由
              </td>
              <td colspan="3">
                {viewData.CaseReason || '-'}
                {viewData.CaseReasonDescription && (
                  <Tooltip placement="bottom" title={viewData.CaseReasonDescription}>
                    <Icon style="color: #d6d6d6" class={styles.icon} type="info-circle" />
                  </Tooltip>
                )}
              </td>
            </tr>
            <tr>
              <td width="23%" class={styles.tb}>
                公告类型
              </td>
              <td width="27%">{viewData.Category || '-'}</td>
              <td width="23%" class={styles.tb}>
                刊登日期
              </td>
              <td>{viewData.PublishDate ? dateformat(viewData.PublishDate) : '-'}</td>
            </tr>
            <tr>
              <td width="23%" class={styles.tb}>
                刊登版面
              </td>
              <td>{viewData.PublishPage || '-'}</td>
              <td width="23%" class={styles.tb}>
                公告人
              </td>
              <td>{viewData.Court || '-'}</td>
            </tr>
            <tr>
              <td class={styles.tb} width="23%">
                上传日期
              </td>
              <td>{dateformat(viewData.SubmitDate)}</td>
              <td width="23%" class={styles.tb}>
                案号
              </td>
              <td>
                {viewData.CaseSearchId && viewData.CaseSearchId.length !== 0 ? (
                  <q-link to={`/caseDetail/${viewData.CaseSearchId[0]}`}>
                    <span domPropsInnerHTML={viewData.Ano || '-'}></span>
                  </q-link>
                ) : (
                  <span domPropsInnerHTML={viewData.Ano || '-'}></span>
                )}
              </td>
            </tr>
            {viewData.Content && (
              <tr>
                <td class={styles.tb} width="23%">
                  内容
                </td>
                <td colspan="3" domPropsInnerHTML={viewData.Content} v-entity-click></td>
              </tr>
            )}
          </tbody>
        </q-plain-table>
        <q-relate-cases search-params={viewData}></q-relate-cases>
      </div>
    );
  },
});

export default Gonggao;
