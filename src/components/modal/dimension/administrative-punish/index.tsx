import { defineComponent, PropType } from 'vue';

import { dateFormat } from '@/utils/format';
import QSourceLogo from '@/components/global/q-source-logo';
import QRelateCases from '@/components/global/q-relate-cases';

import styles from './index.module.less';

interface ViewData {
  DocNo?: string;
  PunishOffice?: string;
  PunishReason?: string;
  PunishResult?: string;
  PenaltyMoney?: number;
  PunishBasis?: string;
  PunishDate?: string;
  PublishDate?: string;
  OssUrl?: string;
  PunishmentAltList?: Array<{
    Alt?: string;
    AltBefore?: string;
    AltAfter?: string;
    AltDate?: string;
    PenAuth?: string;
  }>;
}

const AdministrativePunish = defineComponent({
  name: 'AdministrativePunish',

  props: {
    viewData: {
      type: Object as PropType<ViewData>,
      default: () => ({}),
    },
  },

  setup() {
    const getMoney = (money?: number): string => {
      if (money) {
        return money.toLocaleString();
      }
      return '-';
    };

    const formatDate = (date?: string): string => {
      return dateFormat(date, { pattern: 'YYYY-MM-DD' });
    };

    return {
      getMoney,
      formatDate,
    };
  },

  render() {
    const { viewData } = this;

    if (!viewData) {
      return null;
    }

    return (
      <div>
        <table class="ntable">
          <tr>
            <td class="tb" width="20%">
              行政处罚决定书文号
            </td>
            <td width="30%">{viewData.DocNo || '-'}</td>
            <td class="tb" width="20%">
              处罚单位
            </td>
            <td width="30%">{viewData.PunishOffice || '-'}</td>
          </tr>
          <tr>
            <td class="tb">处罚事由</td>
            <td colspan="5" domPropsInnerHTML={viewData.PunishReason || '-'}></td>
          </tr>
          <tr>
            <td class="tb">处罚结果</td>
            <td colspan="5" domPropsInnerHTML={viewData.PunishResult || '-'}></td>
          </tr>
          {viewData.PenaltyMoney && (
            <tr>
              <td class="tb">处罚金额（元）</td>
              <td colspan="5" domPropsInnerHTML={this.getMoney(viewData.PenaltyMoney)}></td>
            </tr>
          )}
          <tr>
            <td class="tb">处罚依据</td>
            <td colspan="5" domPropsInnerHTML={viewData.PunishBasis || '-'}></td>
          </tr>
          <tr>
            <td class="tb" width="20%">
              处罚日期
            </td>
            <td width="30%">{this.formatDate(viewData.PunishDate)}</td>
            <td class="tb" width="20%">
              发布日期
            </td>
            <td width="30%">{this.formatDate(viewData.PublishDate)}</td>
          </tr>
          {viewData.OssUrl && viewData.OssUrl.indexOf('.txt') === -1 && (
            <tr>
              <td class="tb">原文</td>
              <td colspan="5">
                <QSourceLogo label="查看" url={viewData.OssUrl} />
              </td>
            </tr>
          )}
        </table>

        {/* 补充变更信息 */}
        {viewData.PunishmentAltList && viewData.PunishmentAltList.length > 0 && (
          <div class="punishment-alt-relat">
            <div class="tcaption" style="margin-bottom: 0">
              行政处罚变更信息
            </div>
            <table class="ntable ntable-odd">
              <tr>
                <th width="50px">序号</th>
                <th>变更事项</th>
                <th>变更前内容</th>
                <th>变更后内容</th>
                <th>变更日期</th>
                <th>作出变更决定机关</th>
              </tr>
              {viewData.PunishmentAltList.map((item, index) => (
                <tr key={index}>
                  <td width="50px" class={styles.textCenter}>
                    {index + 1}
                  </td>
                  <td width="20%" domPropsInnerHTML={item.Alt || '-'}></td>
                  <td width="20%" domPropsInnerHTML={item.AltBefore || '-'}></td>
                  <td width="20%" domPropsInnerHTML={item.AltAfter || '-'}></td>
                  <td width="15%">{item.AltDate || '-'}</td>
                  <td domPropsInnerHTML={item.PenAuth || '-'}></td>
                </tr>
              ))}
            </table>
          </div>
        )}

        <QRelateCases searchParams={viewData} stitle="关联" />
      </div>
    );
  },
});

export default AdministrativePunish;
