import { shallowMount } from '@vue/test-utils';

import TaxPunish from '../index.vue';

// Mock q-entity-link component
const mockQEntityLink = {
  name: 'QEntityLink',
  template: '<a>{{ coyObj.Name }}</a>',
  props: ['coyObj'],
};

// Mock q-relate-cases component
const mockQRelateCases = {
  name: 'QRelateCases',
  template: '<div>关联案件</div>',
  props: ['searchParams', 'stitle'],
};

describe('TaxPunish', () => {
  const defaultViewData = {
    KeyNo: 'tax-key-123',
    CompanyName: '被处罚企业名称',
    CreditCode: '91110000123456789X',
    Oper: {
      KeyNo: 'oper-key-456',
      Name: '法定代表人姓名',
    },
    CaseName: '税务违法案件',
    CaseNo: 'TAX-001',
    Reason: '偷税漏税',
    Type: '税务处罚',
    Status: '已执行',
    Result: '罚款5万元',
    PunishDate: '2023-01-01',
    Basis: '税法第XX条',
    IssuedBy: '国家税务总局XX分局',
    Level: '国税',
    Province: '北京市',
  };

  let wrapper: any;
  let mockOptions: any;

  beforeEach(() => {
    mockOptions = {
      propsData: {
        viewData: defaultViewData,
      },
      stubs: {
        'q-entity-link': mockQEntityLink,
        'q-relate-cases': mockQRelateCases,
      },
    };
  });

  const createWrapper = (viewData = defaultViewData, options = {}) => {
    const mergedOptions = {
      ...mockOptions,
      propsData: {
        ...mockOptions.propsData,
        viewData,
      },
      ...options,
    };

    wrapper = shallowMount(TaxPunish, mergedOptions);
    return wrapper;
  };

  describe('基本渲染', () => {
    test('应该正确渲染基本信息', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('被处罚企业名称');
      expect(wrapper.text()).toContain('91110000123456789X');
      expect(wrapper.text()).toContain('税务违法案件');
      expect(wrapper.text()).toContain('TAX-001');
      expect(wrapper.text()).toContain('偷税漏税');
      expect(wrapper.text()).toContain('税务处罚');
      expect(wrapper.text()).toContain('已执行');
      expect(wrapper.text()).toContain('罚款5万元');
      expect(wrapper.text()).toContain('税法第XX条');
      expect(wrapper.text()).toContain('国家税务总局XX分局');
      expect(wrapper.text()).toContain('国税');
      expect(wrapper.text()).toContain('北京市');
    });

    test('应该正确格式化日期', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('2023-01-01');
    });

    test('应该显示企业名称链接', () => {
      const wrapper = createWrapper();

      const entityLinkComponents = wrapper.findAllComponents(mockQEntityLink);
      expect(entityLinkComponents.length).toBeGreaterThan(0);

      // 检查企业名称链接
      const companyLink = entityLinkComponents.at(0);
      expect(companyLink.props('coyObj')).toEqual({
        KeyNo: 'tax-key-123',
        Name: '被处罚企业名称',
      });
    });

    test('应该显示法定代表人链接', () => {
      const wrapper = createWrapper();

      const entityLinkComponents = wrapper.findAllComponents(mockQEntityLink);
      expect(entityLinkComponents.length).toBe(2);

      // 检查法定代表人链接
      const operLink = entityLinkComponents.at(1);
      expect(operLink.props('coyObj')).toEqual({
        KeyNo: 'oper-key-456',
        Name: '法定代表人姓名',
      });
    });
  });

  describe('空值处理', () => {
    test('应该正确处理空字段', () => {
      const emptyViewData = {
        KeyNo: '',
        CompanyName: '',
        CreditCode: '',
        Oper: { KeyNo: '', Name: '' }, // 提供空对象而不是null
        CaseName: '',
        CaseNo: '',
        Reason: '',
        Type: '',
        Status: '',
        Result: '',
        Basis: '',
        IssuedBy: '',
        Level: '',
        Province: '',
      };

      const wrapper = createWrapper(emptyViewData);

      expect(wrapper.text()).toContain('-');
    });

    test('应该正确处理undefined字段', () => {
      const wrapper = createWrapper({
        Oper: { KeyNo: '', Name: '' }, // 提供空对象避免模板错误
      });

      expect(wrapper.text()).toContain('-');
    });

    test('当Oper为空时应该正确处理', () => {
      const viewDataWithoutOper = {
        ...defaultViewData,
        Oper: { KeyNo: '', Name: '' }, // 提供空对象而不是null
      };

      const wrapper = createWrapper(viewDataWithoutOper);

      const entityLinkComponents = wrapper.findAllComponents(mockQEntityLink);
      // 应该有两个链接：企业名称和法定代表人
      expect(entityLinkComponents.length).toBe(2);
      expect(entityLinkComponents.at(0).props('coyObj')).toEqual({
        KeyNo: 'tax-key-123',
        Name: '被处罚企业名称',
      });
      expect(entityLinkComponents.at(1).props('coyObj')).toEqual({
        KeyNo: '',
        Name: '',
      });
    });

    test('当Oper为空对象时应该正确处理', () => {
      const viewDataWithEmptyOper = {
        ...defaultViewData,
        Oper: {},
      };

      const wrapper = createWrapper(viewDataWithEmptyOper);

      const entityLinkComponents = wrapper.findAllComponents(mockQEntityLink);
      expect(entityLinkComponents.length).toBe(2);
      expect(entityLinkComponents.at(1).props('coyObj')).toEqual({});
    });
  });

  describe('表格结构', () => {
    test('应该包含正确的表格结构', () => {
      const wrapper = createWrapper();

      const table = wrapper.find('table.ntable');
      expect(table.exists()).toBe(true);

      const rows = wrapper.findAll('tr');
      expect(rows.length).toBeGreaterThan(0);

      // 检查表头
      expect(wrapper.text()).toContain('行政相对人名称');
      expect(wrapper.text()).toContain('行政相对人统一社会信用代码');
      expect(wrapper.text()).toContain('法定代表人姓名');
      expect(wrapper.text()).toContain('案件名称');
      expect(wrapper.text()).toContain('决定书文号');
      expect(wrapper.text()).toContain('处罚事由');
      expect(wrapper.text()).toContain('处罚类型');
      expect(wrapper.text()).toContain('处罚状态');
      expect(wrapper.text()).toContain('处罚结果');
      expect(wrapper.text()).toContain('处罚决定日期');
      expect(wrapper.text()).toContain('执法依据');
      expect(wrapper.text()).toContain('作出处罚决定部门');
      expect(wrapper.text()).toContain('国税/地税');
      expect(wrapper.text()).toContain('省份');
    });
  });

  describe('关联案件组件', () => {
    test('应该渲染关联案件组件', () => {
      const wrapper = createWrapper();

      const relateCasesComponent = wrapper.findComponent(mockQRelateCases);
      expect(relateCasesComponent.exists()).toBe(true);
      expect(relateCasesComponent.props('searchParams')).toEqual(defaultViewData);
      expect(relateCasesComponent.props('stitle')).toBe('关联');
    });
  });

  describe('表格布局', () => {
    test('应该有正确的列宽设置', () => {
      const wrapper = createWrapper();

      // 检查特定的宽度设置
      const cells = wrapper.findAll('td.tb');
      expect(cells.length).toBeGreaterThan(0);

      // 检查是否有23%宽度的单元格
      const widthCells = wrapper.findAll('td[width="23%"]');
      expect(widthCells.length).toBeGreaterThan(0);
    });

    test('应该有正确的colspan设置', () => {
      const wrapper = createWrapper();

      // 检查colspan="3"的单元格
      const colspanCells = wrapper.findAll('td[colspan="3"]');
      expect(colspanCells.length).toBeGreaterThan(0);
    });
  });

  describe('数据完整性', () => {
    test('应该处理部分数据缺失的情况', () => {
      const partialViewData = {
        CompanyName: '企业名称',
        CaseName: '案件名称',
        Oper: { KeyNo: '', Name: '' }, // 提供空对象避免模板错误
        // 其他字段缺失
      };

      const wrapper = createWrapper(partialViewData);

      expect(wrapper.text()).toContain('企业名称');
      expect(wrapper.text()).toContain('案件名称');
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理所有必要字段都存在的情况', () => {
      const wrapper = createWrapper();

      // 验证所有重要信息都被显示
      expect(wrapper.text()).toContain(defaultViewData.CompanyName);
      expect(wrapper.text()).toContain(defaultViewData.CaseName);
      expect(wrapper.text()).toContain(defaultViewData.CaseNo);
      expect(wrapper.text()).toContain(defaultViewData.Reason);
      expect(wrapper.text()).toContain(defaultViewData.Type);
      expect(wrapper.text()).toContain(defaultViewData.Status);
      expect(wrapper.text()).toContain(defaultViewData.Result);
      expect(wrapper.text()).toContain(defaultViewData.Basis);
      expect(wrapper.text()).toContain(defaultViewData.IssuedBy);
      expect(wrapper.text()).toContain(defaultViewData.Level);
      expect(wrapper.text()).toContain(defaultViewData.Province);
    });
  });
});
