import { shallowMount } from '@vue/test-utils';

import OtherPunish from '../index.vue';

// Mock q-entity-link component
const mockQEntityLink = {
  name: 'QEntityLink',
  template: '<a>{{ coyObj.Name }}</a>',
  props: ['coyObj'],
};

// Mock q-relate-cases component
const mockQRelateCases = {
  name: 'QRelateCases',
  template: '<div>关联案件</div>',
  props: ['searchParams', 'stitle'],
};

describe('OtherPunish', () => {
  const defaultViewData = {
    CaseNo: 'OTHER-001',
    OwnerInfo: {
      KeyNo: 'owner-key-123',
      Name: '被处罚企业名称',
    },
    CaseName: '违法案件名称',
    Category: '环保处罚',
    CreditCode: '91110000123456789X',
    Unit: '环保局',
    JudgeDate: '2023-01-01',
    Basis: '环保法第XX条',
    Reason: '违法排放污染物',
    Result: '罚款10万元',
    FilePath: 'https://example.com/penalty-doc.pdf',
  };

  const createWrapper = (viewData = defaultViewData, options = {}) => {
    return shallowMount(OtherPunish, {
      propsData: {
        viewData,
      },
      stubs: {
        'q-entity-link': mockQEntityLink,
        'q-relate-cases': mockQRelateCases,
      },
      ...options,
    });
  };

  describe('基本渲染', () => {
    test('应该正确渲染基本信息', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('OTHER-001');
      expect(wrapper.text()).toContain('违法案件名称');
      expect(wrapper.text()).toContain('环保处罚');
      expect(wrapper.text()).toContain('91110000123456789X');
      expect(wrapper.text()).toContain('环保局');
      expect(wrapper.text()).toContain('环保法第XX条');
      expect(wrapper.text()).toContain('违法排放污染物');
      expect(wrapper.text()).toContain('罚款10万元');
    });

    test('应该正确格式化日期', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('2023-01-01');
    });

    test('应该显示行政相对人链接', () => {
      const wrapper = createWrapper();

      const entityLinkComponent = wrapper.findComponent(mockQEntityLink);
      expect(entityLinkComponent.exists()).toBe(true);
      expect(entityLinkComponent.props('coyObj')).toEqual({
        KeyNo: 'owner-key-123',
        Name: '被处罚企业名称',
      });
    });

    test('应该显示原文链接', () => {
      const wrapper = createWrapper();

      const link = wrapper.find('a[target="_blank"]');
      expect(link.exists()).toBe(true);
      expect(link.attributes('href')).toBe('https://example.com/penalty-doc.pdf');
      expect(link.text()).toContain('详情');
    });
  });

  describe('条件渲染 - FilePath vs Content', () => {
    test('当有FilePath时应该显示原文链接', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('查看原文');
      const link = wrapper.find('a[target="_blank"]');
      expect(link.exists()).toBe(true);
      expect(wrapper.text()).not.toContain('处罚文书明细');
    });

    test('当没有FilePath但有Content时应该显示处罚文书明细', () => {
      const viewDataWithContent = {
        ...defaultViewData,
        FilePath: null,
        Content: '详细的处罚文书内容',
      };

      const wrapper = createWrapper(viewDataWithContent);

      expect(wrapper.text()).toContain('处罚文书明细');
      expect(wrapper.text()).toContain('详细的处罚文书内容');
      expect(wrapper.text()).not.toContain('查看原文');
      const link = wrapper.find('a[target="_blank"]');
      expect(link.exists()).toBe(false);
    });

    test('当既没有FilePath也没有Content时都不显示', () => {
      const viewDataWithoutBoth = {
        ...defaultViewData,
        FilePath: null,
        Content: null,
      };

      const wrapper = createWrapper(viewDataWithoutBoth);

      expect(wrapper.text()).not.toContain('查看原文');
      expect(wrapper.text()).not.toContain('处罚文书明细');
    });
  });

  describe('空值处理', () => {
    test('应该正确处理空字段', () => {
      const emptyViewData = {
        CaseNo: '',
        OwnerInfo: { KeyNo: '', Name: '' }, // 提供空对象而不是null
        CaseName: '',
        Category: '',
        CreditCode: '',
        Unit: '',
        Basis: '',
        Reason: '',
        Result: '',
      };

      const wrapper = createWrapper(emptyViewData);

      expect(wrapper.text()).toContain('-');
    });

    test('应该正确处理undefined字段', () => {
      const wrapper = createWrapper({
        OwnerInfo: { KeyNo: '', Name: '' }, // 提供空对象避免模板错误
      });

      expect(wrapper.text()).toContain('-');
    });

    test('当OwnerInfo为空时应该正确处理', () => {
      const viewDataWithoutOwner = {
        ...defaultViewData,
        OwnerInfo: { KeyNo: '', Name: '' }, // 提供空对象而不是null
      };

      const wrapper = createWrapper(viewDataWithoutOwner);

      const entityLinkComponent = wrapper.findComponent(mockQEntityLink);
      expect(entityLinkComponent.props('coyObj')).toEqual({ KeyNo: '', Name: '' });
    });
  });

  describe('HTML内容渲染', () => {
    test('应该正确渲染HTML内容', () => {
      const viewDataWithHtml = {
        ...defaultViewData,
        Reason: '<strong>严重</strong>违法排放',
        Result: '<em>罚款</em>10万元',
        Content: '<p>详细内容</p>',
      };

      const wrapper = createWrapper(viewDataWithHtml);

      // 检查HTML是否被正确渲染
      expect(wrapper.html()).toContain('<strong>严重</strong>违法排放');
      expect(wrapper.html()).toContain('<em>罚款</em>10万元');
    });
  });

  describe('表格结构', () => {
    test('应该包含正确的表格结构', () => {
      const wrapper = createWrapper();

      const table = wrapper.find('table.ntable');
      expect(table.exists()).toBe(true);

      const rows = wrapper.findAll('tr');
      expect(rows.length).toBeGreaterThan(0);

      // 检查表头
      expect(wrapper.text()).toContain('行政处罚决定书文号');
      expect(wrapper.text()).toContain('行政相对人名称');
      expect(wrapper.text()).toContain('项目名称');
      expect(wrapper.text()).toContain('处罚类型');
      expect(wrapper.text()).toContain('行政相对人统一社会信用代码');
      expect(wrapper.text()).toContain('处罚单位');
      expect(wrapper.text()).toContain('处罚决定日期');
      expect(wrapper.text()).toContain('处罚依据');
      expect(wrapper.text()).toContain('处罚事由');
      expect(wrapper.text()).toContain('处罚结果');
    });
  });

  describe('关联案件组件', () => {
    test('应该渲染关联案件组件', () => {
      const wrapper = createWrapper();

      const relateCasesComponent = wrapper.findComponent(mockQRelateCases);
      expect(relateCasesComponent.exists()).toBe(true);
      expect(relateCasesComponent.props('searchParams')).toEqual(defaultViewData);
      expect(relateCasesComponent.props('stitle')).toBe('关联');
    });
  });

  describe('组件名称', () => {
    test('应该有正确的组件名称', () => {
      const wrapper = createWrapper();

      expect(wrapper.vm.$options.name).toBe('otherPunish');
    });
  });

  describe('指令测试', () => {
    test('应该包含v-entity-click指令', () => {
      const wrapper = createWrapper();

      // 查找包含v-entity-click指令的元素
      const reasonCell = wrapper.find('td[v-entity-click]');
      // 由于shallowMount可能不会完全渲染指令，我们检查HTML结构
      expect(wrapper.html()).toContain('违法排放污染物');
    });
  });
});
