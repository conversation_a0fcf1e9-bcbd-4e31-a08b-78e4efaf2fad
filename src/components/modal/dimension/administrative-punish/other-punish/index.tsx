import { defineComponent, PropType } from 'vue';

import { dateFormat } from '@/utils/format';
import QEntityLink from '@/components/global/q-entity-link';
import QRelateCases from '@/components/global/q-relate-cases';

import styles from './index.module.less';

interface ViewData {
  CaseNo?: string;
  CaseName?: string;
  Category?: string;
  CreditCode?: string;
  Unit?: string;
  JudgeDate?: string;
  Basis?: string;
  Reason?: string;
  Result?: string;
  FilePath?: string | null;
  Content?: string | null;
  OwnerInfo?: {
    KeyNo?: string;
    Name?: string;
  } | null;
}

const OtherPunish = defineComponent({
  name: 'otherPunish',

  props: {
    viewData: {
      type: Object as PropType<ViewData>,
      default: () => ({}),
    },
  },

  setup() {
    const formatDate = (date?: string): string => {
      return dateFormat(date, { pattern: 'YYYY-MM-DD' });
    };

    return {
      formatDate,
    };
  },

  render() {
    const { viewData } = this;

    return (
      <div>
        <table class="ntable">
          <tr>
            <td class="tb" width="22%">
              行政处罚决定书文号
            </td>
            <td width="28%">{viewData.CaseNo || '-'}</td>
            <td class="tb" width="22%">
              行政相对人名称
            </td>
            <td width="28%">
              <QEntityLink coyObj={viewData.OwnerInfo} />
            </td>
          </tr>
          <tr>
            <td class="tb" width="">
              项目名称
            </td>
            <td width="">{viewData.CaseName || '-'}</td>
            <td class="tb">处罚类型</td>
            <td>{viewData.Category || '-'}</td>
          </tr>
          <tr>
            <td class="tb">行政相对人统一社会信用代码</td>
            <td>{viewData.CreditCode || '-'}</td>
            <td class="tb">处罚单位</td>
            <td>{viewData.Unit || '-'}</td>
          </tr>
          <tr>
            <td class="tb" width="">
              处罚决定日期
            </td>
            <td>{this.formatDate(viewData.JudgeDate)}</td>
            <td class="tb" width="">
              处罚依据
            </td>
            <td>{viewData.Basis || '-'}</td>
          </tr>
          <tr>
            <td class="tb">处罚事由</td>
            <td colspan="3" domPropsInnerHTML={viewData.Reason} v-entity-click></td>
          </tr>
          <tr>
            <td class="tb">处罚结果</td>
            <td colspan="3" domPropsInnerHTML={viewData.Result || '-'}></td>
          </tr>
          {viewData.FilePath ? (
            <tr>
              <td class="tb">查看原文</td>
              <td colspan="3">
                <a target="_blank" rel="nofollow" href={viewData.FilePath}>
                  详情
                </a>
              </td>
            </tr>
          ) : viewData.Content ? (
            <tr>
              <td class="tb">处罚文书明细</td>
              <td colspan="3" domPropsInnerHTML={viewData.Content}></td>
            </tr>
          ) : null}
        </table>
        <QRelateCases searchParams={viewData} stitle="关联" />
      </div>
    );
  },
});

export default OtherPunish;
