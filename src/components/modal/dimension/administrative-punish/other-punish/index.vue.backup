<template>
  <div>
    <table class="ntable">
      <tr>
        <td class="tb" width="22%">行政处罚决定书文号</td>
        <td width="28%">{{ viewData.CaseNo || '-' }}</td>
        <td class="tb" width="22%">行政相对人名称</td>
        <td width="28%">
          <q-entity-link :coy-obj="viewData.OwnerInfo"></q-entity-link>
        </td>
      </tr>
      <tr>
        <td class="tb" width="">项目名称</td>
        <td width="">{{ viewData.CaseName || '-' }}</td>
        <td class="tb">处罚类型</td>
        <td>{{ viewData.Category || '-' }}</td>
      </tr>
      <tr>
        <td class="tb">行政相对人统一社会信用代码</td>
        <td>{{ viewData.CreditCode || '-' }}</td>
        <td class="tb">处罚单位</td>
        <td>{{ viewData.Unit || '-' }}</td>
      </tr>
      <tr>
        <td class="tb" width="">处罚决定日期</td>
        <td>{{ viewData.JudgeDate | dateformat('YYYY-MM-DD') }}</td>
        <td class="tb" width="">处罚依据</td>
        <td>{{ viewData.Basis || '-' }}</td>
      </tr>
      <tr>
        <td class="tb">处罚事由</td>
        <td colspan="3" v-html="viewData.Reason" v-entity-click></td>
      </tr>
      <tr>
        <td class="tb">处罚结果</td>
        <td colspan="3" v-html="viewData.Result || '-'"></td>
      </tr>
      <tr v-if="viewData.FilePath">
        <td class="tb">查看原文</td>
        <td colspan="3">
          <a v-if="viewData.FilePath" target="_blank" rel="nofollow" :href="viewData.FilePath">详情</a>
        </td>
      </tr>
      <tr v-else-if="viewData.Content">
        <td class="tb">处罚文书明细</td>
        <td colspan="3" v-html="viewData.Content"></td>
      </tr>
    </table>
    <q-relate-cases :search-params="viewData" stitle="关联"></q-relate-cases>
  </div>
</template>
<script>
export default {
  name: 'otherPunish',
  props: {
    viewData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
};
</script>
