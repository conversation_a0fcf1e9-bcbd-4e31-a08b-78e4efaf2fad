import { shallowMount } from '@vue/test-utils';

import AdministrativePunish from '../index.vue';

// Mock q-source-logo component
const mockQSourceLogo = {
  name: 'QSourceLogo',
  template: '<a :href="url">{{ label }}</a>',
  props: ['url', 'label'],
};

// Mock q-relate-cases component
const mockQRelateCases = {
  name: 'QRelateCases',
  template: '<div>关联案件</div>',
  props: ['searchParams', 'stitle'],
};

describe('AdministrativePunish', () => {
  const defaultViewData = {
    DocNo: 'TEST-DOC-001',
    PunishOffice: '测试处罚单位',
    PunishReason: '测试处罚事由',
    PunishResult: '测试处罚结果',
    PenaltyMoney: 10000,
    PunishBasis: '测试处罚依据',
    PunishDate: '2023-01-01',
    PublishDate: '2023-01-02',
    OssUrl: 'https://example.com/document.pdf',
  };

  const createWrapper = (viewData = defaultViewData, options = {}) => {
    return shallowMount(AdministrativePunish, {
      propsData: {
        viewData,
      },
      stubs: {
        'q-source-logo': mockQSourceLogo,
        'q-relate-cases': mockQRelateCases,
      },
      ...options,
    });
  };

  describe('基本渲染', () => {
    test('应该正确渲染基本信息', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('TEST-DOC-001');
      expect(wrapper.text()).toContain('测试处罚单位');
      expect(wrapper.text()).toContain('测试处罚事由');
      expect(wrapper.text()).toContain('测试处罚结果');
      expect(wrapper.text()).toContain('测试处罚依据');
    });

    test('应该正确显示处罚金额', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('10,000');
    });

    test('应该正确格式化日期', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('2023-01-01');
      expect(wrapper.text()).toContain('2023-01-02');
    });

    test('应该显示原文链接', () => {
      const wrapper = createWrapper();

      const sourceLogoComponent = wrapper.findComponent(mockQSourceLogo);
      expect(sourceLogoComponent.exists()).toBe(true);
      expect(sourceLogoComponent.props('url')).toBe('https://example.com/document.pdf');
      expect(sourceLogoComponent.props('label')).toBe('查看');
    });
  });

  describe('条件渲染', () => {
    test('当没有处罚金额时不应该显示处罚金额行', () => {
      const viewDataWithoutMoney = { ...defaultViewData };
      delete viewDataWithoutMoney.PenaltyMoney;

      const wrapper = createWrapper(viewDataWithoutMoney);

      expect(wrapper.text()).not.toContain('处罚金额（元）');
    });

    test('当OssUrl是txt文件时不应该显示原文链接', () => {
      const viewDataWithTxt = {
        ...defaultViewData,
        OssUrl: 'https://example.com/document.txt',
      };

      const wrapper = createWrapper(viewDataWithTxt);

      const sourceLogoComponent = wrapper.findComponent(mockQSourceLogo);
      expect(sourceLogoComponent.exists()).toBe(false);
    });

    test('当没有OssUrl时不应该显示原文链接', () => {
      const viewDataWithoutUrl = { ...defaultViewData };
      delete viewDataWithoutUrl.OssUrl;

      const wrapper = createWrapper(viewDataWithoutUrl);

      const sourceLogoComponent = wrapper.findComponent(mockQSourceLogo);
      expect(sourceLogoComponent.exists()).toBe(false);
    });
  });

  describe('处罚变更信息', () => {
    test('应该显示处罚变更信息表格', () => {
      const viewDataWithAltList = {
        ...defaultViewData,
        PunishmentAltList: [
          {
            Alt: '变更事项1',
            AltBefore: '变更前内容1',
            AltAfter: '变更后内容1',
            AltDate: '2023-02-01',
            PenAuth: '变更决定机关1',
          },
          {
            Alt: '变更事项2',
            AltBefore: '变更前内容2',
            AltAfter: '变更后内容2',
            AltDate: '2023-02-02',
            PenAuth: '变更决定机关2',
          },
        ],
      };

      const wrapper = createWrapper(viewDataWithAltList);

      expect(wrapper.text()).toContain('行政处罚变更信息');
      expect(wrapper.text()).toContain('变更事项1');
      expect(wrapper.text()).toContain('变更前内容1');
      expect(wrapper.text()).toContain('变更后内容1');
      expect(wrapper.text()).toContain('变更决定机关1');
      expect(wrapper.text()).toContain('变更事项2');
      expect(wrapper.text()).toContain('变更前内容2');
      expect(wrapper.text()).toContain('变更后内容2');
      expect(wrapper.text()).toContain('变更决定机关2');
    });

    test('当没有变更信息时不应该显示变更信息表格', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).not.toContain('行政处罚变更信息');
    });

    test('当变更信息为空数组时不应该显示变更信息表格', () => {
      const viewDataWithEmptyAltList = {
        ...defaultViewData,
        PunishmentAltList: [],
      };

      const wrapper = createWrapper(viewDataWithEmptyAltList);

      expect(wrapper.text()).not.toContain('行政处罚变更信息');
    });
  });

  describe('方法测试', () => {
    test('getMoney方法应该正确格式化金额', () => {
      const wrapper = createWrapper();
      const vm = wrapper.vm as any;

      expect(vm.getMoney(1000)).toBe('1,000');
      expect(vm.getMoney(1000000)).toBe('1,000,000');
      expect(vm.getMoney(null)).toBe('-');
      expect(vm.getMoney(undefined)).toBe('-');
      expect(vm.getMoney(0)).toBe('-'); // 0 在原组件中也被当作falsy值处理
    });
  });

  describe('空数据处理', () => {
    test('应该正确处理空的viewData', () => {
      const wrapper = createWrapper({});

      expect(wrapper.text()).toContain('-');
      expect(wrapper.exists()).toBe(true);
    });

    test('应该正确处理null的viewData', () => {
      const wrapper = createWrapper(null);

      expect(wrapper.text()).toBe('');
    });
  });

  describe('关联案件组件', () => {
    test('应该渲染关联案件组件', () => {
      const wrapper = createWrapper();

      const relateCasesComponent = wrapper.findComponent(mockQRelateCases);
      expect(relateCasesComponent.exists()).toBe(true);
      expect(relateCasesComponent.props('searchParams')).toEqual(defaultViewData);
      expect(relateCasesComponent.props('stitle')).toBe('关联');
    });
  });
});
