import { shallowMount } from '@vue/test-utils';

import GongshanPuni<PERSON> from '../index.vue';

// Mock q-entity-link component
const mockQEntityLink = {
  name: 'QEntityLink',
  template: '<a>{{ coyObj.Name }}</a>',
  props: ['coyObj'],
};

describe('GongshanPunish', () => {
  const defaultViewData = {
    keyNo: 'test-key-123',
    DocNo: 'GS-DOC-001',
    PenaltyType: '违法广告',
    Content: '罚款5000元',
    PenaltyDate: '2023-01-01',
    PublicDate: '2023-01-02',
    OfficeName: '测试工商局',
    Detail: {
      PersonName: '测试企业名称',
      AdPenaltyLink: 'penalty-doc-123.pdf',
    },
  };

  const createWrapper = (viewData = defaultViewData, options = {}) => {
    return shallowMount(<PERSON><PERSON><PERSON><PERSON><PERSON>, {
      propsData: {
        viewData,
      },
      stubs: {
        'q-entity-link': mockQEntityLink,
      },
      ...options,
    });
  };

  describe('基本渲染', () => {
    test('应该正确渲染基本信息', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('GS-DOC-001');
      expect(wrapper.text()).toContain('违法广告');
      expect(wrapper.text()).toContain('罚款5000元');
      expect(wrapper.text()).toContain('测试工商局');
    });

    test('应该正确格式化日期', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('2023-01-01');
      expect(wrapper.text()).toContain('2023-01-02');
    });

    test('应该显示行政相对人链接', () => {
      const wrapper = createWrapper();

      const entityLinkComponent = wrapper.findComponent(mockQEntityLink);
      expect(entityLinkComponent.exists()).toBe(true);
      expect(entityLinkComponent.props('coyObj')).toEqual({
        KeyNo: 'test-key-123',
        Name: '测试企业名称',
      });
    });

    test('应该显示行政处罚原文链接', () => {
      const wrapper = createWrapper();

      const link = wrapper.find('a[target="_blank"]');
      expect(link.exists()).toBe(true);
      expect(link.attributes('href')).toBe('https://img.qichacha.com/PenaltyDoc/penalty-doc-123.pdf');
      expect(link.text()).toContain('详情');
    });
  });

  describe('条件渲染', () => {
    test('当没有Detail.PersonName时应该显示"-"', () => {
      const viewDataWithoutPersonName = {
        ...defaultViewData,
        Detail: {},
      };

      const wrapper = createWrapper(viewDataWithoutPersonName);

      expect(wrapper.text()).toContain('-');
      const entityLinkComponent = wrapper.findComponent(mockQEntityLink);
      expect(entityLinkComponent.exists()).toBe(false);
    });

    test('当没有Detail时应该显示"-"', () => {
      const viewDataWithoutDetail = { ...defaultViewData };
      delete viewDataWithoutDetail.Detail;

      const wrapper = createWrapper(viewDataWithoutDetail);

      expect(wrapper.text()).toContain('-');
      const entityLinkComponent = wrapper.findComponent(mockQEntityLink);
      expect(entityLinkComponent.exists()).toBe(false);
    });

    test('当没有AdPenaltyLink时不应该显示原文链接', () => {
      const viewDataWithoutLink = {
        ...defaultViewData,
        Detail: {
          PersonName: '测试企业名称',
        },
      };

      const wrapper = createWrapper(viewDataWithoutLink);

      const link = wrapper.find('a[target="_blank"]');
      expect(link.exists()).toBe(false);
      expect(wrapper.text()).not.toContain('行政处罚原文');
    });

    test('当Detail为null时不应该显示原文链接', () => {
      const viewDataWithNullDetail = {
        ...defaultViewData,
        Detail: null,
      };

      const wrapper = createWrapper(viewDataWithNullDetail);

      const link = wrapper.find('a[target="_blank"]');
      expect(link.exists()).toBe(false);
    });
  });

  describe('处罚变更信息', () => {
    test('应该显示处罚变更信息表格', () => {
      const viewDataWithAltList = {
        ...defaultViewData,
        PunishmentAltList: [
          {
            Alt: '变更事项1',
            AltBefore: '变更前内容1',
            AltAfter: '变更后内容1',
            AltDate: '2023-02-01',
            PenAuth: '变更决定机关1',
          },
        ],
      };

      const wrapper = createWrapper(viewDataWithAltList);

      expect(wrapper.text()).toContain('行政处罚变更信息');
      expect(wrapper.text()).toContain('变更事项1');
      expect(wrapper.text()).toContain('变更前内容1');
      expect(wrapper.text()).toContain('变更后内容1');
      expect(wrapper.text()).toContain('变更决定机关1');
    });

    test('当没有变更信息时不应该显示变更信息表格', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).not.toContain('行政处罚变更信息');
    });

    test('当变更信息为空数组时不应该显示变更信息表格', () => {
      const viewDataWithEmptyAltList = {
        ...defaultViewData,
        PunishmentAltList: [],
      };

      const wrapper = createWrapper(viewDataWithEmptyAltList);

      expect(wrapper.text()).not.toContain('行政处罚变更信息');
    });
  });

  describe('空值处理', () => {
    test('应该正确处理空字段', () => {
      const emptyViewData = {
        keyNo: '',
        DocNo: '',
        PenaltyType: '',
        Content: '',
        OfficeName: '',
        Detail: null,
      };

      const wrapper = createWrapper(emptyViewData);

      expect(wrapper.text()).toContain('-');
      expect(wrapper.text()).toContain('未公示');
    });

    test('应该正确处理undefined字段', () => {
      const wrapper = createWrapper({});

      expect(wrapper.text()).toContain('-');
      expect(wrapper.text()).toContain('未公示');
    });
  });

  describe('HTML内容渲染', () => {
    test('应该正确渲染HTML内容', () => {
      const viewDataWithHtml = {
        ...defaultViewData,
        Content: '<strong>罚款</strong>5000元',
      };

      const wrapper = createWrapper(viewDataWithHtml);

      // 检查HTML是否被正确渲染
      expect(wrapper.html()).toContain('<strong>罚款</strong>5000元');
    });
  });

  describe('表格结构', () => {
    test('应该包含正确的表格结构', () => {
      const wrapper = createWrapper();

      const table = wrapper.find('table.ntable');
      expect(table.exists()).toBe(true);

      const rows = wrapper.findAll('tr');
      expect(rows.length).toBeGreaterThan(0);

      // 检查表头
      expect(wrapper.text()).toContain('行政相对人');
      expect(wrapper.text()).toContain('决定书文号');
      expect(wrapper.text()).toContain('处罚事由/违法行为类型');
      expect(wrapper.text()).toContain('处罚结果/内容');
      expect(wrapper.text()).toContain('处罚决定日期');
      expect(wrapper.text()).toContain('公示日期');
      expect(wrapper.text()).toContain('处罚机关');
    });
  });
});
