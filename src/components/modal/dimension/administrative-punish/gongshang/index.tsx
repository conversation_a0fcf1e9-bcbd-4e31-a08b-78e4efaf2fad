import { defineComponent, PropType } from 'vue';

import { dateFormat } from '@/utils/format';
import QEntityLink from '@/components/global/q-entity-link';

import styles from './index.module.less';

interface ViewData {
  keyNo?: string;
  DocNo?: string;
  PenaltyType?: string;
  Content?: string;
  PenaltyDate?: string;
  PublicDate?: string;
  OfficeName?: string;
  Detail?: {
    PersonName?: string;
    AdPenaltyLink?: string;
  } | null;
  PunishmentAltList?: Array<{
    Alt?: string;
    AltBefore?: string;
    AltAfter?: string;
    AltDate?: string;
    PenAuth?: string;
  }>;
}

const GongshanPunish = defineComponent({
  name: '<PERSON><PERSON>Punish',
  
  props: {
    viewData: {
      type: Object as PropType<ViewData>,
      default: () => ({}),
    },
  },

  setup() {
    const formatDate = (date?: string): string => {
      return dateFormat(date, { pattern: 'YYYY-MM-DD' });
    };

    return {
      formatDate,
    };
  },

  render() {
    const { viewData } = this;

    return (
      <div>
        <table class="ntable">
          <tr>
            <td class="tb" width="20%">行政相对人</td>
            <td width="30%">
              {viewData.Detail && viewData.Detail.PersonName ? (
                <span class="val">
                  <QEntityLink coyObj={{ KeyNo: viewData.keyNo, Name: viewData.Detail.PersonName }} />
                </span>
              ) : (
                <span>-</span>
              )}
            </td>
            <td class="tb" width="20%">决定书文号</td>
            <td width="30%">{viewData.DocNo || '-'}</td>
          </tr>
          <tr>
            <td class="tb" width="20%">处罚事由/违法行为类型</td>
            <td width="30%">{viewData.PenaltyType || '-'}</td>
            <td class="tb" width="20%">处罚结果/内容</td>
            <td width="30%" domPropsInnerHTML={viewData.Content || '未公示'}></td>
          </tr>
          <tr>
            <td class="tb" width="20%">处罚决定日期</td>
            <td width="30%">{this.formatDate(viewData.PenaltyDate)}</td>
            <td class="tb" width="20%">公示日期</td>
            <td width="30%">{this.formatDate(viewData.PublicDate)}</td>
          </tr>
          <tr>
            <td class="tb">处罚机关</td>
            <td colspan="5">{viewData.OfficeName || '-'}</td>
          </tr>
          {viewData.Detail && viewData.Detail.AdPenaltyLink && (
            <tr>
              <td class="tb">行政处罚原文</td>
              <td colspan="5">
                <a 
                  target="_blank" 
                  href={`https://img.qichacha.com/PenaltyDoc/${viewData.Detail.AdPenaltyLink}`}
                >
                  详情
                </a>
              </td>
            </tr>
          )}
        </table>

        {/* 补充变更信息 */}
        {viewData.PunishmentAltList && viewData.PunishmentAltList.length > 0 && (
          <div class="punishment-alt-relat">
            <div class="tcaption">行政处罚变更信息</div>
            <table class="ntable ntable-odd">
              <tr>
                <th class="tx">序号</th>
                <th>变更事项</th>
                <th>变更前内容</th>
                <th>变更后内容</th>
                <th>变更日期</th>
                <th>作出变更决定机关</th>
              </tr>
              {viewData.PunishmentAltList.map((item, index) => (
                <tr key={index}>
                  <td class="tx">{index + 1}</td>
                  <td width="20%" class="q-text-center" domPropsInnerHTML={item.Alt || '-'}></td>
                  <td width="20%" class="q-text-center" domPropsInnerHTML={item.AltBefore || '-'}></td>
                  <td width="20%" class="q-text-center" domPropsInnerHTML={item.AltAfter || '-'}></td>
                  <td width="15%" class="q-text-center">{this.formatDate(item.AltDate)}</td>
                  <td width="25%" class="q-text-center" domPropsInnerHTML={item.PenAuth || '-'}></td>
                </tr>
              ))}
            </table>
          </div>
        )}
      </div>
    );
  },
});

export default GongshanPunish;
