<template>
  <div>
    <table class="ntable">
      <tr>
        <td class="tb" width="20%">行政相对人</td>
        <td width="30%">
          <span v-if="viewData.Detail && viewData.Detail.PersonName" class="val">
            <q-entity-link :coy-obj="{ KeyNo: viewData.keyNo, Name: viewData.Detail.PersonName }"></q-entity-link>
            <!-- {{viewData.Detail.PersonName}} -->
          </span>
          <span v-else>-</span>
        </td>
        <td class="tb" width="20%">决定书文号</td>
        <td width="30%">{{ viewData.DocNo || '-' }}</td>
      </tr>
      <tr>
        <td class="tb" width="20%">处罚事由/违法行为类型</td>
        <td width="30%">{{ viewData.PenaltyType || '-' }}</td>
        <td class="tb" width="20%">处罚结果/内容</td>
        <td width="30%" v-html="viewData.Content || '未公示'"></td>
      </tr>
      <tr>
        <td class="tb" width="20%">处罚决定日期</td>
        <td width="30%">{{ viewData.PenaltyDate | dateformat('YYYY-MM-DD') }}</td>
        <td class="tb" width="20%">公示日期</td>
        <td width="30%">{{ viewData.PublicDate | dateformat('YYYY-MM-DD') }}</td>
      </tr>
      <tr>
        <td class="tb">处罚机关</td>
        <td colspan="5">{{ viewData.OfficeName || '-' }}</td>
      </tr>
      <tr v-if="viewData.Detail && viewData.Detail.AdPenaltyLink">
        <td class="tb">行政处罚原文</td>
        <td colspan="5">
          <a target="_blank" :href="`https://img.qichacha.com/PenaltyDoc/${viewData.Detail.AdPenaltyLink}`"> 详情 </a>
        </td>
      </tr>
    </table>
    <!-- 补充变更信息 -->
    <div class="punishment-alt-relat" v-if="viewData.PunishmentAltList && viewData.PunishmentAltList.length > 0">
      <div class="tcaption">行政处罚变更信息</div>
      <table class="ntable ntable-odd">
        <tr>
          <th class="tx">序号</th>
          <th>变更事项</th>
          <th>变更前内容</th>
          <th>变更后内容</th>
          <th>变更日期</th>
          <th>作出变更决定机关</th>
        </tr>
        <tr v-for="(item, index) in viewData.PunishmentAltList" :key="index">
          <td class="tx">{{ index + 1 }}</td>
          <td width="20%" class="q-text-center" v-html="item.Alt || '-'"></td>
          <td width="20%" class="q-text-center" v-html="item.AltBefore || '-'"></td>
          <td width="20%" class="q-text-center" v-html="item.AltAfter || '-'"></td>
          <td width="15%" class="q-text-center">{{ item.AltDate | dateformat('YYYY-MM-DD') }}</td>
          <td width="25%" class="q-text-center" v-html="item.PenAuth || '-'"></td>
        </tr>
      </table>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    viewData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
};
</script>
