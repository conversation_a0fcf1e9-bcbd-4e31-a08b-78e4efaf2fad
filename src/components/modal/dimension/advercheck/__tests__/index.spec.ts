import { shallowMount } from '@vue/test-utils';

import AdverCheck from '../index.tsx';

describe('AdverCheck', () => {
  let wrapper: any;
  let mockOptions: any;

  beforeEach(() => {
    mockOptions = {
      propsData: {
        viewData: {
          AuthorityLis: [{ KeyNo: 'c123', Name: '审查机关' }],
          ApprovalNumber: 'AD123456',
          AdvertType: '医疗广告',
          AdvertTime: '30',
          ApplyList: [{ KeyNo: 'c456', Name: '申请人' }],
          Address: '北京市朝阳区',
          ContentOssId: 'https://example.com/image.jpg',
          ProductType: 1,
          ProductName: '药品名称',
          BrandName: '品牌名称',
          No: 'NO123456',
          AuditDate: '2023-01-01',
        },
      },
    };
  });

  const createWrapper = (propsData: any = {}, options: any = {}) => {
    const mergedOptions = {
      ...mockOptions,
      propsData: {
        ...mockOptions.propsData,
        ...propsData,
      },
      ...options,
    };

    wrapper = shallowMount(AdverCheck, mergedOptions);
    return wrapper;
  };

  afterEach(() => {
    if (wrapper) {
      wrapper.destroy();
    }
  });

  describe('基本渲染', () => {
    test('应该正确渲染基本信息', () => {
      const wrapper = createWrapper();

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('广告审查机关');
      expect(wrapper.text()).toContain('广告批准文号');
      expect(wrapper.text()).toContain('AD123456');
    });

    test('应该正确显示广告类别和时长', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('医疗广告');
      expect(wrapper.text()).toContain('30');
    });

    test('应该正确显示申请人信息', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('广告申请人名称');
      expect(wrapper.text()).toContain('广告申请人地址');
      expect(wrapper.text()).toContain('北京市朝阳区');
    });
  });

  describe('产品类型处理', () => {
    test('当ProductType为1时应该显示商品名称', () => {
      const wrapper = createWrapper({
        viewData: { ProductType: 1 }
      });

      expect(wrapper.text()).toContain('商品名称');
    });

    test('当ProductType不为1时应该显示产品名称', () => {
      const wrapper = createWrapper({
        viewData: { ProductType: 2 }
      });

      expect(wrapper.text()).toContain('产品名称');
    });
  });

  describe('getNoTh方法', () => {
    test('应该根据ProductType返回正确的标题', () => {
      const wrapper = createWrapper();

      expect(wrapper.vm.getNoTh(1)).toBe('药品批准文号');
      expect(wrapper.vm.getNoTh(2)).toBe('医疗器械注册证编号');
      expect(wrapper.vm.getNoTh(3)).toBe('保健食品注册号');
      expect(wrapper.vm.getNoTh(4)).toBe('特殊医学用途配方食品注册号');
    });
  });

  describe('条件渲染', () => {
    test('当ContentOssId存在时应该显示广告发布内容', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('广告发布内容');
      expect(wrapper.find('img').exists()).toBe(true);
    });

    test('当ContentOssId不存在时不应该显示广告发布内容', () => {
      const wrapper = createWrapper({
        viewData: { ContentOssId: null }
      });

      expect(wrapper.text()).not.toContain('广告发布内容');
      expect(wrapper.find('img').exists()).toBe(false);
    });

    test('当AuthorityLis为空时应该显示-', () => {
      const wrapper = createWrapper({
        viewData: { AuthorityLis: null }
      });

      // 检查是否有默认的"-"显示
      const cells = wrapper.findAll('td');
      expect(cells.length).toBeGreaterThan(0);
    });
  });

  describe('边界情况', () => {
    test('应该处理空的viewData', () => {
      const wrapper = createWrapper({ viewData: {} });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理undefined的字段', () => {
      const wrapper = createWrapper({
        viewData: {
          ApprovalNumber: undefined,
          AdvertType: undefined,
          AdvertTime: undefined,
        }
      });

      expect(wrapper.exists()).toBe(true);
    });
  });
});
