import { defineComponent, PropType } from 'vue';

import QEntityLink from '@/components/global/q-entity-link';
import QPlainTable from '@/components/global/q-plain-table';
import dateFormat from '@/utils/format/date';

interface ViewData {
  AuthorityLis?: Array<{
    KeyNo?: string;
    Name?: string;
  }>;
  ApprovalNumber?: string;
  AdvertType?: string;
  AdvertTime?: string;
  ApplyList?: Array<{
    KeyNo?: string;
    Name?: string;
  }>;
  Address?: string;
  ContentOssId?: string;
  ProductType?: number;
  ProductName?: string;
  BrandName?: string;
  No?: string;
  AuditDate?: string;
}

const AdverCheck = defineComponent({
  name: 'AdverCheck',

  props: {
    viewData: {
      type: Object as PropType<ViewData>,
      default: () => ({}),
    },
  },

  setup() {
    const getNoTh = (type?: number): string => {
      switch (type) {
        case 1:
          return '药品批准文号';
        case 2:
          return '医疗器械注册证编号';
        case 3:
          return '保健食品注册号';
        default:
          return '特殊医学用途配方食品注册号';
      }
    };

    return {
      getNoTh,
    };
  },

  render() {
    const { viewData } = this;

    return (
      <QPlainTable>
        <tbody>
          <tr>
            <th width="23%">广告审查机关</th>
            <td width="27%">{viewData.AuthorityLis ? <QEntityLink coyArr={viewData.AuthorityLis} /> : <span>-</span>}</td>
            <th width="23%">广告批准文号</th>
            <td width="27%">{viewData.ApprovalNumber || '-'}</td>
          </tr>
          <tr>
            <th width="23%">广告类别</th>
            <td width="27%">{viewData.AdvertType || '-'}</td>
            <th width="23%">广告时长（秒）</th>
            <td width="27%">{viewData.AdvertTime || '-'}</td>
          </tr>
          <tr>
            <th>广告申请人名称</th>
            <td colspan="5">{viewData.ApplyList ? <QEntityLink coyArr={viewData.ApplyList} /> : <span>-</span>}</td>
          </tr>
          <tr>
            <th>广告申请人地址</th>
            <td colspan="5">{viewData.Address || '-'}</td>
          </tr>
          {viewData.ContentOssId && (
            <tr>
              <th>广告发布内容</th>
              <td colspan="5">
                <a href={viewData.ContentOssId} target="_blank">
                  <img src={viewData.ContentOssId} width="110px" height="60px" />
                </a>
              </td>
            </tr>
          )}
          <tr>
            <th width="23%">{viewData.ProductType === 1 ? '商品名称' : '产品名称'}</th>
            <td width="27%">{viewData.ProductName || '-'}</td>
            <th width="23%">品牌名称</th>
            <td width="27%">{viewData.BrandName || '-'}</td>
          </tr>
          <tr>
            <th width="23%">{this.getNoTh(viewData.ProductType)}</th>
            <td width="27%">{viewData.No || '-'}</td>
            <th width="23%">广告审批日期</th>
            <td width="27%">{dateFormat(viewData.AuditDate)}</td>
          </tr>
        </tbody>
      </QPlainTable>
    );
  },
});

export default AdverCheck;
