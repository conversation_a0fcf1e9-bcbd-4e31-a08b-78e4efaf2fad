import { defineComponent, PropType } from 'vue';

import dimensionMixin from '@/shared/mixins/dimension.mixin';
import { createMixin } from '@/shared/mixins/list.mixin';
import tenderMixin from '@/shared/mixins/tender.mixin';
import { TENDER_TYPE } from '@/utils/search-transform/tender/filter';

import styles from './index.module.less';

interface DetailParams {
  [key: string]: any;
}

const listMixin = createMixin(['current']);

const Competitor = defineComponent({
  name: 'Competitor',

  mixins: [dimensionMixin, listMixin, tenderMixin],

  props: {
    detailParams: {
      type: Object as PropType<DetailParams>,
      default: () => ({}),
    },
  },

  data() {
    return {
      TENDER_TYPE,
    };
  },

  computed: {
    columns() {
      return this.mTenderGenColumns('all');
    },
  },

  methods: {
    fetchDataSource({ pagination }: { pagination: any }) {
      return this.mDimenGetList(
        {
          ...this.detailParams,
          ...pagination,
        },
        'competitor',
        'dimensionDetail'
      );
    },
  },

  render() {
    return (
      <q-rich-table
        v-data={this.mListGetTableData('current')}
        columns={this.mTenderGenColumns(TENDER_TYPE.all)}
        scopedSlots={{
          keyNoTenderee: (record: any) => <q-entity-link coy-arr={record.KeyNoTendereeObj} />,
        }}
      />
    );
  },
});

export default Competitor;
