import { shallowMount } from '@vue/test-utils';

import Competitor from '../index.tsx';

describe('Competitor', () => {
  let wrapper: any;
  let mockOptions: any;

  beforeEach(() => {
    mockOptions = {
      propsData: {
        detailParams: {
          keyNo: 'TEST123',
          companyName: '测试企业'
        }
      },
      stubs: {
        'q-rich-table': true,
        'q-entity-link': true
      },
      data() {
        return {
          info: {
            isRiskScan: false,
            isValid: 0
          }
        };
      },
      methods: {
        mListGetTableData: vi.fn(() => ({
          dataSource: [
            {
              KeyNoTendereeObj: [{ Name: '竞争对手1', KeyNo: 'COMP1' }],
              TenderTitle: '测试招标项目1',
              TenderAmount: 1000000
            },
            {
              KeyNoTendereeObj: [{ Name: '竞争对手2', KeyNo: 'COMP2' }],
              TenderTitle: '测试招标项目2',
              TenderAmount: 2000000
            }
          ],
          pagination: {
            current: 1,
            pageSize: 10,
            total: 2
          }
        })),
        mTenderGenColumns: vi.fn(() => [
          {
            title: '中标人',
            dataIndex: 'keyNoTenderee',
            key: 'keyNoTenderee'
          },
          {
            title: '招标项目',
            dataIndex: 'TenderTitle',
            key: 'TenderTitle'
          },
          {
            title: '中标金额',
            dataIndex: 'TenderAmount',
            key: 'TenderAmount'
          }
        ]),
        mDimenGetList: vi.fn(() => Promise.resolve({
          data: {
            dataSource: [],
            pagination: { current: 1, pageSize: 10, total: 0 }
          }
        }))
      }
    };
  });

  const createWrapper = (propsData: any = {}, options: any = {}) => {
    const mergedOptions = {
      ...mockOptions,
      propsData: {
        ...mockOptions.propsData,
        ...propsData,
      },
      ...options,
    };

    wrapper = shallowMount(Competitor, mergedOptions);
    return wrapper;
  };

  afterEach(() => {
    if (wrapper) {
      wrapper.destroy();
    }
  });

  describe('基本渲染', () => {
    test('应该正确渲染组件', () => {
      const wrapper = createWrapper();

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.find('q-rich-table-stub').exists()).toBe(true);
    });

    test('应该传递正确的数据给表格', () => {
      const wrapper = createWrapper();

      const table = wrapper.find('q-rich-table-stub');
      expect(table.exists()).toBe(true);
      // 检查表格数据是否正确传递
      expect(typeof wrapper.vm.mListGetTableData).toBe('function');
    });

    test('应该传递正确的列配置给表格', () => {
      const wrapper = createWrapper();

      const table = wrapper.find('q-rich-table-stub');
      expect(table.exists()).toBe(true);
      // 检查列配置是否正确传递
      expect(typeof wrapper.vm.mTenderGenColumns).toBe('function');
    });
  });

  describe('组件属性', () => {
    test('应该正确接收detailParams属性', () => {
      const testParams = {
        keyNo: 'TEST456',
        companyName: '测试公司'
      };
      const wrapper = createWrapper({ detailParams: testParams });

      expect(wrapper.vm.detailParams).toEqual(testParams);
    });

    test('应该有默认的detailParams', () => {
      const wrapper = createWrapper({ detailParams: undefined });

      expect(wrapper.vm.detailParams).toEqual({});
    });
  });

  describe('数据获取', () => {
    test('应该调用正确的数据获取方法', async () => {
      const wrapper = createWrapper();

      // 检查fetchDataSource方法存在
      expect(typeof wrapper.vm.fetchDataSource).toBe('function');

      // 检查mDimenGetList方法存在
      expect(typeof wrapper.vm.mDimenGetList).toBe('function');
    });

    test('应该合并detailParams和pagination参数', async () => {
      const testParams = {
        keyNo: 'TEST789',
        companyName: '测试企业'
      };
      const wrapper = createWrapper({ detailParams: testParams });

      // 检查参数正确传递
      expect(wrapper.vm.detailParams).toEqual(testParams);
      expect(typeof wrapper.vm.fetchDataSource).toBe('function');
    });
  });

  describe('计算属性', () => {
    test('应该正确计算columns', () => {
      const wrapper = createWrapper();

      const columns = wrapper.vm.columns;
      expect(columns).toBeDefined();
      expect(typeof wrapper.vm.mTenderGenColumns).toBe('function');
    });
  });

  describe('常量', () => {
    test('应该包含TENDER_TYPE常量', () => {
      const wrapper = createWrapper();

      expect(wrapper.vm.TENDER_TYPE).toBeDefined();
      expect(wrapper.vm.TENDER_TYPE.all).toBeDefined();
    });
  });

  describe('插槽渲染', () => {
    test('应该有keyNoTenderee插槽', () => {
      const wrapper = createWrapper();

      const table = wrapper.find('q-rich-table-stub');
      expect(table.exists()).toBe(true);

      // 检查是否有插槽内容
      const slots = table.vm.$slots;
      expect(slots).toBeDefined();
    });
  });

  describe('mixins功能', () => {
    test('应该包含dimension mixin的方法', () => {
      const wrapper = createWrapper();

      expect(typeof wrapper.vm.mDimenGetList).toBe('function');
    });

    test('应该包含list mixin的方法', () => {
      const wrapper = createWrapper();

      expect(typeof wrapper.vm.mListGetTableData).toBe('function');
    });

    test('应该包含tender mixin的方法', () => {
      const wrapper = createWrapper();

      expect(typeof wrapper.vm.mTenderGenColumns).toBe('function');
    });
  });

  describe('边界情况', () => {
    test('应该处理空的detailParams', () => {
      const wrapper = createWrapper({ detailParams: {} });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.vm.detailParams).toEqual({});
    });

    test('应该处理null的detailParams', () => {
      const wrapper = createWrapper({ detailParams: null });

      expect(wrapper.exists()).toBe(true);
    });

    test('应该处理undefined的detailParams', () => {
      const wrapper = createWrapper({ detailParams: undefined });

      expect(wrapper.exists()).toBe(true);
    });
  });

  describe('数据源处理', () => {
    test('应该正确处理表格数据', () => {
      const wrapper = createWrapper();

      expect(typeof wrapper.vm.mListGetTableData).toBe('function');
      // 调用方法检查返回值
      const tableData = wrapper.vm.mListGetTableData('current');
      expect(tableData).toBeDefined();
    });

    test('应该正确处理列配置', () => {
      const wrapper = createWrapper();

      const columns = wrapper.vm.mTenderGenColumns('all');
      expect(columns).toBeDefined();
      expect(Array.isArray(columns)).toBe(true);
    });
  });

  describe('实体链接处理', () => {
    test('应该正确渲染实体链接组件', () => {
      const wrapper = createWrapper();

      // 检查是否有实体链接的插槽配置
      const table = wrapper.find('q-rich-table-stub');
      expect(table.exists()).toBe(true);
    });
  });

  describe('招标类型', () => {
    test('应该使用正确的招标类型', () => {
      const wrapper = createWrapper();

      expect(typeof wrapper.vm.mTenderGenColumns).toBe('function');
      // 检查TENDER_TYPE常量
      expect(wrapper.vm.TENDER_TYPE).toBeDefined();
    });
  });

  describe('方法调用', () => {
    test('fetchDataSource应该是一个函数', () => {
      const wrapper = createWrapper();

      expect(typeof wrapper.vm.fetchDataSource).toBe('function');
    });

    test('fetchDataSource应该返回Promise', () => {
      const wrapper = createWrapper();

      const result = wrapper.vm.fetchDataSource({ pagination: { current: 1, pageSize: 10 } });
      expect(result).toBeInstanceOf(Promise);
    });
  });

  describe('组件生命周期', () => {
    test('组件应该正确初始化', () => {
      const wrapper = createWrapper();

      expect(wrapper.vm).toBeDefined();
      expect(wrapper.vm.TENDER_TYPE).toBeDefined();
      expect(wrapper.vm.detailParams).toBeDefined();
    });
  });
});
