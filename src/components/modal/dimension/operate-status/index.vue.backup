<template>
  <div>
    <table class="ntable">
      <tr>
        <td class="tb" width="20%">变更企业</td>
        <td width="30%" v-if="viewData.Name">
          <q-entity-link :coy-arr="[{ KeyNo: viewData.KeyNo, Name: viewData.Name }]"></q-entity-link>
        </td>
        <td v-else>-</td>
        <td class="tb" width="20%">变更日期</td>
        <td width="30%">
          <template v-if="viewData.CreateDate">
            {{ new Date(viewData.CreateDate).getTime() | dateformat('YYYY-MM-DD') }}
          </template>
          <template v-else>-</template>
        </td>
      </tr>
      <tr>
        <td class="tb" width="20%">变更前</td>
        <td width="30%">{{ viewData.BeforeContent || '-' }}</td>
        <td class="tb" width="20%">变更后</td>
        <td width="30%">{{ viewData.AfterContent || '-' }}</td>
      </tr>
      <tr>
        <td width="20%" class="tb">登记机关</td>
        <td colspan="3">{{ viewData.Extend1 || viewData.Registry || '-' }}</td>
      </tr>
    </table>
  </div>
</template>
<script>
export default {
  props: {
    viewData: {
      type: Object,
      default() {
        return {};
      },
    },
  },
};
</script>
