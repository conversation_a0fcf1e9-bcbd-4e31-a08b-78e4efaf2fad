import { defineComponent, PropType } from 'vue';

import formatDate from '@/utils/format/date';

import styles from './index.module.less';

interface ViewData {
  KeyNo?: string;
  Name?: string;
  CreateDate?: string;
  BeforeContent?: string;
  AfterContent?: string;
  Extend1?: string;
  Registry?: string;
}

const OperateStatus = defineComponent({
  name: 'OperateStatus',

  props: {
    viewData: {
      type: Object as PropType<ViewData>,
      default: () => ({}),
    },
  },

  setup(props) {
    const dateformat = (timestamp: number) => {
      return formatDate(timestamp, { pattern: 'YYYY-MM-DD', defaultVal: '-' });
    };

    return {
      dateformat,
    };
  },

  render() {
    const { viewData, dateformat } = this;

    return (
      <table class={styles.ntable}>
        <tr>
          <td class={styles.tb} width="20%">变更企业：</td>
          {viewData.Name ? (
            <td width="30%">
              <q-entity-link coy-arr={[{ Name: viewData.Name, KeyNo: viewData.KeyNo }]} />
            </td>
          ) : (
            <td>-</td>
          )}
          <td class={styles.tb} width="20%">变更日期：</td>
          {viewData.CreateDate ? (
            <td width="30%">{dateformat(new Date(viewData.CreateDate).getTime())}</td>
          ) : (
            <td>-</td>
          )}
        </tr>
        <tr>
          <td class={styles.tb} width="20%">变更前：</td>
          <td width="30%">{viewData.BeforeContent || '-'}</td>
          <td class={styles.tb} width="20%">变更后：</td>
          <td width="30%">{viewData.AfterContent || '-'}</td>
        </tr>
        <tr>
          <td class={styles.tb} width="20%">登记机关：</td>
          <td colspan="3">{viewData.Extend1 || viewData.Registry || '-'}</td>
        </tr>
      </table>
    );
  },
});

export default OperateStatus;
