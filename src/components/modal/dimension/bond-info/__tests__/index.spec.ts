import { shallowMount } from '@vue/test-utils';

import BondInfo from '../index.tsx';

describe('BondInfo', () => {
  let wrapper: any;
  let mockOptions: any;

  beforeEach(() => {
    mockOptions = {
      propsData: {
        viewData: {
          FullName: '测试债券全称',
          ShortName: '测试债券',
          BondCode: 'TEST001',
          BondType: '企业债',
          BoundValue: '100',
          YearLimit: '5',
          InterestRate: '3.5',
          MaturityDate: '2025-12-31',
          HonourDate: '2025-12-31',
          DelistDate: '2025-12-30',
          InterestRateIntroduce: '固定利率',
          PlanBreathWay: '单利',
          ServicingWay: '到期一次还本付息',
          PayoutDate: '2020-01-01',
          CeaseDate: '2025-12-31',
          AnnualInterestRate: '1',
          InterestPaymentDate: '2025-12-31',
          OfferingPrice: '100',
          Issuance: '10',
          ReleaseDate: '2020-01-01',
          LaunchDate: '2020-01-15',
          PublicPlaces: '上海证券交易所',
          CreditRating: 'AAA',
          InternalCreditEnhancementMode: '担保',
          ExternalCreditEnhancementMode: '保险'
        },
      },
    };
  });

  const createWrapper = (propsData: any = {}, options: any = {}) => {
    const mergedOptions = {
      ...mockOptions,
      propsData: {
        viewData: {
          ...mockOptions.propsData.viewData,
          ...propsData.viewData,
        },
      },
      ...options,
    };

    wrapper = shallowMount(BondInfo, mergedOptions);
    return wrapper;
  };

  afterEach(() => {
    if (wrapper) {
      wrapper.destroy();
    }
  });

  describe('基本渲染', () => {
    test('应该正确渲染债券基本信息', () => {
      const wrapper = createWrapper();

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('债券名称');
      expect(wrapper.text()).toContain('债券简称');
      expect(wrapper.text()).toContain('测试债券全称');
      expect(wrapper.text()).toContain('测试债券');
    });

    test('应该正确显示债券代码和类型', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('债券代码');
      expect(wrapper.text()).toContain('债券类型');
      expect(wrapper.text()).toContain('TEST001');
      expect(wrapper.text()).toContain('企业债');
    });

    test('应该正确显示债券面值和年限', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('债券面值(元)');
      expect(wrapper.text()).toContain('债券年限(年)');
      expect(wrapper.text()).toContain('100');
      expect(wrapper.text()).toContain('5');
    });

    test('应该正确显示利率信息', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('票面利率(%)');
      expect(wrapper.text()).toContain('利率说明');
      expect(wrapper.text()).toContain('3.5');
      expect(wrapper.text()).toContain('固定利率');
    });
  });

  describe('日期格式化', () => {
    test('应该正确显示各种日期字段', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('到期日');
      expect(wrapper.text()).toContain('兑付日');
      expect(wrapper.text()).toContain('摘牌日');
      expect(wrapper.text()).toContain('起息日期');
      expect(wrapper.text()).toContain('止息日期');
      expect(wrapper.text()).toContain('付息日期');
      expect(wrapper.text()).toContain('发行日期');
      expect(wrapper.text()).toContain('上市日期');
    });
  });

  describe('发行信息', () => {
    test('应该正确显示发行相关信息', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('发行价格(元)');
      expect(wrapper.text()).toContain('发行规模（亿元）');
      expect(wrapper.text()).toContain('上市场所');
      expect(wrapper.text()).toContain('信用等级');
      expect(wrapper.text()).toContain('上海证券交易所');
      expect(wrapper.text()).toContain('AAA');
    });

    test('应该正确显示信用增级方式', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('内部信用增级方式');
      expect(wrapper.text()).toContain('外部信用增级方式');
      expect(wrapper.text()).toContain('担保');
      expect(wrapper.text()).toContain('保险');
    });
  });

  describe('计息和付息信息', () => {
    test('应该正确显示计息和付息方式', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('计息方式');
      expect(wrapper.text()).toContain('付息方式');
      expect(wrapper.text()).toContain('年付息次数');
      expect(wrapper.text()).toContain('单利');
      expect(wrapper.text()).toContain('到期一次还本付息');
    });
  });

  describe('边界情况', () => {
    test('应该处理空的viewData', () => {
      const wrapper = createWrapper({ viewData: {} });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理undefined的字段', () => {
      const wrapper = createWrapper({
        viewData: {
          FullName: undefined,
          ShortName: undefined,
          BondCode: undefined,
          BondType: undefined,
        }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理null值', () => {
      const wrapper = createWrapper({
        viewData: {
          FullName: null,
          ShortName: null,
          BondCode: null,
          BondType: null,
        }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });
  });

  describe('表格结构', () => {
    test('应该渲染正确的表格结构', () => {
      const wrapper = createWrapper();

      const table = wrapper.find('table');
      expect(table.exists()).toBe(true);
      expect(table.classes()).toContain('ntable');

      const rows = wrapper.findAll('tr');
      expect(rows.length).toBeGreaterThan(10); // 应该有多行数据
    });

    test('应该有正确的列宽设置', () => {
      const wrapper = createWrapper();

      const cells = wrapper.findAll('td[width="20%"]');
      expect(cells.length).toBeGreaterThan(0);

      const cells30 = wrapper.findAll('td[width="30%"]');
      expect(cells30.length).toBeGreaterThan(0);
    });
  });
});
