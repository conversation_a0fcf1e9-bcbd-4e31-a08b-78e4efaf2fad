import { defineComponent, PropType } from 'vue';

import dateFormat from '@/utils/format/date';

import styles from './index.module.less';

interface ViewData {
  FullName?: string;
  ShortName?: string;
  BondCode?: string;
  BondType?: string;
  BoundValue?: string;
  YearLimit?: string;
  InterestRate?: string;
  MaturityDate?: string;
  HonourDate?: string;
  DelistDate?: string;
  InterestRateIntroduce?: string;
  PlanBreathWay?: string;
  ServicingWay?: string;
  PayoutDate?: string;
  CeaseDate?: string;
  AnnualInterestRate?: string;
  InterestPaymentDate?: string;
  OfferingPrice?: string;
  Issuance?: string;
  ReleaseDate?: string;
  LaunchDate?: string;
  PublicPlaces?: string;
  CreditRating?: string;
  InternalCreditEnhancementMode?: string;
  ExternalCreditEnhancementMode?: string;
}

const BondInfo = defineComponent({
  name: 'BondInfo',

  props: {
    viewData: {
      type: Object as PropType<ViewData>,
      default: () => ({}),
    },
  },

  setup() {
    return {};
  },

  render() {
    const { viewData } = this;

    return (
      <div>
        <table class="ntable">
          <tbody>
            <tr>
              <td width="20%" class="tb">债券名称</td>
              <td colspan="3">{viewData.FullName || '-'}</td>
            </tr>
            <tr>
              <td width="20%" class="tb">债券简称</td>
              <td colspan="3">{viewData.ShortName || '-'}</td>
            </tr>
            <tr>
              <td class="tb" width="20%">债券代码</td>
              <td width="30%">{viewData.BondCode || '-'}</td>
              <td class="tb" width="20%">债券类型</td>
              <td width="30%">{viewData.BondType || '-'}</td>
            </tr>
            <tr>
              <td class="tb" width="20%">债券面值(元)</td>
              <td width="30%">{viewData.BoundValue || '-'}</td>
              <td class="tb" width="20%">债券年限(年)</td>
              <td width="30%">{viewData.YearLimit || '-'}</td>
            </tr>
            <tr>
              <td class="tb" width="20%">票面利率(%)</td>
              <td width="30%">{viewData.InterestRate || '-'}</td>
              <td class="tb" width="20%">到期日</td>
              <td width="30%">{dateFormat(viewData.MaturityDate)}</td>
            </tr>
            <tr>
              <td class="tb" width="20%">兑付日</td>
              <td width="30%">{dateFormat(viewData.HonourDate)}</td>
              <td class="tb" width="20%">摘牌日</td>
              <td width="30%">{dateFormat(viewData.DelistDate)}</td>
            </tr>
            <tr>
              <td width="20%" class="tb">利率说明</td>
              <td colspan="3">{viewData.InterestRateIntroduce || '-'}</td>
            </tr>
            <tr>
              <td class="tb" width="20%">计息方式</td>
              <td width="30%">{viewData.PlanBreathWay || '-'}</td>
              <td class="tb" width="20%">付息方式</td>
              <td width="30%">{viewData.ServicingWay || '-'}</td>
            </tr>
            <tr>
              <td class="tb" width="20%">起息日期</td>
              <td width="30%">{dateFormat(viewData.PayoutDate)}</td>
              <td class="tb" width="20%">止息日期</td>
              <td width="30%">{dateFormat(viewData.CeaseDate)}</td>
            </tr>
            <tr>
              <td class="tb" width="20%">年付息次数</td>
              <td width="30%">{viewData.AnnualInterestRate || '-'}</td>
              <td class="tb" width="20%">付息日期</td>
              <td width="30%">{dateFormat(viewData.InterestPaymentDate)}</td>
            </tr>
            <tr>
              <td class="tb" width="20%">发行价格(元)</td>
              <td width="30%">{viewData.OfferingPrice || '-'}</td>
              <td class="tb" width="20%">发行规模（亿元）</td>
              <td width="30%">{viewData.Issuance || '-'}</td>
            </tr>
            <tr>
              <td class="tb" width="20%">发行日期</td>
              <td width="30%">{dateFormat(viewData.ReleaseDate)}</td>
              <td class="tb" width="20%">上市日期</td>
              <td width="30%">{dateFormat(viewData.LaunchDate)}</td>
            </tr>
            <tr>
              <td class="tb" width="20%">上市场所</td>
              <td width="30%">{viewData.PublicPlaces || '-'}</td>
              <td class="tb" width="20%">信用等级</td>
              <td width="30%">{viewData.CreditRating || '-'}</td>
            </tr>
            <tr>
              <td class="tb" width="20%">内部信用增级方式</td>
              <td width="30%">{viewData.InternalCreditEnhancementMode || '-'}</td>
              <td class="tb" width="20%">外部信用增级方式</td>
              <td width="30%">{viewData.ExternalCreditEnhancementMode || '-'}</td>
            </tr>
          </tbody>
        </table>
      </div>
    );
  },
});

export default BondInfo;
