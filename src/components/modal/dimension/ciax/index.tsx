import { defineComponent, PropType } from 'vue';
import moment from 'moment';

import { isJSONString } from '@/utils/data-type/is-json-string';

import styles from './index.module.less';

interface CreditGradeItem {
  time?: string;
  grade?: string;
  code?: string;
}

interface PunishInfoItem {
  decisionNo?: string;
  party?: string;
  penaltDate?: string;
  case?: string;
}

interface ViewData {
  company_name?: string;
  reg_no?: string;
  reg_gov?: string;
  administrative_area?: string;
  economic_area?: string;
  trade_type?: string;
  special_trade_area?: string;
  cancellation_flag?: string;
  annual_status?: string;
  reg_date?: string;
  expire_date?: string;
  industry_type?: string;
  e_business_type?: string;
  credit_grade?: CreditGradeItem[];
  punish_info?: string;
}

const Ciax = defineComponent({
  name: 'Ciax',

  props: {
    viewData: {
      type: Object as PropType<ViewData>,
      default: () => ({}),
    },
  },

  setup() {
    const formatRegDate = (str?: string): string => {
      if (str) {
        return moment(str).format('YYYY-MM-DD');
      }
      return '-';
    };

    const formatData = (str?: string): string => {
      if (str) {
        return str.replace(/(\d{4})(\d{2})(\d{2})/g, '$1-$2-$3');
      }
      return '-';
    };

    return {
      formatRegDate,
      formatData,
    };
  },

  render() {
    const { viewData } = this;

    // 解析处罚信息
    let punishInfoList: PunishInfoItem[] = [];
    if (viewData.punish_info && isJSONString(viewData.punish_info)) {
      try {
        punishInfoList = JSON.parse(viewData.punish_info);
      } catch (e) {
        console.error('Failed to parse punish_info:', e);
      }
    }

    return (
      <div>
        <q-plain-table>
          <tbody>
            <tr>
              <td width="20%" class="tb">
                企业名称
              </td>
              <td colspan="3">{viewData.company_name || '-'}</td>
            </tr>
            <tr>
              <td class="tb" width="20%">
                海关注册编码
              </td>
              <td width="30%">{viewData.reg_no || '-'}</td>
              <td class="tb" width="20%">
                注册海关
              </td>
              <td width="30%">{viewData.reg_gov || '-'}</td>
            </tr>
            <tr>
              <td class="tb" width="20%">
                行政地区
              </td>
              <td width="30%">{viewData.administrative_area || '-'}</td>
              <td class="tb" width="20%">
                经济地区
              </td>
              <td width="30%">{viewData.economic_area || '-'}</td>
            </tr>
            <tr>
              <td class="tb" width="20%">
                经营类别
              </td>
              <td width="30%">{viewData.trade_type || '-'}</td>
              <td class="tb" width="20%">
                特殊贸易区域
              </td>
              <td width="30%">{viewData.special_trade_area || '-'}</td>
            </tr>
            <tr>
              <td class="tb" width="20%">
                海关注销标志
              </td>
              <td width="30%">{viewData.cancellation_flag || '-'}</td>
              <td class="tb" width="20%">
                年报情况
              </td>
              <td width="30%">{viewData.annual_status || '-'}</td>
            </tr>
            <tr>
              <td class="tb" width="20%">
                注册日期
              </td>
              <td width="30%">{this.formatRegDate(viewData.reg_date)}</td>
              <td class="tb" width="20%">
                报关有效期
              </td>
              <td width="30%">{this.formatRegDate(viewData.expire_date)}</td>
            </tr>
            <tr>
              <td class="tb" width="20%">
                行业种类
              </td>
              <td width="30%">{viewData.industry_type || '-'}</td>
              <td class="tb" width="20%">
                跨境贸易电子商务类型
              </td>
              <td width="30%">{viewData.e_business_type || '-'}</td>
            </tr>
            <tr>
              <td class="tb" width="20%">
                信用等级
              </td>
              <td colspan="3">
                {viewData.credit_grade && viewData.credit_grade.length > 0 ? (
                  <table class="ntable">
                    <thead>
                      <tr>
                        <th width="33%">认定时间</th>
                        <th width="33%">信用等级</th>
                        <th width="34%">认证证书编码</th>
                      </tr>
                    </thead>
                    <tbody>
                      {viewData.credit_grade.map((item, index) => (
                        <tr key={`credit-${index}`}>
                          <td>{item.time || '-'}</td>
                          <td>{item.grade || '-'}</td>
                          <td>{item.code || '-'}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                ) : (
                  '-'
                )}
              </td>
            </tr>
            <tr>
              <td class="tb" width="20%">
                海关行政处罚
              </td>
              <td colspan="3">
                {punishInfoList.length > 0 ? (
                  <table class="ntable">
                    <thead>
                      <tr>
                        <th width="25%">行政处罚决定书编号</th>
                        <th width="25%">当事人</th>
                        <th width="25%">处罚日期</th>
                        <th width="25%">案件性质</th>
                      </tr>
                    </thead>
                    <tbody>
                      {punishInfoList.map((item, index) => (
                        <tr key={`punish-${index}`}>
                          <td>{item.decisionNo || '-'}</td>
                          <td>{item.party || '-'}</td>
                          <td>{this.formatData(item.penaltDate)}</td>
                          <td>{item.case || '-'}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                ) : (
                  '-'
                )}
              </td>
            </tr>
          </tbody>
        </q-plain-table>
      </div>
    );
  },
});

export default Ciax;
