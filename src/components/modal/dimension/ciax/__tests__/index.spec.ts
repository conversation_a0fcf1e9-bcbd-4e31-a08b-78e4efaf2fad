import { shallowMount } from '@vue/test-utils';

import Ciax from '../index.tsx';

describe('Ciax', () => {
  let wrapper: any;
  let mockOptions: any;

  beforeEach(() => {
    mockOptions = {
      propsData: {
        viewData: {
          company_name: '测试公司',
          reg_no: 'REG123456',
          reg_gov: '上海海关',
          administrative_area: '上海市',
          economic_area: '长三角',
          trade_type: '进出口',
          special_trade_area: '自贸区',
          cancellation_flag: '否',
          annual_status: '正常',
          reg_date: '2020-01-01',
          expire_date: '2025-12-31',
          industry_type: '制造业',
          e_business_type: '跨境电商',
          credit_grade: [
            {
              time: '2023-01-01',
              grade: 'AA',
              code: 'CERT123456'
            }
          ],
          punish_info: JSON.stringify([
            {
              decisionNo: 'PUNISH001',
              party: '测试公司',
              penaltDate: '20230101',
              case: '违规案件'
            }
          ])
        },
      },
    };
  });

  const createWrapper = (propsData: any = {}, options: any = {}) => {
    const mergedOptions = {
      ...mockOptions,
      propsData: {
        viewData: {
          ...mockOptions.propsData.viewData,
          ...propsData.viewData,
        },
      },
      ...options,
    };

    wrapper = shallowMount(Ciax, mergedOptions);
    return wrapper;
  };

  afterEach(() => {
    if (wrapper) {
      wrapper.destroy();
    }
  });

  describe('基本信息渲染', () => {
    test('应该正确渲染公司基本信息', () => {
      const wrapper = createWrapper();

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('测试公司');
      expect(wrapper.text()).toContain('REG123456');
      expect(wrapper.text()).toContain('上海海关');
    });

    test('应该正确显示海关注册信息', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('海关注册编码');
      expect(wrapper.text()).toContain('注册海关');
      expect(wrapper.text()).toContain('行政地区');
      expect(wrapper.text()).toContain('经济地区');
    });

    test('应该正确显示经营信息', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('经营类别');
      expect(wrapper.text()).toContain('特殊贸易区域');
      expect(wrapper.text()).toContain('进出口');
      expect(wrapper.text()).toContain('自贸区');
    });

    test('应该正确显示状态信息', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('海关注销标志');
      expect(wrapper.text()).toContain('年报情况');
      expect(wrapper.text()).toContain('否');
      expect(wrapper.text()).toContain('正常');
    });
  });

  describe('日期格式化', () => {
    test('应该正确格式化注册日期', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('注册日期');
      expect(wrapper.text()).toContain('2020-01-01');
    });

    test('应该正确显示报关有效期', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('报关有效期');
      expect(wrapper.text()).toContain('2025-12-31');
    });
  });

  describe('行业信息', () => {
    test('应该正确显示行业信息', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('行业种类');
      expect(wrapper.text()).toContain('跨境贸易电子商务类型');
      expect(wrapper.text()).toContain('制造业');
      expect(wrapper.text()).toContain('跨境电商');
    });
  });

  describe('信用等级', () => {
    test('应该正确显示信用等级信息', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('认定时间');
      expect(wrapper.text()).toContain('信用等级');
      expect(wrapper.text()).toContain('认证证书编码');
      expect(wrapper.text()).toContain('2023-01-01');
      expect(wrapper.text()).toContain('AA');
      expect(wrapper.text()).toContain('CERT123456');
    });

    test('应该处理空的信用等级', () => {
      const wrapper = createWrapper({
        viewData: {
          credit_grade: []
        }
      });

      expect(wrapper.text()).toContain('信用等级');
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理undefined的信用等级', () => {
      const wrapper = createWrapper({
        viewData: {
          credit_grade: undefined
        }
      });

      expect(wrapper.text()).toContain('信用等级');
      expect(wrapper.text()).toContain('-');
    });
  });

  describe('海关行政处罚', () => {
    test('应该正确显示处罚信息', () => {
      const wrapper = createWrapper();

      expect(wrapper.text()).toContain('海关行政处罚');
      expect(wrapper.text()).toContain('行政处罚决定书编号');
      expect(wrapper.text()).toContain('当事人');
      expect(wrapper.text()).toContain('处罚日期');
      expect(wrapper.text()).toContain('案件性质');
      expect(wrapper.text()).toContain('PUNISH001');
      expect(wrapper.text()).toContain('2023-01-01');
      expect(wrapper.text()).toContain('违规案件');
    });

    test('应该处理空的处罚信息', () => {
      const wrapper = createWrapper({
        viewData: {
          punish_info: JSON.stringify([])
        }
      });

      expect(wrapper.text()).toContain('海关行政处罚');
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理undefined的处罚信息', () => {
      const wrapper = createWrapper({
        viewData: {
          punish_info: undefined
        }
      });

      expect(wrapper.text()).toContain('海关行政处罚');
      expect(wrapper.text()).toContain('-');
    });
  });

  describe('边界情况', () => {
    test('应该处理空的viewData', () => {
      const wrapper = createWrapper({ viewData: {} });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理undefined的字段', () => {
      const wrapper = createWrapper({
        viewData: {
          company_name: undefined,
          reg_no: undefined,
          reg_gov: undefined,
        }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });

    test('应该处理null值', () => {
      const wrapper = createWrapper({
        viewData: {
          company_name: null,
          reg_no: null,
          reg_gov: null,
        }
      });

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.text()).toContain('-');
    });
  });

  describe('方法测试', () => {
    test('formatRegDate方法应该存在', () => {
      const wrapper = createWrapper();

      expect(wrapper.vm.formatRegDate).toBeDefined();
      expect(typeof wrapper.vm.formatRegDate).toBe('function');
    });

    test('formatRegDate方法应该正确格式化日期', () => {
      const wrapper = createWrapper();

      expect(wrapper.vm.formatRegDate('2020-01-01')).toBe('2020-01-01');
      expect(wrapper.vm.formatRegDate('')).toBe('-');
      expect(wrapper.vm.formatRegDate(null)).toBe('-');
    });

    test('formatData方法应该存在', () => {
      const wrapper = createWrapper();

      expect(wrapper.vm.formatData).toBeDefined();
      expect(typeof wrapper.vm.formatData).toBe('function');
    });

    test('formatData方法应该正确格式化日期字符串', () => {
      const wrapper = createWrapper();

      expect(wrapper.vm.formatData('20230101')).toBe('2023-01-01');
      expect(wrapper.vm.formatData('')).toBe('-');
      expect(wrapper.vm.formatData(null)).toBe('-');
    });
  });

  describe('表格结构', () => {
    test('应该渲染正确的表格结构', () => {
      const wrapper = createWrapper();

      const table = wrapper.find('q-plain-table');
      expect(table.exists()).toBe(true);

      const tbody = wrapper.find('tbody');
      expect(tbody.exists()).toBe(true);

      const rows = wrapper.findAll('tr');
      expect(rows.length).toBeGreaterThan(10);
    });
  });
});
