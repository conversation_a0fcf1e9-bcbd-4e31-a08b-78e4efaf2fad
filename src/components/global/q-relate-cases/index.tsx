import { defineComponent, ref, computed, onMounted, PropType } from 'vue';
import { useRoute } from 'vue-router/composables';
import { dimension } from '@/shared/services';
import QPlainTable from '../q-plain-table';
import styles from './q-relate-cases.module.less';

interface CaseData {
  Id: string;
  CaseName: string;
  CloseStatus: number;
  CaseTypeArray: string[];
  CaseReason: string;
  AnNoList: string[];
  CourtList: string[];
  LatestTrialRound: string;
}

export default defineComponent({
  name: 'QRelateCases',
  props: {
    searchParams: {
      type: Object as PropType<Record<string, any>>,
      default: () => ({}),
    },
    stitle: {
      type: String,
      default: '所属',
    },
    hideTitle: {
      type: Boolean,
      default: false,
    },
  },
  emits: ['visibleChange'],
  setup(props, { emit }) {
    const route = useRoute();
    const casesList = ref<CaseData[]>([]);
    const noSearchId = ref(false);

    const showRelate = computed(() => route.name !== 'caseDetail');

    const getRelateDetail = async () => {
      if (!props.searchParams?.CaseSearchId?.length) {
        return;
      }
      try {
        const res = await dimension.getCaseListByIds({
          ids: props.searchParams.CaseSearchId,
        });
        if (res?.Result?.length) {
          casesList.value = res.Result;
          noSearchId.value = true;
        }
      } catch (error) {
        console.error('获取案件列表失败:', error);
      }
    };

    const onVisibleChange = () => {
      emit('visibleChange');
    };

    onMounted(() => {
      getRelateDetail();
    });

    return {
      showRelate,
      noSearchId,

      casesList,
      onVisibleChange,
    };
  },
  render() {
    if (!this.noSearchId || !this.showRelate) {
      return null;
    }

    return (
      <div id="app-relate-cases" class={styles.container}>
        {!this.hideTitle && (
          <div class={styles.tcaption} style={{ color: '#333' }}>
            {this.stitle}司法案件
          </div>
        )}
        <QPlainTable>
          <thead>
            <tr class={styles.qTextCenter}>
              <th>序号</th>
              <th>案件名称</th>
              <th>案件类型</th>
              <th>案由</th>
              <th>案号</th>
              <th>法院</th>
              <th>最新审理程序</th>
            </tr>
          </thead>
          <tbody>
            {this.casesList.map((caseData, index) => (
              <tr key={index}>
                <td align="center">{index + 1}</td>
                <td>
                  {caseData.CloseStatus === 1 && <span class={styles.textSuccess}>【已结案】</span>}
                  <a rel="nofollow" href={`/embed/courtCaseDetail?caseId=${caseData.Id}&title=${caseData.CaseName}`} target="_blank">
                    <span onClick={this.onVisibleChange}>{caseData.CaseName || '-'}</span>
                  </a>
                </td>
                <td width="12%" align="center">
                  {caseData.CaseTypeArray.map((type, ctIndex) => (
                    <span key={ctIndex}>
                      {type}
                      <br />
                    </span>
                  ))}
                </td>
                <td width="12%" align="center" domPropsInnerHTML={caseData.CaseReason || '-'} />
                <td width="25%">
                  {caseData.AnNoList.map((anNo, anIndex) => (
                    <span key={anIndex}>
                      {anNo}
                      <br />
                    </span>
                  ))}
                </td>
                <td width="16%">
                  {caseData.CourtList.map((court, clIndex) => (
                    <span key={clIndex}>
                      {court}
                      <br />
                    </span>
                  ))}
                </td>
                <td width="14%" align="center" domPropsInnerHTML={caseData.LatestTrialRound || '-'} />
              </tr>
            ))}
          </tbody>
        </QPlainTable>
      </div>
    );
  },
});
