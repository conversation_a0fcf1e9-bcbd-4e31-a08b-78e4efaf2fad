import { shallowMount } from '@vue/test-utils';
import { vi } from 'vitest';

import QSourceLogo from '../index.tsx';

// Mock require function for SVG imports
// vi.mock('../../../assets/images/pdf.svg', () => 'mocked-pdf-icon');
// vi.mock('../../../assets/images/word.svg', () => 'mocked-word-icon');
// vi.mock('../../../assets/images/excel.svg', () => 'mocked-excel-icon');
// vi.mock('../../../assets/images/png.svg', () => 'mocked-png-icon');
// vi.mock('../../../assets/images/txt.svg', () => 'mocked-txt-icon');
// vi.mock('../../../assets/images/other.svg', () => 'mocked-other-icon');

describe('QSourceLogo', () => {
  let wrapper: any;
  let mockOptions: any;

  beforeEach(() => {
    mockOptions = {
      propsData: {
        url: 'https://example.com/document.pdf',
        label: '查看',
      },
    };
  });

  const createWrapper = (propsData: any = {}, options: any = {}) => {
    const mergedOptions = {
      ...mockOptions,
      propsData: {
        ...mockOptions.propsData,
        ...propsData,
      },
      ...options,
    };

    wrapper = shallowMount(QSourceLogo, mergedOptions);
    return wrapper;
  };

  afterEach(() => {
    if (wrapper) {
      wrapper.destroy();
    }
  });

  describe('基本渲染', () => {
    test('应该正确渲染组件', () => {
      const wrapper = createWrapper();

      expect(wrapper.exists()).toBe(true);
      expect(wrapper.vm.$options.name).toBe('app-source-logo');
    });

    test('应该正确显示标签文本', () => {
      const wrapper = createWrapper({ label: '下载文档' });

      expect(wrapper.vm.label).toBe('下载文档');
    });

    test('当没有标签时应该为空', () => {
      const wrapper = createWrapper({ label: '' });

      expect(wrapper.vm.label).toBe('');
    });
  });

  describe('文件类型识别', () => {
    test('应该正确识别PDF文件', () => {
      const wrapper = createWrapper({ url: 'https://example.com/document.pdf' });

      expect(wrapper.vm.logo).toBe('pdf');
    });

    test('应该正确识别Word文件', () => {
      const wrapper = createWrapper({ url: 'https://example.com/document.doc' });
      expect(wrapper.vm.logo).toBe('doc');

      const wrapper2 = createWrapper({ url: 'https://example.com/document.docx' });
      expect(wrapper2.vm.logo).toBe('docx');
    });

    test('应该正确识别Excel文件', () => {
      const wrapper = createWrapper({ url: 'https://example.com/document.xls' });
      expect(wrapper.vm.logo).toBe('xls');

      const wrapper2 = createWrapper({ url: 'https://example.com/document.xlsx' });
      expect(wrapper2.vm.logo).toBe('xlsx');
    });

    test('应该正确识别图片文件', () => {
      const wrapper = createWrapper({ url: 'https://example.com/image.jpg' });
      expect(wrapper.vm.logo).toBe('picture');

      const wrapper2 = createWrapper({ url: 'https://example.com/image.png' });
      expect(wrapper2.vm.logo).toBe('picture');

      const wrapper3 = createWrapper({ url: 'https://example.com/image.bmp' });
      expect(wrapper3.vm.logo).toBe('picture');
    });

    test('应该正确识别文本文件', () => {
      const wrapper = createWrapper({ url: 'https://example.com/document.txt' });

      expect(wrapper.vm.logo).toBe('txt');
    });

    test('应该将未知文件类型识别为other', () => {
      const wrapper = createWrapper({ url: 'https://example.com/document.unknown' });

      expect(wrapper.vm.logo).toBe('other');
    });

    test('应该处理没有扩展名的URL', () => {
      const wrapper = createWrapper({ url: 'https://example.com/document' });

      expect(wrapper.vm.logo).toBe('other');
    });
  });

  describe('fileType属性优先级', () => {
    test('当提供fileType时应该优先使用fileType', () => {
      const wrapper = createWrapper({
        url: 'https://example.com/document.pdf',
        fileType: 'doc',
      });

      expect(wrapper.vm.logo).toBe('doc');
    });

    test('当fileType不在支持列表中时应该返回other', () => {
      const wrapper = createWrapper({
        url: 'https://example.com/document.pdf',
        fileType: 'unsupported',
      });

      expect(wrapper.vm.logo).toBe('other');
    });

    test('当fileType为空时应该使用URL推断', () => {
      const wrapper = createWrapper({
        url: 'https://example.com/document.pdf',
        fileType: '',
      });

      expect(wrapper.vm.logo).toBe('pdf');
    });
  });

  describe('点击事件', () => {
    test('点击时应该打开新窗口', () => {
      const mockOpen = vi.fn();
      Object.defineProperty(window, 'open', {
        value: mockOpen,
        writable: true,
      });

      const wrapper = createWrapper({ url: 'https://example.com/document.pdf' });

      // 直接调用组件的方法而不是触发DOM事件
      wrapper.vm.showFile();

      expect(mockOpen).toHaveBeenCalledWith('https://example.com/document.pdf');
    });

    test('当URL为空时点击不应该报错', () => {
      const mockOpen = vi.fn();
      Object.defineProperty(window, 'open', {
        value: mockOpen,
        writable: true,
      });

      const wrapper = createWrapper({ url: '' });

      expect(() => {
        wrapper.vm.showFile();
      }).not.toThrow();

      expect(mockOpen).toHaveBeenCalledWith('');
    });
  });

  describe('方法测试', () => {
    test('getFileType方法应该正确解析文件类型', () => {
      const wrapper = createWrapper();

      expect(wrapper.vm.getFileType('document.pdf')).toBe('pdf');
      expect(wrapper.vm.getFileType('document.doc')).toBe('doc');
      expect(wrapper.vm.getFileType('document.docx')).toBe('docx');
      expect(wrapper.vm.getFileType('document.xls')).toBe('xls');
      expect(wrapper.vm.getFileType('document.xlsx')).toBe('xlsx');
      expect(wrapper.vm.getFileType('image.jpg')).toBe('picture');
      expect(wrapper.vm.getFileType('image.png')).toBe('picture');
      expect(wrapper.vm.getFileType('image.bmp')).toBe('picture');
      expect(wrapper.vm.getFileType('document.txt')).toBe('txt');
      expect(wrapper.vm.getFileType('document.unknown')).toBe('other');
    });

    test('getFileType方法应该处理空URL', () => {
      const wrapper = createWrapper();

      expect(wrapper.vm.getFileType('')).toBe('other');
      expect(wrapper.vm.getFileType(null)).toBe('other');
      expect(wrapper.vm.getFileType(undefined)).toBe('other');
    });

    test('showFile方法应该调用window.open', () => {
      const mockOpen = vi.fn();
      Object.defineProperty(window, 'open', {
        value: mockOpen,
        writable: true,
      });

      const wrapper = createWrapper({ url: 'https://example.com/test.pdf' });

      wrapper.vm.showFile();

      expect(mockOpen).toHaveBeenCalledWith('https://example.com/test.pdf');
    });
  });

  describe('边界情况', () => {
    test('应该处理复杂的URL路径', () => {
      const wrapper = createWrapper({
        url: 'https://example.com/path/to/document.with.dots.pdf',
      });

      expect(wrapper.vm.logo).toBe('pdf');
    });

    test('应该处理大写扩展名', () => {
      const wrapper = createWrapper({ url: 'https://example.com/document.PDF' });

      // 现在实现会将大写转换为小写处理
      expect(wrapper.vm.logo).toBe('pdf');
    });

    test('应该处理查询参数的URL', () => {
      const wrapper = createWrapper({
        url: 'https://example.com/document.pdf?version=1&token=abc',
      });

      // 当前实现会将查询参数作为扩展名的一部分
      expect(wrapper.vm.logo).toBe('other');
    });
  });

  describe('组件属性', () => {
    test('应该有正确的组件名称', () => {
      const wrapper = createWrapper();

      expect(wrapper.vm.$options.name).toBe('app-source-logo');
    });

    test('应该正确处理所有props的默认值', () => {
      const wrapper = shallowMount(QSourceLogo);

      expect(wrapper.vm.fileType).toBe('');
      expect(wrapper.vm.url).toBe('');
      expect(wrapper.vm.label).toBe('');
    });
  });
});
