import { defineComponent, PropType, computed } from 'vue';

// 导入所有图标文件
import docIcon from './icons/doc.svg';
import docxIcon from './icons/docx.svg';
import excelIcon from './icons/excel.svg';
import gifIcon from './icons/gif.svg';
import ieIcon from './icons/ie.svg';
import otherIcon from './icons/other.svg';
import pdfIcon from './icons/pdf.svg';
import pictureIcon from './icons/picture.svg';
import pptIcon from './icons/ppt.svg';
import prdIcon from './icons/prd.svg';
import txtIcon from './icons/txt.svg';
import wordIcon from './icons/word.svg';
import xlsIcon from './icons/xls.svg';
import xlsxIcon from './icons/xlsx.svg';
import zipIcon from './icons/zip.svg';

import styles from './index.module.less';

// 支持的文件类型（基于可用的图标）
const FILE_TYPE = [
  'doc',
  'docx',
  'excel',
  'gif',
  'ie',
  'other',
  'pdf',
  'picture',
  'ppt',
  'prd',
  'txt',
  'word',
  'xls',
  'xlsx',
  'zip',
  // 兼容的别名
  'bmp',
  'jpg',
  'jpeg',
  'png',
];

// 图标映射表
const ICON_MAP: Record<string, string> = {
  // 直接映射
  doc: docIcon,
  docx: docxIcon,
  excel: excelIcon,
  gif: gifIcon,
  ie: ieIcon,
  other: otherIcon,
  pdf: pdfIcon,
  picture: pictureIcon,
  ppt: pptIcon,
  prd: prdIcon,
  txt: txtIcon,
  word: wordIcon,
  xls: xlsIcon,
  xlsx: xlsxIcon,
  zip: zipIcon,

  // 别名映射
  bmp: pictureIcon,
  jpg: pictureIcon,
  jpeg: pictureIcon,
  png: pictureIcon,
};

const QSourceLogo = defineComponent({
  name: 'app-source-logo',

  props: {
    fileType: {
      type: String as PropType<string>,
      default: '',
    },
    url: {
      type: String as PropType<string>,
      default: '',
    },
    label: {
      type: String as PropType<string>,
      default: '',
    },
  },

  setup(props) {
    const getFileType = (url: string): string => {
      if (!url) return 'other';

      const type = url.split('.').pop()?.toLowerCase();
      if (!type) return 'other';

      // 文件类型映射规则
      const typeMapping: Record<string, string> = {
        // PDF
        pdf: 'pdf',
        // Word 文档
        doc: 'doc',
        docx: 'docx',
        // Excel 文档
        xls: 'xls',
        xlsx: 'xlsx',
        // 图片文件
        bmp: 'picture',
        png: 'picture',
        jpg: 'picture',
        jpeg: 'picture',
        // PowerPoint
        ppt: 'ppt',
        pptx: 'ppt',
        // 压缩文件
        zip: 'zip',
        rar: 'zip',
        '7z': 'zip',
        // 文本文件
        txt: 'txt',
        // 其他
        gif: 'gif',
      };

      return typeMapping[type] || 'other';
    };

    const logo = computed(() => {
      if (props.fileType) {
        if (FILE_TYPE.indexOf(props.fileType) > -1) {
          return props.fileType;
        }
        return 'other';
      }
      return getFileType(props.url);
    });

    const showFile = () => {
      window.open(props.url);
    };

    // 获取图标资源
    const getImageSrc = (logoType: string): string => {
      return ICON_MAP[logoType] || ICON_MAP.other;
    };

    return {
      logo,
      showFile,
      getFileType,
      getImageSrc,
    };
  },

  render() {
    const { logo, label, showFile, getImageSrc } = this;

    return (
      <div onClick={showFile} class={styles.appSourceLogo}>
        <img class={styles.iconBg} src={getImageSrc(logo)} alt={`${logo} icon`} />
        {label && <span class={styles.label}>{label}</span>}
      </div>
    );
  },
});

export default QSourceLogo;
