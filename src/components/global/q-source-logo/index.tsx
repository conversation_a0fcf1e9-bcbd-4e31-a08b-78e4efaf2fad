import { defineComponent, PropType, computed } from 'vue';

import styles from './index.module.less';

const FILE_TYPE = ['bmp', 'excel', 'ie', 'jpg', 'other', 'pdf', 'png', 'txt', 'word', 'doc', 'docx', 'xls', 'xlsx', 'picture'];

interface Props {
  fileType?: string;
  url?: string;
  label?: string;
}

const QSourceLogo = defineComponent({
  name: 'app-source-logo',

  props: {
    fileType: {
      type: String as PropType<string>,
      default: '',
    },
    url: {
      type: String as PropType<string>,
      default: '',
    },
    label: {
      type: String as PropType<string>,
      default: '',
    },
  },

  setup(props) {
    const getFileType = (url: string): string => {
      const type = url && url.split('.')[url.split('.').length - 1];
      if (FILE_TYPE.includes(type)) {
        if (['doc', 'docx'].includes(type)) {
          return 'word';
        }
        if (['xls', 'xlsx'].includes(type)) {
          return 'excel';
        }
        if (['bmp', 'png', 'jpg', 'jpeg'].includes(type)) {
          return 'png';
        }
        return type;
      }
      return 'other';
    };

    const logo = computed(() => {
      if (props.fileType) {
        if (FILE_TYPE.indexOf(props.fileType) > -1) {
          return props.fileType;
        }
        return 'other';
      }
      return getFileType(props.url);
    });

    const showFile = () => {
      window.open(props.url);
    };

    // 获取图片路径（使用ES6模板字符串）
    const getImageSrc = (logoType: string): string => {
      // 使用相对路径构建图片URL
      return `/src/assets/images/${logoType}.svg`;
    };

    return {
      logo,
      showFile,
      getFileType,
      getImageSrc,
    };
  },

  render() {
    const { logo, label, showFile, getImageSrc } = this;

    return (
      <div onClick={showFile} class={styles.appSourceLogo}>
        <img class={styles.iconBg} src={getImageSrc(logo)} alt={`${logo} icon`} />
        {label && <span class={styles.label}>{label}</span>}
      </div>
    );
  },
});

export default QSourceLogo;
