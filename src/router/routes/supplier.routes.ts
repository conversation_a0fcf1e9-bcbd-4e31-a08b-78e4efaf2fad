import type { RouteConfig } from 'vue-router';

import { hasPermission } from '@/shared/composables/use-permission';
import SidebarMenuLayout from '@/shared/layouts/sidebar-menu';
import { InterestPermissionCode, PotentialPermissionCode } from '@/shared/constants/diligence.constant';

const MENU_ITEMS = [
  {
    key: '/supplier/dashboard',
    icon: 'icon-gongzuotai',
    label: 'Home',
  },
  { border: true, label: '业务' },
  {
    key: 'access',
    icon: 'icon-zhunrupaicha',
    label: 'Risk Checks',
    children: [
      {
        key: '/supplier/investigation',
        label: 'Access Checks',
      },
      {
        key: '/supplier/batch-investigation',
        label: 'Batch Checks',
      },
      {
        key: '/supplier/investigation-history',
        label: 'Check Records',
      },
    ],
  },
  // 合作监控
  // 不和路由保持一个名字，不然会resolve到子路由，导致菜单显示出错
  {
    key: 'risk-watching',
    icon: 'icon-chixujintiao2',
    label: 'Cooperative Monitor',
    children: [
      {
        key: '/supplier/risk-trends',
        label: 'Check Trends',
      },
      {
        key: '/supplier/risk-monitor',
        label: 'Company List',
      },
    ],
  },
  {
    key: 'Tender',
    icon: 'icon-toubiaozhongbiao',
    label: 'Tender Screening',
    children: [
      {
        key: '/supplier/bidding-investigation',
        icon: 'icon-toubiaozhongbiao',
        label: 'Tender Screening',
      },
      {
        key: '/supplier/bidding-investigation-history',
        icon: 'icon-toubiaozhongbiao',
        label: 'Tender Screening Records',
      },
    ],
  },
  {
    key: 'Compliance',
    icon: 'icon-shoucijintiao',
    label: 'Compliance Screening',
    children: [
      {
        key: '/supplier/interest-investigation',
        icon: 'icon-toubiaozhongbiao',
        label: 'Interest Relation Screening',
      },
      {
        key: '/supplier/potential-investigation',
        icon: 'icon-toubiaozhongbiao',
        label: 'Potential Relation Screening',
      },
    ],
  },
  {
    key: '/supplier/bidding-warning',
    icon: 'icon-toubiaoyujing',
    label: 'Bidding Warning',
  },
  { border: true, label: '管理' },
  {
    key: 'partnerShip',
    icon: 'icon-keshangguanli',
    label: `Third Party`,
    children: [
      {
        key: '/supplier/third-party/partners',
        label: `Third Party List`,
      },
      {
        key: '/supplier/third-party/annual-review',
        label: `Risk Annual Review`,
      },
    ],
  },

  {
    key: 'blacklist',
    icon: 'icon-heimingdanguanli',
    label: 'Blacklist',
    children: [
      {
        key: '/supplier/blacklist/internal-blacklist',
        label: 'Internal Blacklist',
      },
      {
        key: '/supplier/blacklist/external-blacklist',
        label: 'External Blacklist',
      },
    ],
  },
  {
    key: '/supplier/staff',
    icon: 'icon-renyuanguanli',
    label: 'Employee List',
  },
  { border: true, label: '分析' },
  {
    key: '/supplier/analysis-dashboard',
    icon: 'icon-zengchangshuai',
    label: 'Dashboard',
  },
];

const ZEISS_MENU_ITEMS = [
  {
    key: '/supplier/dashboard',
    icon: 'icon-gongzuotai',
    label: 'Home',
  },
  {
    key: 'access',
    icon: 'icon-zhunrupaicha',
    label: 'Risk Checks',
    children: [
      {
        key: '/supplier/investigation',
        label: 'Access Checks',
      },
      {
        key: '/supplier/batch-investigation',
        label: 'Batch Checks',
      },
      {
        key: '/supplier/investigation-history',
        label: 'Check Records',
      },
    ],
  },
  // 合作监控
  // 不和路由保持一个名字，不然会resolve到子路由，导致菜单显示出错
  {
    key: 'risk-watching',
    icon: 'icon-chixujintiao2',
    label: 'Cooperative Monitor',
    children: [
      {
        key: '/supplier/risk-trends',
        label: 'Check Trends',
      },
      {
        key: '/supplier/risk-monitor',
        label: 'Company List',
      },
    ],
  },
  {
    key: 'Tender',
    icon: 'icon-toubiaozhongbiao',
    label: 'Tender Screening',
    children: [
      {
        key: '/supplier/bidding-investigation',
        icon: 'icon-toubiaozhongbiao',
        label: 'Tender Screening',
      },
      {
        key: '/supplier/bidding-investigation-history',
        icon: 'icon-toubiaozhongbiao',
        label: 'Tender Screening Records',
      },
    ],
  },
  {
    key: 'interest-investigation',
    icon: 'icon-shoucijintiao',
    label: 'Compliance Screening',
    children: [
      {
        key: '/supplier/interest-investigation',
        icon: 'icon-toubiaozhongbiao',
        label: 'Interest Relation Screening',
      },
      {
        key: '/supplier/potential-investigation',
        icon: 'icon-toubiaozhongbiao',
        label: 'Potential Relation Screening',
      },
    ],
  },
  {
    key: 'partnerShip',
    icon: 'icon-keshangguanli',
    label: `Third Party`,
    children: [
      {
        key: '/supplier/third-party/partners',
        label: `Third Party List`,
      },
      {
        key: '/supplier/third-party/annual-review',
        label: `Risk Annual Review`,
      },
    ],
  },

  {
    key: 'blacklist',
    icon: 'icon-heimingdanguanli',
    label: 'Blacklist',
    children: [
      {
        key: '/supplier/blacklist/internal-blacklist',
        label: 'Internal Blacklist',
      },
      {
        key: '/supplier/blacklist/external-blacklist',
        label: 'External Blacklist',
      },
    ],
  },
  {
    key: '/supplier/staff',
    icon: 'icon-renyuanguanli',
    label: 'Employee List',
  },
  {
    key: '/supplier/analysis-dashboard',
    icon: 'icon-zengchangshuai',
    label: 'Dashboard',
  },
  {
    key: '/supplier/bidding-warning',
    icon: 'icon-toubiaoyujing',
    label: 'Bidding Warning',
  },
];

const SINO_PHARM_MENU_ITEMS = [
  {
    key: '/supplier/dashboard',
    icon: 'icon-gongzuotai',
    label: 'Home',
  },
  {
    key: 'access',
    icon: 'icon-zhunrupaicha',
    label: 'Risk Checks',
    children: [
      {
        key: '/supplier/investigation',
        label: 'Access Checks',
      },
      {
        key: '/supplier/batch-investigation',
        label: 'Batch Checks',
      },
      {
        key: '/supplier/investigation-history',
        label: 'Check Records',
      },
    ],
  },
  // 合作监控
  // 不和路由保持一个名字，不然会resolve到子路由，导致菜单显示出错
  {
    key: 'risk-watching',
    icon: 'icon-chixujintiao2',
    label: 'Cooperative Monitor',
    children: [
      {
        key: '/supplier/risk-trends',
        label: 'Check Trends',
      },
      {
        key: '/supplier/risk-monitor',
        label: 'Company List',
      },
    ],
  },
  {
    key: 'Tender',
    icon: 'icon-toubiaozhongbiao',
    label: 'Tender Screening',
    children: [
      {
        key: '/supplier/bidding-investigation',
        icon: 'icon-toubiaozhongbiao',
        label: 'Tender Screening',
      },
      {
        key: '/supplier/bidding-investigation-history',
        icon: 'icon-toubiaozhongbiao',
        label: 'Tender Screening Records',
      },
    ],
  },
  {
    key: 'interest-investigation',
    icon: 'icon-shoucijintiao',
    label: 'Compliance Screening',
    children: [
      {
        key: '/supplier/interest-investigation',
        icon: 'icon-toubiaozhongbiao',
        label: 'Interest Relation Screening',
      },
      {
        key: '/supplier/potential-investigation',
        icon: 'icon-toubiaozhongbiao',
        label: 'Potential Relation Screening',
      },
    ],
  },
  // {
  //   key: 'partnerShip',
  //   icon: 'icon-keshangguanli',
  //   label: `Third Party`,
  //   children: [
  //     {
  //       key: '/supplier/third-party/partners',
  //       label: `Third Party List`,
  //     },
  //     {
  //       key: '/supplier/third-party/annual-review',
  //       label: `Risk Annual Review`,
  //     },
  //   ],
  // },

  {
    key: '/supplier/third-party-partners',
    icon: 'icon-keshangguanli',
    label: `Third Party List`,
  },

  {
    key: '/supplier/third-party/annual-review/risk',
    icon: 'icon-a-wenjiansousuoxian',
    label: `Risk Annual Review`,
  },

  {
    key: 'blacklist',
    icon: 'icon-heimingdanguanli',
    label: 'Blacklist',
    children: [
      {
        key: '/supplier/blacklist/internal-blacklist',
        label: 'Internal Blacklist',
      },
      {
        key: '/supplier/blacklist/external-blacklist',
        label: 'External Blacklist',
      },
    ],
  },
  {
    key: '/supplier/staff',
    icon: 'icon-renyuanguanli',
    label: 'Employee List',
  },
  {
    key: '/supplier/analysis-dashboard',
    icon: 'icon-zengchangshuai',
    label: 'Dashboard',
  },
  // {
  //   key: '/supplier/bidding-warning',
  //   icon: 'icon-toubiaoyujing',
  //   label: 'Bidding Warning',
  // },
];

const MENU_LIST = {
  default: MENU_ITEMS,
  zeiss: ZEISS_MENU_ITEMS,
  sinoPharm: SINO_PHARM_MENU_ITEMS,
};

/**
 * 招标排查
 */
const biddingInvestigationRoutes = () => [
  {
    path: 'bidding-investigation',
    redirect: () => {
      if (hasPermission([2110])) {
        return {
          name: 'bidding-investigation',
        };
      }
      return {
        name: 'bidding-batch',
      };
    },
  },
  {
    path: 'bidding-investigation/single',
    name: 'bidding-investigation',
    component: () => import('@/pages/supplier/bidding-investigation/bidding-investigation-single'),
    meta: {
      title: '招标排查',
      permission: [2110],
    },
  },
  {
    path: 'bidding-investigation/batch',
    name: 'bidding-batch',
    component: () => import('@/pages/supplier/bidding-investigation/bidding-batch'),
    meta: {
      title: '批量招标排查',
      permission: [2114],
    },
  },
  {
    path: 'bidding-investigation/verify',
    name: 'bidding-investigation-verify',
    component: () => import('@/pages/supplier/bidding-investigation-verify'),
    meta: {
      title: '招标排查核实',
      permission: [2110],
    },
  },
  {
    path: 'bidding-investigation-history',
    name: 'bidding-investigation-history',
    component: () => import('@/pages/supplier/bidding-investigation-history'),
    meta: {
      title: '历史记录',
      permission: [2113],
    },
  },
  // 招标排查-排查中页面
  {
    path: ':type(bidding-investigation|bidding-investigation-history)/loading',
    name: 'bidding-investigation-loading',
    component: () => import('@/pages/supplier/bidding-investigation-detail/bidding'),
    meta: {
      title: '招标排查详情',
      permission: [2110],
    },
  },
  // 招标排查-排查失败页面
  {
    path: ':type(bidding-investigation|bidding-investigation-history)/failed',
    name: 'bidding-investigation-failed',
    component: () => import('@/pages/supplier/bidding-investigation-detail/failed'),
    meta: {
      title: '招标排查详情',
      permission: [2110],
    },
  },
  {
    path: ':type(bidding-investigation|bidding-investigation-history)/detail',
    name: 'bidding-investigation-detail',
    component: () => import('@/pages/supplier/bidding-investigation-detail'),
    meta: {
      title: '招标排查详情',
      permission: [2110],
    },
  },
  {
    path: 'bidding-investigation/batch-detail',
    name: 'bidding-batch-detail',
    component: () => import('@/pages/supplier/bidding-batch-detail'),
    meta: {
      title: '批量招标排查详情',
      permission: [2114],
    },
  },
];

/**
 * 特定利益关系排查
 */
const interestInvestigationRoutes = () => [
  {
    path: 'interest-investigation',
    redirect: () => {
      if (hasPermission([InterestPermissionCode.single])) {
        return {
          name: 'interest-investigation',
        };
      }
      if (hasPermission([InterestPermissionCode.batch])) {
        return {
          name: 'interest-batch',
        };
      }
      return {
        name: 'interest-investigation-history',
      };
    },
  },
  {
    path: 'interest-investigation/single',
    name: 'interest-investigation',
    component: () => import('@/pages/supplier/interest-investigation/interest-investigation-single'),
    meta: {
      title: '特定利益关系排查',
      permission: [InterestPermissionCode.single],
    },
  },
  {
    path: 'interest-investigation/batch',
    name: 'interest-batch',
    component: () => import('@/pages/supplier/interest-investigation/interest-investigation-batch'),
    meta: {
      title: '批量特定利益关系排查',
      permission: [InterestPermissionCode.batch],
    },
  },
  {
    path: 'interest-investigation/batch-detail',
    name: 'interest-batch-detail',
    component: () => import('@/pages/supplier/interest-investigation/interest-batch-detail'),
    meta: {
      title: '批量特定利益关系排查详情',
      permission: [InterestPermissionCode.batch],
    },
  },
  {
    path: 'interest-investigation/history',
    name: 'interest-investigation-history',
    component: () => import('@/pages/supplier/interest-investigation/interest-investigation-history'),
    meta: {
      title: '历史记录',
      permission: [InterestPermissionCode.history],
    },
  },
  {
    path: 'interest-investigation/verify',
    name: 'interest-investigation-verify',
    component: () => import('@/pages/supplier/interest-investigation/interest-investigation-verify'),
    meta: {
      title: '特定利益关系排查核实',
      permission: [InterestPermissionCode.single],
    },
  },
  {
    path: 'interest-investigation/:type(search|history)/detail',
    name: 'interest-investigation-detail',
    component: () => import('@/pages/supplier/interest-investigation/interest-investigation-detail'),
    meta: {
      title: '特定利益关系排查详情',
      permission: [InterestPermissionCode.single, InterestPermissionCode.history],
    },
  },
  /**
   * 特定利益关系排查：排查中
   */
  {
    path: 'interest-investigation/:type(search|history)/processing',
    name: 'interest-investigation-processing',
    component: () => import('@/pages/supplier/interest-investigation/interest-investigation-processing'),
    meta: {
      title: '特定利益关系排查详情',
      permission: [InterestPermissionCode.single],
    },
  },
  /**
   * 特定利益关系排查：排查失败
   */
  {
    path: 'interest-investigation/:type(search|history)/failed',
    name: 'interest-investigation-failed',
    component: () => import('@/pages/supplier/interest-investigation/interest-investigation-failed'),
    meta: {
      title: '特定利益关系排查详情',
      permission: [InterestPermissionCode.single],
    },
  },
];

/**
 * 潜在利益冲突排查
 */
const potentialInvestigationRoutes = () => [
  {
    path: 'potential-investigation',
    redirect: () => {
      // if (hasPermission([PotentialPermissionCode.single])) {
      //   return {
      //     name: 'potential-investigation',
      //   };
      // }
      if (hasPermission([PotentialPermissionCode.batch])) {
        return {
          name: 'potential-batch',
        };
      }
      return {
        name: 'potential-investigation-history',
      };
    },
  },
  // {
  //   path: 'potential-investigation/single',
  //   name: 'potential-investigation',
  //   component: () => import('@/pages/supplier/potential-investigation/potential-investigation-single'),
  //   meta: {
  //     title: '潜在利益冲突排查',
  //     permission: [PotentialPermissionCode.single],
  //   },
  // },
  {
    path: 'potential-investigation/batch',
    name: 'potential-batch',
    component: () => import('@/pages/supplier/potential-investigation/potential-investigation-batch'),
    meta: {
      title: '批量潜在利益冲突排查',
      permission: [PotentialPermissionCode.batch],
    },
  },
  {
    path: 'potential-investigation/batch-detail',
    name: 'potential-batch-detail',
    component: () => import('@/pages/supplier/potential-investigation/potential-batch-detail'),
    meta: {
      title: '批量潜在利益冲突排查详情',
      permission: [PotentialPermissionCode.batch],
    },
  },
  // 潜在利益冲突搜索列表
  // {
  //   path: 'potential-investigation/search',
  //   name: 'potential-investigation-search',
  //   component: () => import('@/pages/supplier/investigate-list'),
  //   meta: {
  //     widthSearchKeywords: true, // 是否把url中的keyword带入搜索框
  //     title: '搜索结果',
  //   },
  //   props: {
  //     type: 'potential',
  //   },
  // },
  {
    path: 'potential-investigation/history',
    name: 'potential-investigation-history',
    component: () => import('@/pages/supplier/potential-investigation/potential-investigation-history'),
    meta: {
      title: '历史记录',
      permission: [PotentialPermissionCode.history],
    },
  },
  {
    path: 'potential-investigation/:type(search|search-list|history|batch-detail)/detail/:id',
    name: 'potential-investigation-detail',
    component: () => import('@/pages/supplier/potential-investigation/potential-investigation-detail'),
    meta: {
      title: '潜在利益冲突排查详情',
      permission: [PotentialPermissionCode.single],
    },
  },
];

export const supplierRoutes = (): RouteConfig[] => [
  {
    path: '/supplier',
    component: SidebarMenuLayout,
    props: {
      source: 'supplier',
      menu: MENU_ITEMS,
      menuList: MENU_LIST,
      pageTitle: undefined,
    },
    meta: {
      title: '供应商',
    },
    children: [
      {
        path: '',
        redirect: {
          name: 'index-page',
        },
      },
      {
        path: 'dashboard',
        name: 'dashboard',
        component: () => import('@/pages/supplier/dashboard'),
        meta: {
          title: '个人工作台',
          permission: [2137],
        },
      },

      {
        path: 'investigation',
        name: 'supplier-investigation',
        component: () => import('@/pages/supplier/investigation'),
        meta: {
          title: '准入排查',
          permission: [2001],
        },
      },
      // 批量准入排查
      {
        path: 'batch-investigation',
        name: 'batch-investigation',
        meta: {
          title: '批量排查',
          permission: [2011],
        },
        component: () => import('@/pages/supplier/risk-assessment-batch/default'),
      },
      // 上传文件核实列表 带\/等要进行转义。不然router.push的时候fullPath形成的有误，resolve的话就不用转义了
      {
        path: ':type(batch-investigation|third-party/partners|third-party-partners|blacklist/internal-blacklist|risk-monitor|potential-investigation/batch)/upload-confirm',
        name: 'upload-confirm',
        meta: {
          title: '企业核实',
        },
        component: () => import('@/pages/supplier/risk-assessment-batch/upload-confirm'),
      },
      // 批量准入排查结果
      {
        path: 'batch-investigation/detail/:id',
        name: 'batch-investigation-detail',
        component: () => import('@/pages/supplier/risk-assessment-detail'),
        meta: {
          title: '批量排查结果',
          // keepAlive: true,
          permission: [2011],
        },
      },
      // 排查记录
      {
        path: 'investigation-history',
        name: 'investigation-history',
        component: () => import('@/pages/supplier/investigation-history'),
        meta: {
          title: '排查记录',
          // keepAlive: true,
          permission: [2021],
        },
      },

      {
        path: 'risk-trends',
        redirect: () => {
          if (hasPermission([2091])) {
            return {
              name: 'risk-trends-overview',
            };
          }
          return {
            name: 'risk-trends-specific',
          };
        },
      },
      {
        path: 'risk-trends/overview',
        name: 'risk-trends-overview',
        component: () => import('@/pages/supplier/risk-trends/risk-trends-overview'),
        meta: {
          title: '监控动态',
          permission: [2091],
        },
      },
      {
        path: 'risk-trends/specific',
        name: 'risk-trends-specific',
        component: () => import('@/pages/supplier/risk-trends/risk-trends-specific'),
        meta: {
          title: '舆情动态',
          permission: [2092],
        },
      },

      {
        path: 'risk-monitor',
        name: 'risk-monitor',
        component: () => import('@/pages/supplier/risk-trends/risk-monitor'),
        meta: {
          title: '监控列表',
          permission: [2101],
        },
      },

      /**
       * 招标排查
       */
      ...biddingInvestigationRoutes(),

      /**
       * 特定利益关系排查
       */
      ...interestInvestigationRoutes(),

      /**
       * 潜在利益冲突排查
       */
      ...potentialInvestigationRoutes(),

      {
        path: '(third-party/partners|third-party-partners)',
        name: 'supplier-partners',
        component: () => import('@/pages/supplier/third-party/partners'),
        meta: {
          title: '第三方列表',
          permission: [2031],
        },
      },
      {
        path: 'third-party/annual-review',
        redirect: () => {
          return {
            name: 'risk-annual-review',
          };
        },
      },
      {
        path: 'third-party/annual-review/risk',
        name: 'risk-annual-review',
        component: () => import('@/pages/supplier/third-party/annual-review'),
        meta: {
          title: '风险巡检',
          permission: [2014],
        },
      },
      {
        path: 'third-party/annual-review/record',
        name: 'annual-review-record',
        component: () => import('@/pages/supplier/third-party/annual-review-record'),
        meta: {
          title: '巡检记录',
          permission: [2014],
        },
      },
      {
        path: 'third-party/annual-review/record/:id',
        name: 'annual-review-investigate',
        component: () => import('@/pages/supplier/risk-assessment-detail'),
        meta: {
          title: '巡检结果',
          // keepAlive: true,
          permission: [2014],
        },
      },
      {
        path: 'third-party/annual-review/record/detail/:id([a-z0-9,]+)',
        name: 'annual-review-investigate-detail',
        component: () => import('@/pages/supplier/investigate-detail/default'),
        meta: {
          title: '排查详情',
          permission: [2014],
        },
      },
      {
        path: 'blacklist',
        redirect: () => {
          if (hasPermission([2041])) {
            return {
              name: 'internal-blacklist',
            };
          }
          return {
            name: 'external-blacklist',
          };
        },
      },
      {
        path: 'blacklist/internal-blacklist',
        name: 'internal-blacklist',
        component: () => import('@/pages/supplier/blacklist/internal-blacklist'),
        meta: {
          title: '内部黑名单',
          permission: [2041],
        },
      },
      {
        path: 'blacklist/external-blacklist',
        name: 'external-blacklist',
        component: () => import('@/pages/supplier/blacklist/external-blacklist'),
        meta: {
          title: '外部黑名单',
          permission: [2051],
        },
      },
      {
        path: 'staff',
        name: 'supplier-staff',
        component: () => import('@/pages/supplier/staff'),
        meta: {
          title: '人员管理',
          permission: [2061],
        },
      },

      // 准入排查搜索列表
      {
        path: 'investigation/search',
        name: 'investigation-search',
        component: () => import('@/pages/supplier/investigate-list'),
        meta: {
          widthSearchKeywords: true, // 是否把url中的keyword带入搜索框
          title: '搜索结果',
          // keepAlive: true,
          permission: [2001],
        },
        props: {
          type: 'investigation',
        },
      },

      // 企业搜索列表
      {
        path: 'company/search',
        name: 'company-search',
        component: () => import('@/pages/supplier/investigate-list'),
        meta: {
          widthSearchKeywords: true, // 是否把url中的keyword带入搜索框
          title: '搜索结果',
        },
        props: {
          type: 'company',
        },
      },
      {
        path: ':type(investigation-history|dynamicDetails|investigation|batch-investigation|risk-trends)/:id([a-z0-9,]+)',
        name: 'supplier-investigate-detail',
        component: () => import('@/pages/supplier/investigate-detail/default'),
        meta: {
          title: '排查详情',
          permission: [2001, 20062],
        },
      },
      {
        path: 'hide-company',
        name: 'supplier-hide-company',
        component: () => import('@/pages/supplier/investigate-detail/hide'),
        meta: {
          title: '排查详情',
          permission: [2001],
        },
      },
      {
        path: 'analysis-dashboard',
        name: 'analysis-dashboard',
        component: () => import('@/pages/supplier/analysis-dashboard'),
        meta: {
          title: '分析看板',
          permission: [2071],
        },
      },
      {
        path: 'bidding-warning',
        name: 'bidding-warning',
        component: () => import('@/pages/supplier/bidding-warning'),
        meta: {
          title: '投标预警',
          permission: [2135],
        },
      },
      {
        path: 'bidding-warning/detail/:id',
        name: 'bidding-warning-detail',
        component: () => import('@/pages/supplier/bidding-warning-detail'),
        meta: {
          title: '投标详情',
          permission: [2135],
        },
      },
    ],
  },
];
