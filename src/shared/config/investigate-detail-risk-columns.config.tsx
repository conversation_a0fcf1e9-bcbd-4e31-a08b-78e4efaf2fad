import moment from 'moment';
import { cloneDeep, get, isObject } from 'lodash';
import { Tooltip } from 'ant-design-vue';

import { BLACKLIST_DURATION_MAP } from '@/shared/constants/black-list.constants';
import { dateFormat } from '@/utils/format';
import { numberToHuman } from '@/utils/number-formatter';
import { getCurrentLocale } from '@/utils/locale';
import QIcon from '@/components/global/q-icon';
import { getCertificationStyle } from '@/config/risk.config';
import QGlossaryInfo from '@/components/global/q-glossary-info';
import { billAcceptanceRiskStatusMap } from '@/apps/setting/pages/investigation-setting/widgets/risk-settings/risk-settings.config';

import { getHighLightDifferent } from '../constants/string-buffer';

// 持股比例处理
const renderStockPercent = (item) => {
  const number = parseFloat(item.stockPercent);
  if (!number) {
    return '';
  }
  return `${number}%`;
};

export const blacklistDurationFormatter = (item) => {
  const value = isObject(item) ? get(item, 'duration') : item;
  if (value === undefined || value === null) {
    return '-';
  }
  // duration后端返回了两种，一种是时间戳，一种是type，type只有0-8，大于100默认它是时间戳，做处理
  if (value > 100) {
    return moment
      .duration(value * 1000)
      .locale(getCurrentLocale())
      .humanize();
  }
  return BLACKLIST_DURATION_MAP[value] ?? '-';
};

// ------------- 经营合规风险 -------------

// 简易注销
const BusinessAbnormal2Columns = [
  {
    title: '公告名称',
    scopedSlots: {
      customRender: 'annoAction',
    },
  },
  {
    title: '公告开始日期-结束日期',
    dataIndex: 'publishDate',
  },
  {
    title: '简易注销结果',
    dataIndex: 'resultContent',
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: {
      customRender: 'action',
    },
  },
];

// 被列入经营异常(未移出)
const BusinessAbnormal3Columns = [
  {
    title: '列入对象',
    dataIndex: 'SubjectInfo',
    scopedSlots: {
      customRender: 'EntityLinksAndJob',
    },
  },
  {
    title: '列入日期',
    width: 103,
    dataIndex: 'PublishDate',
    scopedSlots: {
      customRender: 'date',
    },
    sorter: true,
    key: 'currencedate',
  },
  { title: '作出决定机关(列入)', dataIndex: 'Court' },
  { title: '列入经营异常名录原因', dataIndex: 'CaseReason' },
];

// 疑似停业歇业停产或被吊销证照
const BusinessAbnormal5Columns = [
  {
    title: '决定书文号',
    width: 210,
    scopedSlots: { customRender: 'docNo' },
  },
  {
    title: '违法事实',
    dataIndex: 'punishReason',
    scopedSlots: { customRender: 'clampcontent' },
  },
  {
    title: '处罚结果',
    width: 200,
    dataIndex: 'punishResult',
    placeholder: '未公示',
    scopedSlots: { customRender: 'clampcontent' },
  },
  // {
  //   title: '处罚金额(元)',
  //   dataIndex: 'Amount',
  //   scopedSlots: { customRender: 'money' },
  // },
  {
    title: '处罚单位',
    width: 150,
    dataIndex: 'punishOffice',
  },
  {
    title: '处罚日期',
    width: 120,
    dateFormat: true,
    dataIndex: 'punishDate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  // {
  //   title: '原文',
  //   width: 68,
  //   scopedSlots: { customRender: 'originalSource' },
  // },
];

// ------------- 经营合规风险 -------------

// ------------- 法律风险 -------------

// 税收违法
const TaxationOffencesColumns = [
  {
    title: '发布日期',
    dataIndex: 'PublishDate',
    width: 103,
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '所属税务机关',
    dataIndex: 'Court',
  },
  {
    title: '案件性质',
    dataIndex: 'ActionRemark',
  },
  {
    title: '主要违法事实',
    dataIndex: 'CaseReason',
    scopedSlots: { customRender: 'clampcontent' },
  },
  {
    title: () => {
      return (
        <div>
          <span>罚款金额(元)</span>
          <Tooltip overlayClassName="self-customed" title="该数据从公示结果解析得出，仅供参考，并不代表企查查任何明示、暗示之观点或保证。">
            <QIcon type="icon-icon_zhushi" style="color: #d8d8d8;margin-left: 4px;"></QIcon>
          </Tooltip>
        </div>
      );
    },
    dataIndex: 'amount',
    sorter: true,
    scopedSlots: { customRender: 'money' },
  },
  {
    title: '法律依据及处理处罚情况',
    dataIndex: 'Title',
    scopedSlots: { customRender: 'clampcontent' },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'action' },
  },
];

// 股权冻结
const FreezeEquityColumns = [
  {
    title: '执行通知书文号',
    // RA-1787: 内蒙古新大地建设集团股份有限公司 bd35e32c0f98d0c99c0b7096e6f5cf42
    scopedSlots: { customRender: 'CaseSearchId' },
  },
  {
    title: '被执行人',
    dataIndex: 'Name',
    scopedSlots: { customRender: 'pledgName' },
  },
  {
    title: '冻结股权标的企业',
    dataIndex: 'Pledgor',
    scopedSlots: { customRender: 'pledgPledgor' },
  },
  {
    title: '股权数额',
    width: 180,
    dataIndex: 'AmountDesc',
    scopedSlots: { customRender: 'thousandsNumber' },
  },
  {
    title: '执行法院',
    dataIndex: 'Court',
  },
  {
    title: '类型|状态',
    width: 110,
    customRender: (item) => {
      // 兼容老数据，老数据没有statusdesc
      const status = item.statusdesc || item.ExecuteStatus;
      return status ? `${item.TypeDesc} | ${status}` : '-';
    },
  },
  {
    title: '冻结起止日期',
    width: 170,
    customRender: (record) => {
      const start = dateFormat(record.LianDate * 1000);
      if (start === '-' || !start) {
        return '-';
      }
      const end = dateFormat(record.ActionRemark * 1000);
      return `${start}至${end}`;
    },
  },
  {
    title: '公示日期',
    dataIndex: 'PublishDate',
    width: 90,
    sorter: true,
    customRender: (date) => {
      return dateFormat(date * 1000);
    },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'action' },
  },
];

// 被列入失信被执行人
const PersonCreditCurrentColumns = [
  {
    title: '案号',
    scopedSlots: { customRender: 'CaseNo' },
  },
  {
    title: () => {
      return (
        <div>
          <span>疑似申请执行人</span>
          <Tooltip
            overlayClassName="self-customed"
            title="疑似申请执行人是基于司法案件的基础数据维度综合分析得出，仅供参考，并不代表企查查任何明示、暗示之观点或保证"
          >
            <QIcon type="icon-icon_zhushi" style="color: #d8d8d8;margin-left: 4px;"></QIcon>
          </Tooltip>
        </div>
      );
    },
    dataIndex: 'SqrInfo',
    scopedSlots: { customRender: 'qentityArr' },
  },
  {
    title: '执行法院',
    width: 170,
    dataIndex: 'ExecuteGov',
  },
  // {
  //   title: '执行依据文号',
  //   width: 200,
  //   customRender: (item) => item.ExecuteNo || '-',
  // },
  {
    title: () => {
      return (
        <div>
          <span>涉案金额(元)</span>
          <Tooltip
            overlayClassName="self-customed"
            title="涉案金额是基于相关生效法律文书确定义务分析得出，仅供参考，并不代表企查查任何明示、暗示之观点或保证。"
          >
            <QIcon type="icon-icon_zhushi" style="color: #d8d8d8;margin-left: 4px;"></QIcon>
          </Tooltip>
        </div>
      );
    },
    width: 120,
    sorter: true,
    dataIndex: 'Amount',
    scopedSlots: { customRender: 'money' },
  },
  {
    title: '履行情况',
    width: 90,
    customCell: (item) => {
      return {
        domProps: {
          innerHTML: item.executeStatus || item.ExecuteStatus || '-',
        },
      };
    },
  },
  { title: '失信行为', dataIndex: 'ActionRemark' },
  {
    title: '立案日期',
    key: 'liandate',
    sorter: true,
    dataIndex: 'LiAnDate',
    width: 103,
    customRender: (item) => {
      return dateFormat(item);
    },
  },
  {
    title: '发布日期',
    width: 103,
    key: 'pubdate',
    dataIndex: 'PublicDate',
    sorter: true,
    customRender: (item) => {
      return dateFormat(item);
    },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'action' },
  },
];

// 历史失信被执行人
const PersonCreditColumns = [
  {
    title: '案号',
    scopedSlots: { customRender: 'CaseNo' },
  },
  {
    title: '疑似申请执行人',
    dataIndex: 'SqrInfo',
    scopedSlots: { customRender: 'qentityArr' },
  },
  {
    title: '执行法院',
    customCell: (item) => {
      return {
        domProps: {
          innerHTML: item.Executegov || item.ExecuteGov || (Array.isArray(item.Court) ? item.Court[0] : item.Court || '-'),
        },
      };
    },
  },
  // {
  //   title: '执行依据文号',
  //   customRender: (item) => item.ExecuteNo || '-',
  // },
  {
    title: '涉案金额(元)',
    width: 120,
    sorter: true,
    key: 'amount',
    dataIndex: 'Amount',
    scopedSlots: { customRender: 'money' },
  },
  {
    title: '履行情况',
    customCell: (item) => {
      return {
        domProps: {
          innerHTML: item.executeStatus || item.ExecuteStatus || '-',
        },
      };
    },
  },
  { title: '失信行为', dataIndex: 'ActionRemark' },
  {
    title: '立案日期',
    // key: 'liandate',
    // sorter: true,
    width: 103,
    customRender: (item) => {
      return dateFormat(item.lianDate || item.LianDate || item.LiAnDate);
    },
  },
  {
    title: '发布日期',
    width: 103,
    key: 'pubdate',
    sorter: true,
    dataIndex: 'PublicDate',
    customRender: (item) => {
      return dateFormat(item);
    },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'action' },
  },
];

// 主要负责人被列入失信被执行人
const MainMembersPersonCreditCurrent = [
  {
    title: '案号',
    scopedSlots: { customRender: 'CaseNo' },
  },
  {
    title: '疑似申请执行人',
    dataIndex: 'SqrInfo',
    scopedSlots: { customRender: 'qentityArr' },
  },
  {
    title: '执行法院',
    customCell: (item) => {
      return {
        domProps: {
          innerHTML: item.Executegov || item.ExecuteGov || (Array.isArray(item.Court) ? item.Court[0] : item.Court || '-'),
        },
      };
    },
  },
  {
    title: '执行依据文号',
    scopedSlots: { customRender: 'OrgNo' },
  },
  {
    title: '失信被执行人',
    width: 140,
    dataIndex: 'SubjectInfo',
    scopedSlots: { customRender: 'EntityLinksAndJob' },
  },
  {
    title: '涉案金额(元)',
    width: 120,
    // sorter: true,
    key: 'amount',
    dataIndex: 'Amount',
    scopedSlots: { customRender: 'money' },
  },
  {
    title: '履行情况',
    customCell: (item) => {
      return {
        domProps: {
          innerHTML: item.executeStatus || item.ExecuteStatus || '-',
        },
      };
    },
  },
  { title: '失信行为', dataIndex: 'ActionRemark' },
  {
    title: '立案日期',
    key: 'liandate',
    sorter: true,
    dataIndex: 'LianDate',
    width: 103,
    customRender: (_, item) => {
      return dateFormat(item.lianDate || item.LianDate || item.LiAnDate);
    },
  },
  {
    title: '发布日期',
    width: 103,
    key: 'publishdate',
    sorter: true,
    dataIndex: 'PublishDate',
    customRender: (_, item) => {
      return dateFormat(item.publishDate || item.PublishDate || item.PublicDate);
    },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'action' },
  },
];

// 子公司被列入失信被执行人
const SubsidiaryPersonCreditCurrentColumns = [
  {
    title: '案号',
    scopedSlots: { customRender: 'CaseNo' },
  },
  {
    title: '被执行子公司',
    scopedSlots: { customRender: 'NameAndKeyNo' },
  },
  {
    title: '执行法院',
    customCell: (item) => {
      return {
        domProps: {
          innerHTML: Array.isArray(item.Court) ? item.Court[0] : item.Court,
        },
      };
    },
  },
  {
    title: '执行依据文号',
    customRender: (item) => item.ExecuteNo || '-',
  },
  {
    title: '涉案金额(元)',
    width: 120,
    key: 'amount',
    dataIndex: 'Amount',
    scopedSlots: { customRender: 'money' },
  },
  {
    title: '履行情况',
    customCell: (item) => {
      return {
        domProps: {
          innerHTML: item.executeStatus || item.ExecuteStatus || '-',
        },
      };
    },
  },
  { title: '失信行为', dataIndex: 'ActionRemark' },
  {
    key: 'liandate',
    title: '立案日期',
    width: 103,
    customRender: (item) => {
      return dateFormat(item.lianDate || item.LianDate);
    },
  },
  {
    title: '发布日期',
    width: 103,
    key: 'publishdate',
    customRender: (item) => {
      return dateFormat(item.publishDate || item.PublishDate);
    },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'action' },
  },
];

// 被列入限制高消费名单 主要人员被列入限制高消费 历史限制高消费
const RestrictedConsumptionColumns = [
  {
    title: '案号',
    width: 200,
    scopedSlots: { customRender: 'CaseNo' },
  },
  {
    title: '限消令对象',
    dataIndex: 'SubjectInfo',
    scopedSlots: { customRender: 'EntityLinksAndJob' },
  },
  {
    title: '关联对象',
    width: 160,
    scopedSlots: { customRender: 'pledgorInfo' },
  },
  {
    title: '申请人',
    scopedSlots: { customRender: 'applicant' },
  },
  {
    title: '立案日期',
    width: 100,
    dataIndex: 'LianDate',
    scopedSlots: { customRender: 'date' },
    sorter: true,
    key: 'liandate',
  },
  {
    title: '发布日期',
    width: 100,
    dataIndex: 'PublishDate',
    scopedSlots: { customRender: 'date' },
    sorter: true,
    key: 'publishdate',
  },
  {
    title: '原文',
    width: 90,
    scopedSlots: { customRender: 'originalSource' },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'urlAction' },
  },
];

// 主要人员被列入限制出境
const RestrictedOutboundColumns = [
  {
    title: '案号',
    scopedSlots: { customRender: 'CaseNo' },
  },
  {
    title: '限制出境对象',
    dataIndex: 'SubjectInfo',
    width: 140,
    scopedSlots: { customRender: 'EntityLinksAndJob' },
  },
  {
    title: '被执行人',
    dataIndex: 'PledgorInfo',
    scopedSlots: { customRender: 'entityLinks' },
  },
  {
    title: '被执行人地址',
    dataIndex: 'Address',
  },
  {
    title: '申请执行人',
    dataIndex: 'ApplicantInfo',
    scopedSlots: { customRender: 'entityLinks' },
  },
  {
    title: '执行标的金额(元)',
    dataIndex: 'Amount',
    scopedSlots: { customRender: 'money' },
    sorter: true,
    key: 'amount',
  },
  {
    title: '执行法院',
    dataIndex: 'Court',
  },
  {
    title: '发布日期',
    width: 103,
    dataIndex: 'PublishDate',
    scopedSlots: { customRender: 'date' },
    sorter: true,
    key: 'publishdate',
  },
];

// 破产重整
const BankruptcyColumns = [
  {
    title: '案号',
    width: 240,
    scopedSlots: { customRender: 'CaseNo' },
  },
  {
    title: '破产类型',
    width: 120,
    dataIndex: 'CaseReasonType',
  },
  {
    width: 224,
    title: '被申请人',
    dataIndex: 'SubjectInfo',
    scopedSlots: { customRender: 'EntityLinksAndJob' },
  },
  {
    width: 224,
    title: '申请人',
    scopedSlots: { customRender: 'applicant' },
  },
  {
    title: '经办法院',
    width: 149,
    dataIndex: 'Court',
  },
  {
    title: '公开日期',
    key: 'publishdate',
    sorter: true,
    dataIndex: 'PublishDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'action' },
  },
];

// 被执行人信息
const PersonExecutionColumns = [
  {
    title: '案号',
    customRender: (text, record) => {
      if (record.CaseNo && record.CaseSearchId) {
        // NOTE: 传递 anno 不能滚动到对应的位置（专业版未与C端同步）
        return (
          <a href={`/embed/courtCaseDetail?caseId=${record.CaseSearchId}&anno=${record.CaseNo}`} target="_blank">
            {record.CaseNo}
          </a>
        );
      }
      return <span>{record.CaseNo || '-'}</span>;
    },
  },
  // 被执行人、
  {
    title: '被执行人',
    dataIndex: 'Name',
    customRender: (text, record) => {
      if (Array.isArray(record.NameAndKeyNos) && record.NameAndKeyNos.length >= 1) {
        return record.NameAndKeyNos.map((item) => {
          return (
            <div key={item.KeyNo}>
              <a href={`/embed/companyDetail?keyNo=${item.KeyNo}&title=${item.Name}`} target="_blank">
                {item.Name}
              </a>
            </div>
          );
        });
      }
      return <div>{text}</div>;
    },
  },
  // 执行标的（元）、
  {
    title: '执行标的(元)',
    dataIndex: 'BiaoDi',
    scopedSlots: { customRender: 'money' },
    sorter: true,
    key: 'biaodiamount',
  },
  // 执行法院、
  { title: '执行法院', dataIndex: 'ExecuteGov' },
  // 立案日期、内容（详情）
  {
    title: '立案日期',
    width: 103,
    dataIndex: 'LiAnDate',
    scopedSlots: { customRender: 'date' },
    sorter: true,
    key: 'liandate',
  },
  // 详情
  {
    title: '详情',
    scopedSlots: { customRender: 'CaseNo' },
  },
];

// ------------- 法律风险 -------------

// ------------- 行政监管风险 -------------

// 行政处罚 环保处罚 税务处罚
const AdministrativePenaltiesColumns = [
  {
    title: '决定书文号',
    width: 200,
    scopedSlots: { customRender: 'casenoWithTag' },
  },
  {
    title: '违法事实',
    width: 200,
    dataIndex: 'CaseReason',
    scopedSlots: { customRender: 'clampcontent' },
  },
  {
    title: '处罚结果',
    width: 200,
    dataIndex: 'Title',
    placeholder: '未公示',
    scopedSlots: { customRender: 'clampcontent' },
  },
  {
    title: '处罚金额(元)',
    width: 100,
    dataIndex: 'Amount',
    scopedSlots: { customRender: 'money' },
    sorter: true,
    key: 'amount',
  },
  {
    title: '处罚单位',
    width: 150,
    dataIndex: 'Court',
  },
  {
    title: '处罚日期',
    width: 100,
    dateFormat: true,
    dataIndex: 'PunishDate',
    scopedSlots: {
      customRender: 'date',
    },
    sorter: true,
    key: 'punishdate',
  },
  {
    title: '发布日期',
    width: 100,
    dateFormat: true,
    dataIndex: 'PublishDate',
    scopedSlots: {
      customRender: 'date',
    },
    sorter: true,
  },
  {
    title: '原文',
    width: 90,
    scopedSlots: { customRender: 'originalSource' },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'urlAction' },
  },
];

// 涉及商业贿赂、垄断行为或政府采购活动违法行为行政处罚
// 3年前涉及商业贿赂、垄断行为或政府采购活动违法行为行政处罚
export const AdministrativePenalties2Columns = [
  {
    title: '处罚对象名称',
    dataIndex: 'SubjectInfo',
    scopedSlots: { customRender: 'EntityLinksAndJob' },
  },
  {
    title: '决定文书号',
    width: 200,
    dataIndex: 'caseno',
  },
  {
    title: '违法事实',
    width: 200,
    dataIndex: 'casereason',
    scopedSlots: { customRender: 'clampcontent' },
  },
  {
    title: '处罚结果',
    width: 200,
    dataIndex: 'title',
    placeholder: '未公示',
    scopedSlots: { customRender: 'clampcontent' },
  },
  {
    title: '处罚金额(元)',
    width: 100,
    dataIndex: 'amount',
    scopedSlots: { customRender: 'money' },
  },
  {
    title: '处罚单位',
    width: 150,
    dataIndex: 'court',
  },
  {
    title: '处罚日期',
    width: 100,
    dateFormat: true,
    dataIndex: 'punishdate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '原文',
    width: 68,
    scopedSlots: { customRender: 'originalSource' },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'urlAction' },
  },
];

// 欠税公告
const TaxArrearsNoticeColumns = [
  {
    title: '欠税税种',
    dataIndex: 'Title',
  },
  {
    title: '欠税余额(元)',
    dataIndex: 'Amount',
    scopedSlots: { customRender: 'money' },
    sorter: true,
    key: 'amount',
  },
  {
    title: '当前新发生的欠税金额(元)',
    dataIndex: 'NewAmount',
    scopedSlots: { customRender: 'money' },
    sorter: true,
    key: 'amount2',
  },
  { title: '发布单位', dataIndex: 'IssuedBy' },
  {
    title: '发布日期',
    width: 103,
    dataIndex: 'PublishDate',
    scopedSlots: { customRender: 'date' },
    sorter: true,
    key: 'liandate',
  },
  { title: '内容', width: 70, scopedSlots: { customRender: 'action' } },
];

// 产品召回
const ProductQualityProblem1Columns = [
  {
    title: '召回产品',
    dataIndex: 'Title',
    scopedSlots: { customRender: 'html' },
  },
  {
    title: '召回企业',
    key: 'NameAndKeyNo',
    scopedSlots: { customRender: 'NameAndKeyNo' },
  },
  {
    title: '发布日期',
    width: 103,
    dataIndex: 'PublishDate',
    key: 'PublishDate',
    sorter: true,
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'urlAction' },
  },
];

// 产品抽查不合格
const ProductQualityProblem2Columns = [
  {
    title: '产品名称',
    dataIndex: 'CaseReason',
  },
  {
    title: '产品类别',
    dataIndex: 'CaseReasonType',
  },
  {
    title: '规格型号',
    dataIndex: 'OrgNo',
  },
  {
    title: '生产单位',
    scopedSlots: { customRender: 'name' },
  },
  {
    title: '抽查/公告时间',
    dataIndex: 'PublishDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '主要不合格项目',
    dataIndex: 'ActionRemark',
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: {
      customRender: 'productCheckedDetail',
    },
  },
];

// 假冒伪劣产品
const ProductQualityProblem3Columns = [
  {
    title: '决定书文号',
    scopedSlots: {
      customRender: 'PenaltiesNo',
    },
  },
  {
    title: '处罚事由/违法行为类型',
    dataIndex: 'CaseReason',
    scopedSlots: { customRender: 'clampcontent' },
  },
  {
    title: '处罚结果/内容',
    dataIndex: 'Title',
    placeholder: '未公示',
    scopedSlots: { customRender: 'clampcontent' },
  },
  {
    title: '处罚金额(元)',
    dataIndex: 'Amount',
    scopedSlots: { customRender: 'money' },
  },
  {
    title: '处罚单位',
    dataIndex: 'Court',
  },
  {
    title: '处罚日期',
    dateFormat: true,
    width: 103,
    dataIndex: 'PunishDate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '原文',
    width: 68,
    scopedSlots: { customRender: 'originalSource' },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'urlAction' },
  },
];

// 虚假宣传
const ProductQualityProblem4Columns = [
  {
    title: '决定书文号',
    scopedSlots: {
      customRender: 'PenaltiesNo',
    },
  },
  {
    title: '处罚事由',
    dataIndex: 'CaseReason',
    scopedSlots: { customRender: 'clampcontent' },
  },
  {
    title: '处罚结果',
    dataIndex: 'Title',
    placeholder: '未公示',
    scopedSlots: { customRender: 'clampcontent' },
  },
  {
    title: '处罚金额(元)',
    dataIndex: 'Amount',
    scopedSlots: { customRender: 'money' },
  },
  {
    title: '处罚单位',
    dataIndex: 'Court',
  },
  {
    title: '处罚日期',
    dateFormat: true,
    width: 103,
    dataIndex: 'PunishDate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '原文',
    width: 68,
    scopedSlots: { customRender: 'originalSource' },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'urlAction' },
  },
];

// 其他质量问题
const ProductQualityProblem5Columns = [
  {
    title: '任务编号',
    scopedSlots: { customRender: 'taskNo' },
  },
  {
    title: '任务名称',
    dataIndex: 'Title',
  },
  {
    title: '抽查机关',
    dataIndex: 'Court',
  },
  {
    title: '完成日期',
    width: 103,
    dataIndex: 'PublishDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'drcDetail' },
  },
];

// 未准入境
const ProductQualityProblem6Columns = [
  { title: '产品名称', dataIndex: 'Title' },
  { title: '产品类型', dataIndex: 'CaseReasonType' },
  // { title: '生产企业信息/品牌', dataIndex: 'Applicant' },
  {
    title: '数量/重量',
    dataIndex: 'AmountDesc',
    scopedSlots: { customRender: 'thousandsNumber' },
  },
  { title: '原因', dataIndex: 'ActionRemark' },
  {
    title: '报送时间',
    dataIndex: 'LianDate',
    customRender: (text) => {
      if (!text || text < 0) return '-';

      return moment(text * 1000).format('YYYY年MM月');
    },
  },
  {
    title: '发布日期',
    width: 103,
    dataIndex: 'PublishDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '原文',
    width: 68,
    scopedSlots: { customRender: 'originalSource' },
  },
  { title: '内容', scopedSlots: { customRender: 'action' }, width: 70 },
];

// 药品抽查
const ProductQualityProblem7Columns = [
  { title: '药品品名', dataIndex: 'Specs' },
  {
    title: '检查实施机关',
    dataIndex: 'Court',
  },
  {
    title: '类型',
    dataIndex: 'CaseReasonType',
  },
  {
    title: '检测结果',
    dataIndex: 'ActionRemark',
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: {
      customRender: 'medicineDetail',
    },
  },
];

// 抽查检查-不合格
const SpotCheck = [
  {
    title: '检查实施机关',
    dataIndex: 'court',
  },
  {
    title: '类型',
    width: 110,
    dataIndex: 'casereasontype',
  },
  {
    title: '日期',
    width: 110,
    dataIndex: 'publishdate',
    key: 'publishdate',
    sorter: true,
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '结果',
    scopedSlots: { customRender: 'SpotCheck' },
  },
];

// 假冒化妆品
const ProductQualityProblem8Columns = [
  {
    title: '产品名称',
    dataIndex: 'CaseNo',
  },
  {
    title: '规格',
    dataIndex: 'StockInfo',
  },
  {
    title: '生产商',
    scopedSlots: { customRender: 'applicant' },
  },
  {
    title: '授权商',
    scopedSlots: { customRender: 'pledgorInfo' },
  },
  {
    title: '运营单位',
    dataIndex: 'CaseReason',
  },
  {
    title: '公告时间',
    dataIndex: 'PublishDate',
    scopedSlots: { customRender: 'date' },
  },
];

// 食品安全不合格
const ProductQualityProblem9Columns = [
  {
    title: '食品名称',
    dataIndex: 'Title',
  },
  {
    title: '抽检次数',
    dataIndex: 'Amount2',
    customRender: (text) => {
      return text > 0 ? `第${text}次抽检` : '-';
    },
  },
  {
    title: '被抽检企业',
    scopedSlots: { customRender: 'NameAndKeyNo' },
  },
  {
    title: '标称生产企业',
    scopedSlots: { customRender: 'applicant' },
  },
  {
    title: '标称生产企业地址',
    dataIndex: 'Address',
  },
  {
    title: '商标',
    dataIndex: 'ActionRemark',
  },
  {
    title: '规格型号',
    dataIndex: 'Specs',
  },
  {
    title: '生产日期/批号',
    width: 103,
    customRender: (item) => {
      return dateFormat(item.lianDate || item.LianDate);
    },
  },
  {
    title: '抽检结果',
    scopedSlots: { customRender: 'CheckResult' },
  },
];

// 被列入严重违法失信企业名录
const CompanyCreditColumns = [
  {
    title: '列入对象',
    dataIndex: 'SubjectInfo',
    scopedSlots: {
      customRender: 'EntityLinksAndJob',
    },
  },
  {
    title: '列入日期',
    width: 103,
    dateFormat: true,
    dataIndex: 'AddDate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '作出决定机关(列入)',
    dataIndex: 'AddOffice',
  },
  {
    title: '列入严重违法失信企业名单原因',
    dataIndex: 'AddReason',
  },
];

// 被列入严重违法失信企业名录(历史)
const CompanyCreditColumnsHistory = [
  {
    title: '移出日期',
    width: 103,
    dateFormat: true,
    dataIndex: 'RemoveDate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '作出决定机关(移出)',
    dataIndex: 'RemoveOffice',
  },
  {
    title: '移出严重违法失信企业名单原因',
    dataIndex: 'RemoveReason',
  },
  {
    title: '列入日期',
    width: 103,
    dateFormat: true,
    dataIndex: 'AddDate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '列入对象',
    dataIndex: 'SubjectInfo',
    scopedSlots: {
      customRender: 'EntityLinksAndJob',
    },
  },
  {
    title: '作出决定机关(列入)',
    dataIndex: 'AddOffice',
  },
  {
    title: '列入严重违法失信企业名单原因',
    dataIndex: 'AddReason',
  },
];

// 被列入经营异常名录（历史）
const OperationAbnormalColumns = [
  {
    title: '移出日期',
    width: 103,
    dataIndex: 'LianDate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '作出决定机关(移出)',
    dataIndex: 'ActionRemark',
  },
  {
    title: '移出经营异常名录原因',
    dataIndex: 'RemoveReason',
  },
  {
    title: '列入日期',
    width: 103,
    dataIndex: 'PublishDate',
    scopedSlots: {
      customRender: 'date',
    },
    sorter: true,
    key: 'currencedate',
  },
  {
    title: '列入对象',
    dataIndex: 'SubjectInfo',
    scopedSlots: {
      customRender: 'EntityLinksAndJob',
    },
  },
  {
    title: '作出决定机关(列入)',
    dataIndex: 'Court',
  },
  {
    title: '列入经营异常名录原因',
    dataIndex: 'CaseReason',
  },
];

// ------------- 行政监管风险 -------------

// ------------- 经营稳定性风险 -------------

// 动产抵押
const ChattelMortgageColumns = [
  {
    title: '登记编号',
    scopedSlots: { customRender: 'RegisterNo' },
  },
  {
    title: '抵押人',
    dataIndex: 'RelatedCompanyInfo',
    scopedSlots: {
      customRender: 'qentityObj',
    },
  },
  {
    title: '抵押权人',
    scopedSlots: {
      customRender: 'ChattelMortgagebdyr',
    },
  },
  {
    title: '所有权或使用权归属',
    scopedSlots: { customRender: 'MPledgeDetail' },
  },
  {
    title: '债务人履行债务的期限',
    customRender: (item) => {
      return item?.MPledgeDetail?.GuaranteedCredRight?.FulfillObligation || '-';
    },
  },
  {
    title: '被担保主债权数额',
    dataIndex: 'DebtSecuredAmount',
    scopedSlots: { customRender: 'thousandsNumber' },
  },
  {
    title: '状态',
    dataIndex: 'Status',
    scopedSlots: {
      customRender: 'status',
    },
  },
  {
    title: '登记日期',
    dateFormat: true,
    width: 103,
    scopedSlots: {
      customRender: 'date',
    },
    dataIndex: 'RegisterDate',
    key: 'RegisterDate',
    sorter: true,
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'action' },
  },
];

// 股权出质
const EquityPledgeColumns = [
  {
    title: '登记编号',
    scopedSlots: {
      customRender: 'RegistNo',
    },
  },
  {
    title: '出质人',
    scopedSlots: {
      customRender: 'pledgorInfo',
    },
  },
  {
    title: '出质股权标的企业',
    scopedSlots: {
      customRender: 'relatedCompanyInfo',
    },
  },
  {
    title: '质权人',
    scopedSlots: {
      customRender: 'pledgeeInfo',
    },
  },
  {
    title: '出质股权数额',
    dataIndex: 'PledgedAmount',
    scopedSlots: { customRender: 'thousandsNumber' },
  },
  {
    title: '登记日期',
    width: 103,
    dataIndex: 'RegDate',
    scopedSlots: {
      customRender: 'date',
    },
    sorter: true,
    key: 'liandate',
  },
  {
    title: '状态',
    statusTd: true,
    dataIndex: 'Status',
    scopedSlots: {
      customRender: 'status',
    },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: {
      customRender: 'action',
    },
  },
];

// 土地抵押
const LandMortgageColumns = [
  {
    title: '土地坐落',
    dataIndex: 'Address',
  },
  {
    title: '抵押人',
    scopedSlots: {
      customRender: 'LandMortgagedyr',
    },
  },
  {
    title: '抵押权人',
    scopedSlots: {
      customRender: 'LandMortgagebdyr',
    },
  },
  {
    title: '抵押起止日期',
    scopedSlots: { customRender: 'startEndDate' },
  },
  {
    title: '抵押面积(公顷)',
    dataIndex: 'MortgageAcreage',
  },
  {
    title: '抵押金额(万元)',
    dataIndex: 'MortgagePrice',
    scopedSlots: { customRender: 'thousandsNumber' },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'action' },
  },
];

// 司法拍卖信息
const JudicialAuction1Columns = [
  {
    title: '标题',
    scopedSlots: { customRender: 'biaoti' },
  },
  {
    title: '案号',
    scopedSlots: { customRender: 'CaseNo' },
  },
  {
    title: '起拍价(元)',
    dataIndex: 'yiwu',
    scopedSlots: { customRender: 'money' },
  },
  {
    title: '评估价(元)',
    dataIndex: 'EvaluationPrice',
    scopedSlots: { customRender: 'money' },
  },
  {
    title: '拍卖时间',
    width: 300,
    dataIndex: 'actionremark',
  },
  {
    title: '处置单位',
    dataIndex: 'executegov',
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'urlAction' },
  },
];

// 担保明细
const GuaranteeInfoColumns = [
  {
    title: '被担保方',
    scopedSlots: {
      customRender: 'VoucheeInfo',
    },
  },
  {
    title: '担保方',
    dataIndex: 'Guarantee',
    scopedSlots: {
      customRender: 'qentityArr',
    },
  },
  {
    title: '担保方式',
    dataIndex: 'GuaranteeType',
  },
  {
    title: '担保金额(万元)',
    dataIndex: 'GuaranteeMoney',
    scopedSlots: { customRender: 'money' },
  },
  {
    title: '公告日期',
    dateFormat: true,
    width: 103,
    dataIndex: 'PublicDate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'allAction' },
  },
];

// 担保风险
const GuaranteeRiskColumns = [
  {
    title: '保证类型',
    width: 100,
    dataIndex: 'GuaranteeType',
  },
  {
    title: '被担保方',
    dataIndex: 'Vouchee',
    scopedSlots: {
      customRender: 'qentityArr',
    },
  },
  {
    title: '担保方',
    dataIndex: 'Guarantee',
    scopedSlots: {
      customRender: 'qentityArr',
    },
  },
  {
    title: '债权人',
    dataIndex: 'Creditor',
    scopedSlots: {
      customRender: 'qentityArr',
    },
  },
  {
    title: '被保证债权本金(万元)',
    width: 150,
    customRender: (item) => {
      return item.GuaranteeMoney ? numberToHuman(item.GuaranteeMoney / 10000, { precision: 2 }) : '-';
    },
  },
  {
    title: '币种',
    width: 70,
    dataIndex: 'GuaranteeCurrency',
  },
  {
    title: '裁判日期',
    width: 103,
    dateFormat: true,
    sorter: true,
    dataIndex: 'Judgedate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '发布日期',
    width: 103,
    dateFormat: true,
    sorter: true,
    key: 'pubdate',
    dataIndex: 'PublicDate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'urlAction' },
  },
];

// 经营范围变更
const MainInfoUpdateScopeColumns = [
  {
    title: '变更日期',
    width: 103,
    dataIndex: 'ChangeDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '变更前',
    width: 400,
    scopedSlots: { customRender: 'beforeScope' },
  },
  {
    title: '变更后',
    width: 400,
    scopedSlots: { customRender: 'afterScope' },
  },
];

// 注册地址变更
const MainInfoUpdateAddressColumns = [
  {
    title: '变更日期',
    width: 103,
    dataIndex: 'ChangeDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '变更前',
    width: 400,
    scopedSlots: { customRender: 'beforeAddress' },
  },
  {
    title: '变更后',
    width: 400,
    scopedSlots: { customRender: 'afterAddress' },
  },
];

// 近期变更注册资本
const MainInfoUpdateRegisteredCapitalColumns = [
  {
    title: '变更日期',
    width: 103,
    dataIndex: 'changeDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '变更前',
    width: 400,
    dataIndex: 'beforeContent',
    scopedSlots: { customRender: 'thousandsNumber' },
  },
  {
    title: '变更后',
    width: 400,
    dataIndex: 'afterContent',
    scopedSlots: { customRender: 'thousandsNumber' },
  },
];

// 企业名称变更
const MainInfoUpdateNameColumns = [
  {
    title: '变更日期',
    width: 103,
    dataIndex: 'ChangeDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '变更前',
    width: 400,
    customCell: (item) => {
      const { beforeContent } = getHighLightDifferent(item.CompanyName, item?.after?.CompanyName);
      return {
        domProps: {
          innerHTML: beforeContent || '-',
        },
      };
    },
  },
  {
    title: '变更后',
    width: 400,
    customCell: (item) => {
      const { afterContent } = getHighLightDifferent(item.CompanyName, item?.after?.CompanyName);
      return {
        domProps: {
          innerHTML: afterContent || '-',
        },
      };
    },
  },
];

// 法定代表人变更
const MainInfoUpdateLegalPersonColumns = [
  {
    title: '变更日期',
    width: 103,
    dataIndex: 'ChangeDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '变更前',
    width: 400,
    scopedSlots: { customRender: 'LegalPerson' },
  },
  {
    title: '变更后',
    width: 400,
    scopedSlots: { customRender: 'afterLegalPerson' },
  },
];

// 近期变更大股东
const MainInfoUpdateHolderColumns = [
  {
    title: '变更日期',
    width: 103,
    dataIndex: 'ChangeDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '变更前',
    dataIndex: 'BeforeContent',
    width: 400,
    scopedSlots: { customRender: 'parseContent' },
  },
  {
    title: '变更后',
    width: 400,
    dataIndex: 'AfterContent',
    scopedSlots: { customRender: 'parseContent' },
  },
];

// 董监高变更
const MainInfoUpdateManagerColumns = [
  {
    title: '变更日期',
    width: 103,
    dataIndex: 'ChangeDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '变更前',
    width: 400,
    scopedSlots: { customRender: 'beforeEmployees' },
  },
  {
    title: '变更后',
    width: 400,
    scopedSlots: { customRender: 'Employees' },
  },
];

// 实际控制人变更
const MainInfoUpdatePersonColumns = [
  {
    title: '变更日期',
    width: 103,
    dataIndex: 'ChangeDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '变更前',
    dataIndex: 'BeforeContent',
    width: 400,
    scopedSlots: { customRender: 'parseContent' },
  },
  {
    title: '变更后',
    dataIndex: 'AfterContent',
    width: 400,
    scopedSlots: { customRender: 'parseContent' },
  },
];

// 债券违约
const BondDefaultsColumns = [
  { title: '债券简称', dataIndex: 'BondShortName' },
  { title: '债券类型', dataIndex: 'BondTypeName' },
  { title: '违约状态', dataIndex: 'DefaultStatusDesc' },
  {
    title: '首次违约日期',
    width: 120,
    dataIndex: 'FirstDefaultDate',
    scopedSlots: { customRender: 'date' },
    sorter: true,
    key: 'firstdefaultdate',
  },
  {
    title: '累计违约本金(亿元)',
    dataIndex: 'AccuOverdueCapital',
    scopedSlots: { customRender: 'money' },
    sorter: true,
    key: 'overduecapitalval',
  },
  {
    title: '累计违约利息(亿元)',
    dataIndex: 'AccuOverdueInterest',
    scopedSlots: { customRender: 'money' },
  },
  {
    title: '到期日期',
    dataIndex: 'MaturityDate',
    scopedSlots: { customRender: 'date' },
    sorter: true,
    key: 'maturitydate',
  },
  { title: '违约历程', width: 80, scopedSlots: { customRender: 'action' } },
];

// ------------- 经营稳定性风险 -------------

// ------------- 负面舆情 -------------
// ------------- 负面舆情 -------------

// ------------- 黑名单排查 -------------
// 外部黑名单
const HitOuterBlackListColumns = [
  {
    title: '黑名单企业名称',
    width: 220,
    scopedSlots: { customRender: 'blacklistCompanyName' },
  },
  {
    title: '命中黑名单类型',
    width: 180,
    dataIndex: 'CaseReasonType',
  },
  {
    title: '风险等级',
    dataIndex: 'level',
    width: 80,
    scopedSlots: { customRender: 'riskLevel' },
  },
  {
    title: '列入原因',
    dataIndex: 'CaseReason',
    width: 360,
  },
  {
    title: '列入机关',
    dataIndex: 'Court',
  },
  {
    title: '列入日期',
    width: 103,
    dataIndex: 'Publishdate',
    scopedSlots: { customRender: 'date' },
    sorter: true,
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'action' },
  },
];

// 内部黑名单
export const HitInnerBlackListColumns = [
  {
    title: '黑名单企业名称',
    scopedSlots: { customRender: 'companyName' },
  },
  {
    title: '列入原因',
    dataIndex: 'reason',
    width: 400,
  },
  {
    title: '列入日期',
    width: 103,
    dataIndex: 'joinDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '黑名单有效期',
    customRender: blacklistDurationFormatter,
  },
  {
    title: '截止日期',
    width: 103,
    dataIndex: 'expiredDate',
    scopedSlots: { customRender: 'date' },
  },
];

export const EmploymentRelationshipColumns = [
  {
    title: '关联人员',
    scopedSlots: { customRender: 'personName' },
  },
  {
    title: '关联黑名单企业名称',
    width: 260,
    scopedSlots: { customRender: 'relatedCompanyNameWithTag' },
  },
  {
    title: '关联链',
    scopedSlots: { customRender: 'path' },
  },
  {
    title: '图谱',
    width: 70,
    scopedSlots: { customRender: 'atlasAction' },
  },
];

export const ShareholderColumns = [
  {
    title: '关联黑名单企业名称',
    width: 260,
    scopedSlots: { customRender: 'relatedCompanyNameWithTag' },
  },
  {
    title: '持股比例',
    width: 100,
    customRender: renderStockPercent,
  },
  {
    title: '列入原因',
    dataIndex: 'reason',
    width: '25%',
  },
  {
    title: '列入日期',
    width: 103,
    dataIndex: 'joinDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '黑名单有效期',
    width: 100,
    customRender: blacklistDurationFormatter,
  },
  {
    title: '图谱',
    width: 70,
    scopedSlots: { customRender: 'atlasAction' },
  },
];

export const ForeignInvestmentColumns = [
  {
    title: '关联黑名单企业名称',
    width: 260,
    scopedSlots: { customRender: 'relatedCompanyNameWithTag' },
  },
  {
    title: '持股比例',
    width: 100,
    customRender: renderStockPercent,
  },
  {
    title: '列入原因',
    dataIndex: 'reason',
    width: '25%',
  },
  {
    title: '列入日期',
    width: 103,
    dataIndex: 'joinDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '黑名单有效期',
    width: 100,
    customRender: blacklistDurationFormatter,
  },
  {
    title: '图谱',
    width: 70,
    scopedSlots: { customRender: 'atlasAction' },
  },
];

// ------------- 黑名单排查 -------------

// 潜在利益冲突
export const ConflictInterestColumns = [
  {
    title: '姓名',
    scopedSlots: { customRender: 'name' },
  },
  {
    title: '人员类型/职务',
    dataIndex: 'job',
  },
  {
    title: '“潜在利冲”人员',
    scopedSlots: { customRender: 'PersonNo' },
  },
  {
    title: '“潜在利冲”人员分组',
    dataIndex: 'group',
  },
  {
    title: '操作',
    scopedSlots: { customRender: 'ActionCheck' },
  },
];

// 潜在利益冲突 相同联系方式
const ConflictInterestSamePhoneColumns = [
  {
    title: '联系人',
    customRender: (scope) => {
      const str = String(scope?.contacts || '')
        .split(',')
        .filter((item) => item)
        .join('，');
      return str;
    },
  },
  {
    title: '联系方式',
    customRender: (scope) => {
      const str = `${scope.phones || ''},${scope.emails || ''}`
        .split(',')
        .filter((item) => item)
        .join('，');
      return str;
    },
  },
  {
    title: '“潜在利冲”人员',
    scopedSlots: { customRender: 'PersonNo' },
  },
  {
    title: '“潜在利冲”人员姓名',
    dataIndex: 'name',
  },
  {
    title: '“潜在利冲”人员分组',
    dataIndex: 'group',
  },
];

// 潜在利益冲突 对外投资
export const StaffForeignInvestmentColumns = [
  {
    title: '姓名',
    scopedSlots: { customRender: 'name' },
  },
  {
    title: '人员类型/职务',
    dataIndex: 'job',
  },
  {
    title: '持股比例',
    customRender: renderStockPercent,
  },
  {
    title: '“潜在利冲”人员',
    scopedSlots: { customRender: 'PersonNo' },
  },
  {
    title: '“潜在利冲”人员分组',
    dataIndex: 'group',
  },
  {
    title: '操作',
    scopedSlots: { customRender: 'ActionCheck' },
  },
];

// 潜在利益冲突
export const StaffWorkingOutsideForeignInvestmentColumns = [
  {
    title: '姓名',
    scopedSlots: { customRender: 'name' },
  },
  {
    title: '人员类型/职务',
    dataIndex: 'job',
  },
  {
    title: '“潜在利冲”人员',
    scopedSlots: { customRender: 'PersonNo' },
  },
  {
    title: '“潜在利冲”人员分组',
    dataIndex: 'group',
  },
];

// 疑似潜在利益冲突
export const SuspectedInterestConflictColumns = [
  {
    title: '疑似关系',
    scopedSlots: { customRender: 'interestConflictPerson' },
  },
  {
    title: '人员类型/职务',
    dataIndex: 'job',
  },
  {
    title: '“疑似潜在利冲”人员',
    scopedSlots: { customRender: 'PersonNo' },
  },
  {
    title: '“疑似潜在利冲”人员分组',
    dataIndex: 'group',
  },
  {
    title: '操作',
    scopedSlots: { customRender: 'ActionCheck' },
  },
];

// 注销备案
const CancellationOfFilingColumns = [
  {
    title: '注销原因',
    width: 80,
    customRender: (item) => {
      return item.Detail?.LiqBAInfo?.CancelReason;
    },
  },
  {
    title: '公告内容',
    customRender: (item) => {
      return item.Detail?.CreditorNoticeInfo.NoticeContent;
    },
  },
  {
    title: '公告期',
    width: 220,
    dataIndex: 'CreditorNoticeDate',
  },
  {
    title: '债权申报联系人',
    customRender: (item) => {
      return item.Detail?.CreditorNoticeInfo.ClaimsDeclarationMember;
    },
  },
  {
    title: '债权申报联系电话',
    customRender: (item) => {
      return item.Detail?.CreditorNoticeInfo.ClaimsDeclarationTelNo;
    },
  },
  {
    title: '债权申报地址',
    customRender: (item) => {
      return item.Detail?.CreditorNoticeInfo.ClaimsDeclarationAddress;
    },
  },
  {
    title: '登记机关',
    customRender: (item) => {
      return item.Detail?.LiqBAInfo?.BelongOrg;
    },
  },
  {
    title: '详情',
    scopedSlots: { customRender: 'action' },
  },
];

// 合作方交叉重叠
export const ShareholdingRelationshipColumns = [
  {
    title: '关联企业名称',
    width: 260,
    scopedSlots: { customRender: 'relatedCompanyNameWithTag' },
  },
  {
    title: '持股比例',
    customRender: renderStockPercent,
  },
  {
    title: '关联链',
    scopedSlots: { customRender: 'path' },
  },
  {
    title: '图谱',
    width: 70,

    scopedSlots: { customRender: 'atlasAction' },
  },
];

export const ServeRelationshipColumns = [
  {
    title: '关联人员',
    scopedSlots: { customRender: 'personName' },
  },
  {
    title: '关联企业名称',
    width: 260,
    scopedSlots: { customRender: 'relatedCompanyName' },
  },
  {
    title: '关联链',
    scopedSlots: { customRender: 'path' },
  },
  {
    title: '图谱',
    width: 70,

    scopedSlots: { customRender: 'atlasAction' },
  },
];

export const PartnershipColumns = [
  {
    title: '关联企业名称',
    width: 260,
    scopedSlots: { customRender: 'relatedCompanyNameWithTag' },
  },
  {
    title: '持股比例',
    customRender: renderStockPercent,
  },
  {
    title: '关联链',
    scopedSlots: { customRender: 'path' },
  },
  {
    title: '图谱',
    width: 70,

    scopedSlots: {
      customRender: 'atlasAction',
    },
  },
];

export const BlacklistPartnerInvestigationColumns = [
  {
    title: '关联企业名称',
    width: 280,
    scopedSlots: { customRender: 'relatedCompanyNameWithTag' },
  },
  {
    title: '列入原因',
    width: 150,
    _showReason: true,
    customCell: () => {
      return {
        attrs: {
          colSpan: 4,
        },
      };
    },
    scopedSlots: { customRender: 'relateType' },
  },
  {
    title: '关联类型',
    width: 150,
    customRender: () => {
      return {
        attrs: {
          colSpan: 0,
        },
      };
    },
  },
  {
    title: '关联路径详情',
    customRender: () => {
      return {
        attrs: {
          colSpan: 0,
        },
      };
    },
  },
  {
    title: '操作',
    width: 100,
    customRender: () => {
      return {
        attrs: {
          colSpan: 0,
        },
      };
    },
  },
];

/// 与第三方列表企业存在投资任职关联
export const CustomerPartnerInvestigationColumns = [
  {
    title: '关联企业名称',
    width: 280,
    scopedSlots: { customRender: 'relatedCompanyNameWithTag' },
  },
  {
    title: '关联类型',
    width: 150,
    _showReason: false,
    customCell: () => {
      return {
        attrs: {
          colSpan: 3,
        },
      };
    },
    scopedSlots: { customRender: 'relateType' },
  },
  {
    title: '关联路径详情',
    customRender: () => {
      return {
        attrs: {
          colSpan: 0,
        },
      };
    },
  },
  {
    title: '操作',
    width: 100,
    customRender: () => {
      return {
        attrs: {
          colSpan: 0,
        },
      };
    },
  },
];

// 在外任职
const PunishedEmployeesWorkingOutsideColumns = [
  {
    title: '姓名',
    scopedSlots: { customRender: 'name' },
  },
  {
    title: '人员类型/职务',
    dataIndex: 'job',
  },
  {
    title: '人员编号',
    scopedSlots: { customRender: 'PersonNo' },
  },
  {
    title: '人员分组',
    dataIndex: 'group',
  },
  {
    title: '操作',
    scopedSlots: { customRender: 'ActionCheck' },
  },
];

// 对外投资
const PunishedEmployeesForeignInvestmentColumns = [
  {
    title: '姓名',
    scopedSlots: { customRender: 'name' },
  },
  {
    title: '人员类型/职务',
    dataIndex: 'job',
  },
  {
    title: '持股比例',
    customRender: renderStockPercent,
  },
  {
    title: '人员编号',
    scopedSlots: { customRender: 'PersonNo' },
  },
  {
    title: '人员分组',
    dataIndex: 'group',
  },
  {
    title: '操作',
    scopedSlots: { customRender: 'ActionCheck' },
  },
];

export const CompanyOrMainMembersCriminalOffence = [
  {
    title: '案件名称',
    dataIndex: 'CaseName',
    width: 260,
  },
  // {
  //   title: '案件身份',
  //   width: 120,
  //   scopedSlots: {
  //     customRender: 'CaseIdentity',
  //   },
  // },
  {
    title: '当事人',
    width: 360,
    scopedSlots: { customRender: 'RoleAmt' },
  },
  {
    title: '案号',
    scopedSlots: { customRender: 'AnNoList' },
  },
  {
    title: '案由',
    width: 100,
    dataIndex: 'CaseReason',
    scopedSlots: { customRender: 'caseReason' },
  },
  {
    title: '最新案件进程',
    scopedSlots: { customRender: 'LastCaseProgress' },
  },
  {
    title: '法院',
    scopedSlots: { customRender: 'CourtList' },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'urlAction' },
  },
];

// 买卖合同纠纷/重大纠纷
// 近3年涉贪污受贿裁判相关提及方/涉贪污受贿裁判相关提及方（3年以上及其他）
const Dispute = [
  {
    title: '文书标题',
    width: '15%',
    scopedSlots: { customRender: 'caseName' },
  },
  {
    title: '案号',
    // width: '15%',
    scopedSlots: { customRender: 'CaseNo' },
  },
  {
    title: '案由',
    width: 100,
    dataIndex: 'casereason',
    scopedSlots: { customRender: 'caseReason' },
  },
  {
    title: '当事人',
    width: '15%',
    scopedSlots: { customRender: 'Parties' },
  },
  {
    title: '案件金额(元) ',
    width: 120,
    sorter: true,
    dataIndex: 'amountinvolved',
    scopedSlots: { customRender: 'money' },
  },
  {
    title: '裁判结果',
    // width: '15%',
    dataIndex: 'judgeresult',
    scopedSlots: { customRender: 'clampcontent' },
  },
  {
    title: '裁判日期',
    dataIndex: 'judgedate',
    width: 100,
    scopedSlots: { customRender: 'date' },
    sorter: true,
    key: 'judgedate',
  },
  {
    title: '发布日期',
    width: 100,
    dataIndex: 'courtdate',
    scopedSlots: { customRender: 'date' },
    sorter: true,
    key: 'courtdate',
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'urlAction' },
  },
];

const SameControllRelation = [
  {
    title: '关联企业名称',
    width: 260,
    scopedSlots: { customRender: 'companyName' },
  },
  {
    title: '实际控制人',
    width: 260,
    scopedSlots: { customRender: 'name' },
  },
  {
    title: '控制链',
    scopedSlots: { customRender: 'detailPath' },
  },
  {
    title: '图谱',
    width: 70,

    scopedSlots: { customRender: 'controllerAtlasAction' },
  },
];

const BlacklistSameSuspectedActualController = [
  {
    title: '关联黑名单企业名称',
    width: 260,
    scopedSlots: { customRender: 'companyName' },
  },
  {
    title: '实际控制人',
    width: 260,
    scopedSlots: { customRender: 'name' },
  },
  {
    title: '控制链',
    scopedSlots: { customRender: 'detailPath' },
  },
  {
    title: '图谱',
    width: 70,

    scopedSlots: { customRender: 'controllerAtlasAction' },
  },
];

// 近期变更受益所有人
const MainInfoUpdateBeneficiaryColumns = [
  {
    title: '变更日期',
    width: 103,
    dataIndex: 'ChangeDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '变更前',
    width: 400,
    scopedSlots: { customRender: 'BeforeChange' },
  },
  {
    title: '变更后',
    width: 400,
    scopedSlots: { customRender: 'AfterChange' },
  },
];

// 票据违约
const BillDefaultsColumns = [
  {
    title: '承兑人',
    scopedSlots: { customRender: 'companyNamePascal' },
  },
  {
    title: '截止日期',
    width: 100,
    dataIndex: 'EndDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '披露日期',
    width: 100,
    dataIndex: 'PublishDate',
    scopedSlots: { customRender: 'date' },
    sorter: true,
    key: 'publishdate',
  },
  {
    title: '逾期余额(元)',
    dataIndex: 'OverdueBalance',
    scopedSlots: { customRender: 'money' },
    sorter: true,
    key: 'overduebalance',
  },
  {
    title: '累计逾期发生额(元)',
    dataIndex: 'AccuOverdueAmount',
    scopedSlots: { customRender: 'money' },
    sorter: true,
  },
  {
    title: '状态',
    width: 120,
    dataIndex: 'OverdueStatus',
    scopedSlots: { customRender: 'overdueStatus' },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: {
      customRender: 'action',
    },
  },
];

// 税务催缴公告
const TaxCallNoticeColumns = [
  {
    title: '标题',
    width: 520,
    dataIndex: 'title',
  },
  {
    title: '发布机构',
    width: 300,
    dataIndex: 'courtname',
  },
  {
    title: '发布日期',
    width: 100,
    dataIndex: 'publicdate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '公告类型',
    width: 100,
    dataIndex: 'noticetype',
  },
  {
    title: '内容',
    width: 70,
    dataIndex: 'id',
    scopedSlots: { customRender: 'govNoticeDetail' },
  },
];

// 税务催缴
const TaxCallNoticeV2Columns = [
  {
    title: '税种',
    width: 120,
    dataIndex: 'TaxCategory',
  },
  {
    title: '欠缴金额（元）',
    width: 120,
    dataIndex: 'AmountOwed',
    scopedSlots: { customRender: 'money' },
  },
  {
    title: '所属期起',
    width: 100,
    dataIndex: 'PeriodStartDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '所属期止',
    width: 100,
    dataIndex: 'PeriodEndDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '缴款期限',
    width: 100,
    dataIndex: 'PaymentDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '主管税务机关',
    width: 300,
    dataIndex: 'TaxKeyNoArray',
    scopedSlots: { customRender: 'taxOffice' },
  },
  {
    title: '发布日期',
    width: 100,
    dataIndex: 'PublishDate',
    key: 'PublishDate',
    sorter: true,
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'action' },
  },
];

// 税务催报
const TaxReminderColumns = [
  {
    title: '税种',
    width: 120,
    dataIndex: 'TaxCategory',
  },
  {
    title: '所属期起',
    width: 100,
    dataIndex: 'PeriodStartDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '所属期止',
    width: 100,
    dataIndex: 'PeriodEndDate',
    scopedSlots: { customRender: 'date' },
  },
  //  {
  //   title: '申报期限',
  //   width: 100,
  //   dataIndex: 'PaymentDate',
  //   scopedSlots: { customRender: 'date' },
  // },
  {
    title: '主管税务机关',
    width: 300,
    dataIndex: 'TaxKeyNoArray',
    scopedSlots: { customRender: 'taxOffice' },
  },
  {
    title: '发布日期',
    width: 100,
    dataIndex: 'PublishDate',
    sorter: true,
    key: 'PublishDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: { customRender: 'action' },
  },
];

// 终本案件
const EndExecutionCaseColumns = [
  {
    title: '案号',
    width: 300,
    scopedSlots: { customRender: 'endExecutionCaseDetail' },
  },
  {
    title: '被执行人',
    scopedSlots: { customRender: 'NameAndKeyNo' },
  },
  {
    title: '疑似申请执行人',
    dataIndex: 'SqrInfo',
    scopedSlots: { customRender: 'qentityArr' },
  },
  {
    title: '未履行金额(元)',
    width: 135,
    dataIndex: 'FailureAct',
    scopedSlots: { customRender: 'money' },
    sorter: true,
  },
  {
    title: '执行标的(元)',
    width: 135,
    dataIndex: 'ExecuteObject',
    scopedSlots: { customRender: 'money' },
    sorter: true,
  },
  {
    title: '执行法院',
    dataIndex: 'Court',
  },
  {
    title: '立案日期',
    dataIndex: 'LiAnDate',
    scopedSlots: { customRender: 'date' },
    sorter: true,
    key: 'liandate',
  },
  {
    title: '终本日期',
    dataIndex: 'EndDate',
    scopedSlots: { customRender: 'date' },
    sorter: true,
  },
];

// 公安通告
const SecurityNoticeColumns = [
  {
    title: '涉案企业',
    scopedSlots: { customRender: 'name' },
  },
  {
    title: '涉案案由',
    dataIndex: 'reason',
  },
  {
    title: '发布单位',
    dataIndex: 'publishUnit',
  },
  {
    title: '发布日期',
    width: 100,
    dataIndex: 'publishDate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '更新日期',
    width: 100,
    dataIndex: 'updateDate',
    customRender: (updateDate: number) => {
      if (!updateDate) {
        return '-';
      }
      return moment(updateDate * 1000).format('YYYY-MM-DD');
    },
  },
  {
    title: '内容',
    with: 100,
    scopedSlots: { customRender: 'urlAction' },
  },
];

// 监管处罚
const RegulateFinanceColoums = [
  {
    title: '处罚对象名称',
    width: 200,
    dataIndex: 'SubjectInfo',
    scopedSlots: { customRender: 'EntityLinksAndJob' },
  },
  {
    title: '决定文书号',
    dataIndex: 'caseno',
    width: 200,
  },
  {
    title: '违规事实',
    dataIndex: 'punishReason',
    scopedSlots: { customRender: 'clampcontent' },
  },
  {
    title: '处理结果',
    dataIndex: 'Title',
    scopedSlots: { customRender: 'clampcontent' },
  },
  {
    title: '处理单位',
    width: 160,
    dataIndex: 'Court',
  },
  {
    title: '处理日期',
    width: 100,
    dataIndex: 'PunishDate',
    sorter: true,
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '内容',
    width: 70,
    customRender: (item) => {
      return (
        <a target="_blank" href={`/embed/adminpenaltydetail?id=${item.RiskId}`}>
          详情
        </a>
      );
    },
  },
];

const CapitalReductionColumns = [
  {
    title: '公告企业',
    width: 200,
    scopedSlots: { customRender: 'name' },
  },
  {
    title: '公告内容',
    dataIndex: 'Content',
  },
  {
    title: '公告期限',
    width: 170,
    dataIndex: 'NoticePeriod',
  },
  {
    title: '公告日期',
    width: 100,
    dataIndex: 'DecideDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: {
      customRender: 'action',
    },
  },
];

// 劳动纠纷
const LaborContractDisputeColumns = [
  {
    title: '案件名称',
    scopedSlots: { customRender: 'caseName' },
  },
  {
    title: '案件身份',
    width: '11%',
    scopedSlots: {
      customRender: 'CaseIdentity',
    },
  },
  {
    title: '案由',
    width: '8%',
    dataIndex: 'CaseReason',
    scopedSlots: { customRender: 'caseReason' },
  },
  {
    title: '案号',
    width: '18.5%',
    scopedSlots: {
      customRender: 'AnNoList',
    },
  },
  {
    title: () => {
      return (
        <div>
          <span>案件金额(元)</span>
          <Tooltip
            overlayClassName="self-customed"
            title="该数据是基于裁判文书、终本案件、被执行人数据分析得出，仅供参考，并不代表企查查任何明示、暗示之观点或保证"
          >
            <QIcon type="icon-icon_zhushi" style="color: #d8d8d8;margin-left: 4px;"></QIcon>
          </Tooltip>
        </div>
      );
    },
    width: '11%',
    dataIndex: 'Amt',
    scopedSlots: { customRender: 'money' },
  },
  {
    title: () => {
      return (
        <div>
          <span>最新案件进程</span>
          <Tooltip
            overlayClassName="self-customed"
            title="最新案件进程是基于司法案件已公开的基础数据维度综合分析得出，仅供参考，并不代表企查查任何明示、暗示之观点或保证。"
          >
            <QIcon type="icon-icon_zhushi" style="color: #d8d8d8;margin-left: 4px;"></QIcon>
          </Tooltip>
        </div>
      );
    },
    width: '12%',
    customRender: (record) => {
      return <div style="white-space: pre;" domPropsInnerHTML={record.LatestDateTrialRound}></div>;
    },
  },
  // {
  //   title: '最新进程日期',
  //   width: '115',
  //   dataIndex: 'LastestDateNew',
  //   dateFormat: true
  // },
  {
    title: '法院',
    width: '12%',
    scopedSlots: { customRender: 'CourtList' },
  },
  {
    title: '内容',
    width: 60,
    scopedSlots: { customRender: 'urlAction' },
  },
];

// 不正当竞争纠纷
const UnfairCompetitionColumns = cloneDeep(LaborContractDisputeColumns);

// 资质筛查
const CertificationColumns = [
  {
    title: '资质类型',
    dataIndex: 'certificationType',
    customRender: (value, row) => {
      return {
        children: value,
        attrs: row.attrs,
      };
    },
  },
  {
    title: '资质名称',
    scopedSlots: { customRender: 'certificationName' },
  },
  {
    title: '状态',
    dataIndex: 'expirationDesc',
    customRender: (text) => {
      return <span class={getCertificationStyle(text)}>{text}</span>;
    },
  },
  {
    title: '有效期',
    customRender: (record) => {
      const std = record.startDate ? moment(record.startDate * 1000).format('YYYY-MM-DD') : '-';
      const edd = record.endDate ? moment(record.endDate * 1000).format('YYYY-MM-DD') : '-';
      if (std === '-' && edd === '-') {
        return '-';
      }
      return `${std} 至 ${edd}`;
    },
  },
];

const StockPledgeColumns = [
  {
    title: '质押人',
    width: 200,
    dataIndex: 'Holders',
    scopedSlots: { customRender: 'qentityArr' },
  },
  {
    title: () => {
      return (
        <div>
          质押人参股企业
          <QGlossaryInfo tooltip="即股权被质押企业" />
        </div>
      );
    },
    width: 200,
    dataIndex: 'Companys',
    scopedSlots: { customRender: 'qentityArr' },
  },
  {
    title: '质押权人',
    dataIndex: 'Pledgees',
    width: 200,
    scopedSlots: { customRender: 'qentityArr' },
  },
  {
    title: '质押股份总数(股)',
    width: 134,
    dataIndex: 'ShareFrozenNum',
    scopedSlots: { customRender: 'thousandsNumber' },
  },
  {
    title: '质押股份市值(元)',
    width: 132,
    dataIndex: 'SZ',
    scopedSlots: { customRender: 'thousandsNumber' },
  },
  {
    title: '状态',
    width: 105,
    dataIndex: 'Type',
  },
  {
    title: '公告日期',
    width: 108,
    key: 'publicdate',
    dataIndex: 'NoticeDate',
    scopedSlots: {
      customRender: 'date',
    },
    sorter: true,
  },
  {
    title: '内容',
    width: 60,
    scopedSlots: {
      customRender: 'action',
    },
  },
];

// 近期多起开庭公告 序号、案号、案由、当事人、法院	、开庭时间
const NoticeInTimePeriodColumns = [
  {
    title: '案号',
    scopedSlots: { customRender: 'ReferenceNo' },
  },
  {
    title: '案由',
    width: 200,
    dataIndex: 'caseReason',
    scopedSlots: { customRender: 'caseReason' },
  },
  {
    title: '当事人',
    width: 360,
    scopedSlots: { customRender: 'concernedParties' },
  },
  {
    title: '法院',
    dataIndex: 'court',
  },
  {
    title: '开庭时间',
    width: 100,
    dataIndex: 'courtDate',
    scopedSlots: { customRender: 'date' },
    key: 'courtDate',
    sorter: true,
  },
];

// 被列入非正常户
const BusinessAbnormal4Columns = [
  {
    title: '纳税人识别号',
    dataIndex: 'CaseNo',
    width: 200,
  },
  {
    title: '信用类型',
    dataIndex: 'IsValid',
    width: 200,
    customRender: (value) => {
      return value ? '税务非正常户' : '税务非正常户(失效)';
    },
  },
  {
    title: '列入机关',
    dataIndex: 'ExecuteGov',
  },
  {
    title: '列入日期',
    dataIndex: 'JoinDate',
    scopedSlots: {
      customRender: 'date',
    },
    width: 100,
  },
];

// 票据承兑风险
const PersistentBillOverdueColounms = [
  {
    title: '企业名称',
    scopedSlots: {
      customRender: 'companyName',
    },
  },
  {
    title: '统一社会信用代码',
    dataIndex: 'creditCode',
  },
  {
    title: '名单类型',
    dataIndex: 'listTag',
    customRender: (val) => {
      return billAcceptanceRiskStatusMap[val] || '-';
    },
  },
  {
    title: '开始时间',
    dataIndex: 'beginDate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '公告日期',
    dataIndex: 'publishDate',
    scopedSlots: {
      customRender: 'date',
    },
  },
];

// 知识产权出质
const IPRPledgeColoumns = [
  {
    title: '出质知产类型',
    dataIndex: 'TypeDesc',
  },
  {
    title: '名称',
    dataIndex: 'Name',
  },
  {
    title: '出质登记号',
    dataIndex: 'RegNo',
  },
  {
    title: '出质公告日',
    key: 'PublishDate',
    sorter: true,
    dataIndex: 'PublishDate',
    scopedSlots: { customRender: 'date' },
  },
  {
    title: '出质人名称',
    dataIndex: 'PledgorInfo',
    scopedSlots: { customRender: 'qentityArr' },
  },
  {
    title: '质权人名称',
    dataIndex: 'PledgeeInfo',
    scopedSlots: { customRender: 'qentityArr' },
  },
  {
    title: '出质期限',
    scopedSlots: { customRender: 'IPRPledgePeriod' },
  },
  {
    title: '详情',
    scopedSlots: { customRender: 'urlAction' },
  },
];

// 涉嫌冒名登记
const FakeRegisterColoumns = [
  {
    title: '冒名登记事项',
    dataIndex: 'fakeRegisterItem',
  },
  {
    title: '当事人姓名',
    entity: { keyNo: 'partyKeyNo', name: 'partyName' },
    scopedSlots: { customRender: 'entityLink' },
  },
  {
    title: '冒名登记时间',
    dataIndex: 'fakeRegisterTime',
  },
  {
    title: '公告期自',
    dataIndex: 'startDate',
  },
  {
    title: '公告期至',
    dataIndex: 'endDate',
  },
  {
    title: '处理结果',
    dataIndex: 'processResult',
  },
  {
    title: '作出决定时间',
    dataIndex: 'decisionDate',
  },
  {
    title: '作出决定机关',
    dataIndex: 'executeGov',
  },
];

// 假冒国企
const FakeSOESColumns = [
  {
    title: '被列入假冒国企企业名称',
    scopedSlots: {
      customRender: 'fakeSoeName',
    },
  },
  {
    title: '关联路径',
    width: '50%',
    scopedSlots: {
      customRender: 'fakePath',
    },
  },
];

const DebtOverdueColumns = [
  {
    title: '债务人',
    entity: { keyNo: 'debtorKeyNo', name: 'debtorName' },
    scopedSlots: { customRender: 'entityLink' },
  },
  {
    title: '债权人',
    entity: { keyNo: 'creditorKeyNo', name: 'creditorName' },
    scopedSlots: { customRender: 'entityLink' },
  },
  {
    title: '债务类型',
    dataIndex: 'debtType',
  },
  {
    title: '逾期金额(万元)',
    dataIndex: 'overdueSum',
    scopedSlots: { customRender: 'money' },
  },
  {
    title: '逾期本金(万元)',
    dataIndex: 'overdueCapital',
    scopedSlots: { customRender: 'money' },
  },
  {
    title: '逾期利息(万元)',
    dataIndex: 'overdueInterest',
    scopedSlots: { customRender: 'money' },
  },
  {
    title: '披露方',
    entity: { keyNo: 'publicPartyKeyNo', name: 'publicPartyName' },
    scopedSlots: { customRender: 'entityLink' },
  },
  {
    title: '逾期起始日',
    dataIndex: 'startDate',
    sorter: true,
    key: 'startDate',
    scopedSlots: { customRender: 'date' },
  },
];

// 动产查封
const ChattelSeizureColumns = [
  {
    title: '案号',
    customRender: (text, record) => {
      if (record.CaseSearchIds?.length > 0) {
        return (
          <a href={`/embed/courtCaseDetail?caseId=${record.CaseSearchIds[0]}&title=${record.CaseNo}`} target="_blank">
            {record.CaseNo || '-'}
          </a>
        );
      }
      return record.CaseNo || '-';
    },
  },
  {
    title: '被执行人',
    dataIndex: 'Companys',
    scopedSlots: { customRender: 'qentityArr' },
  },
  {
    title: '执行法院',
    dataIndex: 'ExecuteGov',
  },
  {
    title: '公示日期',
    dataIndex: 'PublicDate',
    scopedSlots: {
      customRender: 'date',
    },
  },
  {
    title: '查封起止日期',
    scopedSlots: { customRender: 'startEndDate' },
  },
  {
    title: '内容',
    width: 70,
    scopedSlots: {
      customRender: 'action',
    },
  },
];

const SeparationNoticeColumns = [
  {
    title: '作出决定日期',
    width: 120,
    dataIndex: 'DecideDate',
    scopedSlots: {
      customRender: 'date',
      dateProps: {
        format: 'YYYY-MM-DD',
        x1000: true,
      },
    },
  },
  {
    title: '公告日期',
    width: 120,
    dataIndex: 'NoticeDate',
    scopedSlots: {
      customRender: 'date',
      dateProps: {
        format: 'YYYY-MM-DD',
        x1000: true,
      },
    },
  },
  {
    title: '公告期限',
    width: 200,
    customRender: (record) => {
      const { StartDate, EndDate } = record;
      if (!StartDate && !EndDate) {
        return '-';
      }
      const start = dateFormat(StartDate);
      const end = dateFormat(EndDate);
      return `${start} 至 ${end}`;
    },
  },
  {
    title: '公告类型',
    width: 120,
    dataIndex: 'NoticeType',
    customRender: (type) => {
      return `${type === 7 ? '分立' : '合并'}公告`;
    },
  },
  {
    title: '公告内容',
    dataIndex: 'Content',
  },
  {
    title: '操作',
    width: 80,
    scopedSlots: {
      customRender: 'action',
    },
  },
];

export const riskColumns = {
  BusinessAbnormal2: BusinessAbnormal2Columns,
  BusinessAbnormal3: BusinessAbnormal3Columns,
  BusinessAbnormal5: BusinessAbnormal5Columns,
  FreezeEquity: FreezeEquityColumns,
  ChattelMortgage: ChattelMortgageColumns,
  LandMortgage: LandMortgageColumns,
  PersonCreditCurrent: PersonCreditCurrentColumns,
  PersonCreditHistory: PersonCreditColumns,
  EquityPledge: EquityPledgeColumns,
  JudicialAuction: JudicialAuction1Columns,
  JudicialAuction1: JudicialAuction1Columns,
  GuaranteeInfo: GuaranteeInfoColumns,
  GuaranteeRisk: GuaranteeRiskColumns,
  RestrictedConsumptionCurrent: RestrictedConsumptionColumns,
  RestrictedConsumptionHistory: RestrictedConsumptionColumns,
  RestrictedOutbound: RestrictedOutboundColumns,
  TaxationOffences: TaxationOffencesColumns,
  Bankruptcy: BankruptcyColumns,
  PersonExecution: PersonExecutionColumns,
  ProductQualityProblem1: ProductQualityProblem1Columns,
  ProductQualityProblem2: ProductQualityProblem2Columns,
  SpotCheck,
  ProductQualityProblem3: ProductQualityProblem3Columns,
  ProductQualityProblem4: ProductQualityProblem4Columns,
  ProductQualityProblem5: ProductQualityProblem5Columns,
  ProductQualityProblem6: ProductQualityProblem6Columns,
  ProductQualityProblem7: ProductQualityProblem7Columns,
  ProductQualityProblem8: ProductQualityProblem8Columns,
  ProductQualityProblem9: ProductQualityProblem9Columns,
  MainInfoUpdateScope: MainInfoUpdateScopeColumns,
  MainInfoUpdateAddress: MainInfoUpdateAddressColumns,
  MainInfoUpdateBeneficiary: MainInfoUpdateBeneficiaryColumns,
  MainInfoUpdateName: MainInfoUpdateNameColumns,
  MainInfoUpdateLegalPerson: MainInfoUpdateLegalPersonColumns,
  MainInfoUpdateHolder: MainInfoUpdateHolderColumns,
  MainInfoUpdateManager: MainInfoUpdateManagerColumns,
  MainInfoUpdatePerson: MainInfoUpdatePersonColumns,
  BondDefaults: BondDefaultsColumns,
  AdministrativePenalties: AdministrativePenaltiesColumns,
  TaxPenalties: AdministrativePenaltiesColumns,
  AdministrativePenalties2: AdministrativePenalties2Columns,
  AdministrativePenalties3: AdministrativePenalties2Columns,
  EnvironmentalPenalties: AdministrativePenaltiesColumns,
  CompanyCredit: CompanyCreditColumns,
  CompanyCreditHistory: CompanyCreditColumnsHistory,
  OperationAbnormal: OperationAbnormalColumns,
  TaxArrearsNotice: TaxArrearsNoticeColumns,
  MainMembersPersonCreditCurrent,
  MainMembersRestrictedConsumptionCurrent: RestrictedConsumptionColumns,
  MainMembersRestrictedOutbound: RestrictedOutboundColumns,
  SubsidiaryPersonCreditCurrent: SubsidiaryPersonCreditCurrentColumns,
  SubsidiaryRestrictedConsumptionCurrent: RestrictedConsumptionColumns,
  StaffWorkingOutside: ConflictInterestColumns,
  SamePhone: ConflictInterestSamePhoneColumns,
  PunishedEmployeesWorkingOutside: PunishedEmployeesWorkingOutsideColumns,
  PunishedEmployeesForeignInvestment: PunishedEmployeesForeignInvestmentColumns,
  StaffForeignInvestment: StaffForeignInvestmentColumns,
  HitInnerBlackList: HitInnerBlackListColumns,
  EmploymentRelationship: EmploymentRelationshipColumns,
  Shareholder: ShareholderColumns,
  ForeignInvestment: ForeignInvestmentColumns,
  CancellationOfFiling: CancellationOfFilingColumns,
  HitOuterBlackList: HitOuterBlackListColumns,
  InvestorsRelationship: PartnershipColumns,
  ShareholdingRelationship: ShareholdingRelationshipColumns,
  ServeRelationship: ServeRelationshipColumns,
  CompanyOrMainMembersCriminalOffence,
  CompanyOrMainMembersCriminalOffenceHistory: CompanyOrMainMembersCriminalOffence,
  SalesContractDispute: Dispute,
  MajorDispute: Dispute,
  CompanyOrMainMembersCriminalInvolve: Dispute,
  CompanyOrMainMembersCriminalInvolveHistory: Dispute,
  SameSuspectedActualController: SameControllRelation,
  BlacklistSameSuspectedActualController,
  SuspectedInterestConflict: SuspectedInterestConflictColumns,
  StaffWorkingOutsideForeignInvestment: StaffWorkingOutsideForeignInvestmentColumns,
  CustomerPartnerInvestigation: CustomerPartnerInvestigationColumns, // 与第三方列表企业存在投资任职关联
  CustomerSuspectedRelation: CustomerPartnerInvestigationColumns, // 与第三方列表企业存在交叉重叠疑似关联
  BlacklistPartnerInvestigation: BlacklistPartnerInvestigationColumns, // 与内部黑名单企业存在投资任职关联
  BlacklistSuspectedRelation: BlacklistPartnerInvestigationColumns, // 与内部黑名单企业存在疑似关联关系
  BillDefaults: BillDefaultsColumns, // 票据违约
  TaxCallNotice: TaxCallNoticeColumns, // 税务催缴公告
  TaxCallNoticeV2: TaxCallNoticeV2Columns, // 税务催缴
  TaxReminder: TaxReminderColumns, // 税务催报
  EndExecutionCase: EndExecutionCaseColumns, // 终本案件
  SecurityNotice: SecurityNoticeColumns, // 公安通告
  RegulateFinance: RegulateFinanceColoums, // 监管处罚
  CapitalReduction: CapitalReductionColumns, // 减资公告
  LaborContractDispute: LaborContractDisputeColumns, // 劳动纠纷
  Certification: CertificationColumns, // 资质筛查
  StockPledge: StockPledgeColumns, // 股权质押
  NoticeInTimePeriod: NoticeInTimePeriodColumns, // 近期多起开庭公告
  BusinessAbnormal4: BusinessAbnormal4Columns, // 被列入非正常户
  UnfairCompetition: UnfairCompetitionColumns, // 不正当竞争纠纷
  PersistentBillOverdue: PersistentBillOverdueColounms, // 票据持续逾期 -> 票据承兑风险
  IPRPledge: IPRPledgeColoumns, // 知识产权出质
  FakeRegister: FakeRegisterColoumns, // 涉嫌冒名登记
  MainInfoUpdateRegisteredCapital: MainInfoUpdateRegisteredCapitalColumns,
  FakeSOES: FakeSOESColumns,
  DebtOverdue: DebtOverdueColumns, // 债务逾期
  ChattelSeizure: ChattelSeizureColumns, // 动产查封
  SeparationNotice: SeparationNoticeColumns, // 合并公告
};
