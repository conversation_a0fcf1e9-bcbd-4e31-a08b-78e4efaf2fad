/**
 * Detail type alias map
 */
export const DETAIL_TYPES_MAP = {
  BusinessAbnormal2: 'jyzx',
  BusinessAbnormal5: 'adminpenaltydetail',
  FreezeEquity: 'assistance',
  ChattelMortgage: 'mPledge',
  LandMortgage: 'landmortgage',
  EquityPledge: 'pledge',
  JudicialAuction: 'sfpaimaiDetail',
  JudicialAuction1: 'sfpaimaiDetail',
  GuaranteeInfo: 'guarantor',
  GuaranteeRisk: 'wenshuDetail',
  PersonCreditCurrent: 'shixin',
  PersonCreditHistory: 'shixin',
  MainMembersPersonCreditCurrent: 'shixin',
  MainMembersRestrictedConsumptionCurrent: 'xiangaoDetail',
  SubsidiaryPersonCreditCurrent: 'shixin',
  SubsidiaryRestrictedConsumptionCurrent: 'xiangaoDetail',
  RestrictedConsumptionCurrent: 'xiangaoDetail',
  RestrictedConsumptionHistory: 'xiangaoDetail',
  TaxationOffences: 'taxIllegal',
  Bankruptcy: 'bankruptcy',
  PersonExecution: 'zhixing',
  ProductQualityProblem1: 'productrecall',
  ProductQualityProblem2: '',
  ProductQualityProblem3: 'adminpenaltydetail',
  ProductQualityProblem4: 'adminpenaltydetail',
  ProductQualityProblem5: 'drc',
  ProductQualityProblem6: 'notallowedentry',
  ProductQualityProblem7: '',
  ProductQualityProblem8: 'adminpenaltydetail',
  ProductQualityProblem9: 'foodsafety',
  BondDefaults: 'bond',
  AdministrativePenalties: 'adminpenaltydetail',
  EnvironmentalPenalties: 'adminpenaltydetail',
  TaxArrearsNotice: 'owenotice',
  BillDefaults: 'billDefaults',
  HitOuterBlackList: 'govProcurementIllegal',
  CapitalReduction: 'decreaseCapiNotice',
  TaxReminder: 'taxReminder',
  TaxCallNoticeV2: 'taxCallNotice',
  StockPledge: 'stockPledge',
  CancellationOfFiling: 'enliqDetail',
  ChattelSeizure: 'chattelSeizure',
  SeparationNotice: 'separationNotice',
};

export function getDetailByType(key, item) {
  let url;
  switch (key) {
    case 'MainMembersRestrictedConsumptionCurrent': // 主要人员限制高消费
    case 'SubsidiaryRestrictedConsumptionCurrent': // 子公司限制高消费
    case 'RestrictedConsumptionCurrent': // 限制高消费（当前有效）
    case 'RestrictedConsumptionHistory': // 历史限制高消费
      url = `/embed/sumptuary?id=${item.RiskId}&title=${item.Court}${item.CaseNo}`;
      break;
    case 'BusinessAbnormal5': // 疑似停业歇业停产或被吊销证照
    case 'ProductQualityProblem3': // 假冒伪劣产品
    case 'ProductQualityProblem4': // 虚假宣传
    case 'ProductQualityProblem8': // 假冒化妆品
    case 'BidCollusive': // 围串标关联（招标排查）
      url = `/embed/adminpenaltydetail?id=${item.id ?? item.Id}&title=${item.CaseNo}`;
      break;
    case 'EnvironmentalPenalties': // 环保处罚
    case 'AdministrativePenalties': // 行政处罚
    case 'TaxPenalties': // 税务处罚
    case 'AdministrativePenalties2': // 涉及商业贿赂、垄断行为
    case 'AdministrativePenalties3': // 政府采购活动违法行为行政处罚
    case 'BidAdministrativePenalties': // 涉围串标处罚
      url = `/embed/adminpenaltydetail?id=${item.riskid ?? item.RiskId}&title=${item.caseno}`;
      break;
    case 'JudicialAuction': // 司法拍卖
    case 'JudicialAuction1': // 司法拍卖(机器设备)
      url = `/embed/judicialSale?id=${item.id ?? item.Id}&title=${item.name}`;
      break;
    case 'ProductQualityProblem1': // 产品召回
      url = `/embed/recall-product?url=${encodeURI(item.OssId)}&title=${item.Title}`;
      break;
    case 'GuaranteeRisk': // 担保风险
      url = `/embed/judgementInfo?id=${item.id ?? item.Id}`;
      break;
    case 'SalesContractDispute': // 买卖合同纠纷
    case 'MajorDispute': // 重大纠纷
    case 'CompanyOrMainMembersCriminalInvolve': // 近3年涉贪污受贿裁判相关提及方
    case 'CompanyOrMainMembersCriminalInvolveHistory': // 涉贪污受贿裁判相关提及方（3年以上及其他）
      url = `/embed/judgementInfo?id=${String(item.id).slice(0, -1)}&title=${item.casename}`;
      break;
    case 'BidAdministrativeJudgement': // 涉诉围串标记录
      if (item.dataType === 'case') {
        url = `/embed/courtCaseDetail?caseId=${item.id}&title=${item.casename}`;
      } else {
        url = `/embed/judgementInfo?id=${String(item.id).slice(0, -1)}&title=${item.casename}`;
      }
      break;
    case 'CompanyOrMainMembersCriminalOffence': // 公司及主要人员涉刑事犯罪（3年内）
    case 'CompanyOrMainMembersCriminalOffenceHistory': // 公司及主要人员涉刑事犯罪（3年以上及其他）
    case 'LaborContractDispute': // 劳动纠纷
      url = `/embed/courtCaseDetail?caseId=${item.id ?? item.Id}&title=${item.CaseName}`;
      break;
    case 'SecurityNotice':
      // 公安通告
      url = `/embed/news-detail-page?newsId=${item.id}&keyNo=${item.keyNo}&title=${item.reason}`;
      break;
    // 历史失信人存在searchCaseId为空的情况
    case 'PersonCreditHistory':
      url = item.CaseSearchId
        ? (url = `/embed/courtCaseDetail?caseId=${item.CaseSearchId}&title=${item.Court ?? item.ExecuteGov ?? ''}${item.CaseNo}`)
        : '';
      break;
    case 'IPRPledge':
      // 商标还是专利
      url = `/embed/${item.Type === 2 ? 'tmDetail' : 'patentDetail'}?id=${item.Id}`;
      break;
    default:
      url = `/embed/courtCaseDetail?caseId=${item.CaseSearchId ?? item.Id ?? item.id}&title=${
        item.Court ?? item.ExecuteGov ?? ''
      }${item.CaseNo}`;
      break;
  }
  return url;
}
