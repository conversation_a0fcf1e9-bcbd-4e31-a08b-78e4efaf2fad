import { defineComponent, ref, onMounted, unref, computed } from 'vue';
import moment from 'moment';
import { Button } from 'ant-design-vue';

import QIcon from '@/components/global/q-icon';
import CompanyStatus from '@/components/global/q-company-status';
import { useUserStore } from '@/shared/composables/use-user-store';
import { TenderResultMap } from '@/shared/config/bidding-investigation-detail.config';

import styles from './history-block-single.module.less';

const HistoryRecordSingle = defineComponent({
  props: {
    value: {
      type: Object,
      default: () => ({}),
    },
    idKey: {
      type: String,
      default: 'tenderNo',
    },
  },
  name: 'HistoryRecordSingle',
  setup(props) {
    const innerRef = ref<HTMLElement | null>(null);
    const isClamp = ref(false);

    const isFailed = computed(() => props.value?.status === 2);
    const isSuccess = computed(() => props.value?.status === 1);
    onMounted(() => {
      const target = unref(innerRef);
      const innerWidth = target?.getBoundingClientRect?.()?.width || 0;
      const parentWidth = target?.parentElement?.getBoundingClientRect?.().width || 0;
      isClamp.value = innerWidth >= parentWidth;
    });

    return {
      isSuccess,
      isFailed,
      innerRef,
      isClamp,
    };
  },
  emits: ['click', 'retry'],
  render() {
    const status = TenderResultMap[this.value?.remarkResult === null ? this.value?.result : this.value?.remarkResult];
    const renderStatus = () => {
      const { profile } = useUserStore();
      const isCreator = this.value?.editor?.userId === profile.value?.id;
      if (this.value?.status === 0) {
        return <span class={styles.waiting}>正在排查中，请稍后…</span>;
      }
      if (this.isFailed && isCreator) {
        return (
          <div class="flex">
            <Button size="small" type="link" v-debounceclick={() => this.$emit('retry', this.value)}>
              <q-icon type="icon-icon_sqq" />
              排查失败，点击重试
            </Button>
          </div>
        );
      }
      if (this.isFailed) {
        return <span class={styles.failed}>排查失败</span>;
      }
      return (
        <CompanyStatus
          v-show={status}
          style={{ height: '24px', lineHeight: '24px', ...status.style }}
          type={status?.type}
          status={status?.label}
        ></CompanyStatus>
      );
    };

    return (
      <div
        class={{
          [styles.record]: true,
          [styles.disabled]: !this.isSuccess,
        }}
        onClick={() => {
          if (!this.isSuccess) {
            return;
          }
          this.$emit('click', this.value.id);
        }}
      >
        <div class={styles.header}>
          <div class={styles.title}>
            <strong>
              {this.value?.projectName}（{this.value?.projectNo}）
            </strong>
            {renderStatus()}
          </div>
        </div>
        <div>
          <span style={{ display: 'inline-flex', alignItems: 'center', gap: '2px' }}>
            <span>查询</span>
            <span style={{ color: '#F04040' }}> {this.value?.companyList?.length} </span>
            <span>个主体</span>
          </span>
          <span class={styles.itemName}>
            <span class={styles.date}>排查编号: </span>
            {this.value?.[this.idKey]}
          </span>
          <span class={styles.itemName}>
            <span class={styles.date}>操作人: </span>
            {this.value?.editor?.name}
          </span>
          <span class={styles.itemName}>
            <span class={styles.date}>排查时间：</span>
            {/* FIXME: updateDate 无值时会变为当前时间 */}
            <span>{moment(this.value.updateDate).format('YYYY-MM-DD HH:mm:ss')}</span>
          </span>
        </div>
        {this.value?.companyList?.length ? (
          <div class={styles.query}>
            <div
              class={{
                [styles.names]: true,
                [styles.clamp]: this.isClamp,
              }}
            >
              <div class={styles.inner} ref="innerRef">
                {this.value?.companyList?.map((item) => {
                  return <div class={styles.name}>{item.companyName}</div>;
                })}
              </div>
            </div>
            <div v-show={this.isSuccess} class={styles.action}>
              <span>查看</span>
              <QIcon type="icon-a-xianduanyou" />
            </div>
          </div>
        ) : null}
      </div>
    );
  },
});

export default HistoryRecordSingle;
