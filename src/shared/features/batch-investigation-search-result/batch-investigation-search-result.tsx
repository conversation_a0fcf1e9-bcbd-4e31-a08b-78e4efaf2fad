import { defineComponent } from 'vue';
import { Button } from 'ant-design-vue';

import QRichTable from '@/components/global/q-rich-table';
import QRichTableEmpty from '@/components/global/q-rich-table/components/empty';
import { useUserStore } from '@/shared/composables/use-user-store';

import BatchRetryButton from './widgets/batch-retry-button';
import styles from './batch-investigation-search-result.module.less';

enum FileGenerateStatus {
  /** 待生成 */
  Pending = 0,
  /** 生成中 */
  Generating = 1,
  /** 已完成 */
  Success = 2,
  /** 生成失败 */
  Failed = 3,
}

/**
 * 批量查询搜索结果列表
 */
const BatchInvestigationSearchResult = defineComponent({
  name: 'BatchInvestigationSearchResult',
  props: {
    /**
     * 表格唯一键
     */
    rowKey: {
      type: String,
      default: 'key',
    },
    /**
     * 数据源
     */
    dataSource: {
      type: Array,
      required: true,
      default: () => [],
    },
    /**
     * 分页信息
     */
    pagination: {
      type: Object,
      required: true,
    },
    /**
     * 表格列配置
     */
    columns: {
      type: Array,
      required: true,
    },
    /**
     * 表格加载状态
     */
    loading: {
      type: Boolean,
      required: true,
    },
    /**
     * 滚动条设置
     */
    scroll: {
      type: Object,
      required: false,
    },
  },

  setup() {
    const { profile } = useUserStore();
    return {
      profile,
    };
  },

  render() {
    if (!this.dataSource.length && !this.loading) {
      return (
        <QRichTableEmpty size={'100px'} minHeight={'calc(100vh - 572px)'}>
          <span class={styles.empty}>
            <div>暂时没有找到相关数据</div>
          </span>
        </QRichTableEmpty>
      );
    }

    return (
      <QRichTable
        showIndex
        rowKey={this.rowKey}
        loading={this.loading}
        columns={this.columns}
        dataSource={this.dataSource}
        pagination={this.pagination}
        scroll={this.scroll}
        scopedSlots={{
          fileName: (name: string) => <div title={name}>{name}</div>,
          status: (item) => {
            let content = null;

            switch (item.status) {
              case FileGenerateStatus.Pending:
                content = <span class={styles.inWait}>待生成</span>;
                break;
              case FileGenerateStatus.Generating:
                content = [
                  <span class={styles.inProgress}>生成中</span>,
                  <span style={styles.percentage}>
                    {`${(Number((item.statisticsInfo?.successCount ?? 0) / (item.statisticsInfo?.recordCount ?? 1)) * 100 || 0).toFixed(0)}%`}
                  </span>,
                ];
                break;
              case FileGenerateStatus.Success:
                content = [
                  <span class={styles.success}>已完成</span>,
                  item.resultFile ? (
                    <a href={item.resultFile} onClick={() => this.$emit('export')}>
                      导出名单
                    </a>
                  ) : null,
                ];
                break;
              case FileGenerateStatus.Failed:
                content = <span class={styles.failed}>生成失败</span>;
                break;
              default:
                break;
            }

            return (
              <div class={styles.status}>
                {content}
                <BatchRetryButton record={item} profile={this.profile} onUpdate={() => this.$emit('retry')} />
              </div>
            );
          },
          action: (item) => {
            const isPendingOrFailed = item.status !== 2; // 待生成或生成失败
            const isTotalFailure = item.statisticsInfo.recordCount === item.statisticsInfo.errorCount; // 全部失败
            return (
              <Button
                type="link"
                disabled={isPendingOrFailed || isTotalFailure}
                onClick={() => {
                  this.$emit('action', item);
                }}
              >
                排查详情
              </Button>
            );
          },
        }}
      ></QRichTable>
    );
  },
});

export default BatchInvestigationSearchResult;
