import { computed, defineComponent, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';
import { isEmpty, omit } from 'lodash';

import AppSideMenu from '@/components/common/app-side-menu';
import { useMenuStore } from '@/hooks/use-menu-store';
import { IMenuItem } from '@/components/common/app-side-menu/types';
import QIcon from '@/components/global/q-icon';
import { useUserStore } from '@/shared/composables/use-user-store';
import { hasPermission } from '@/shared/composables/use-permission';

import ContactServiceModal from '../contact-service-modal';
import styles from './app-sidebar.module.less';

const AppSidebar = defineComponent({
  name: 'AppSidebar',
  props: {
    menuItems: {
      type: Array,
      required: true,
      default: () => [],
    },
  },

  setup(props) {
    const { currentKey, menus, updateMenus, updateMenuKey } = useMenuStore();
    const route = useRoute();
    const router = useRouter();
    const handleRouter = (key) => {
      if (key === currentKey.value) {
        const query = omit(route.query, ['cache', 'cacheQuery', 'useCacheQuery']);

        const getQueryString = () => {
          if (isEmpty(query)) {
            return '';
          }
          const string = Object.entries(query)
            .map(([_key, value]) => `${_key}=${value}`)
            .join('&');
          return `?${string}`;
        };
        window.location.href = `${window.location.pathname}${getQueryString()}`;
        return;
      }
      router.push(key);
    };

    const mode = ref('normal');
    const contactServiceModalVisible = ref(false);
    const updateContactServiceModalVisible = (value: boolean) => {
      contactServiceModalVisible.value = value;
    };

    // 对菜单进行过滤，如果没有子菜单，则过滤掉父菜单
    const filterMenus = computed(() => {
      return props.menuItems.filter((item: any) => {
        if (item.children) {
          return item.children.some((child) => {
            const { resolved } = router.resolve({ path: child.key });
            const permission = resolved?.meta?.permission;
            if (!permission) return true;
            return hasPermission(permission);
          });
        }
        return true;
      });
    });

    const { isZeiss } = useUserStore();

    return {
      handleRouter,
      currentKey,
      menus,
      mode,
      contactServiceModalVisible,
      updateContactServiceModalVisible,
      isZeiss,
      filterMenus,
      updateMenus,
      updateMenuKey,
    };
  },
  watch: {
    '$route.path': {
      handler(val) {
        this.updateMenuKey(val);
      },
      immediate: true,
    },
  },

  render() {
    if (this.filterMenus) {
      this.updateMenus(this.filterMenus as IMenuItem[]);
    }
    return (
      <div class={styles.container}>
        {/* 侧边栏菜单 */}
        <div class={styles.menu}>
          <AppSideMenu
            mode={(this as any).mode}
            items={(this as any).filterMenus}
            activeKey={this.currentKey}
            onChange={this.handleRouter}
          />
        </div>
        <div class={styles.footer}>
          <div class={styles.trigger} onClick={() => this.updateContactServiceModalVisible(true)}>
            <QIcon type="icon-icon_fankui" />
            <span>联系客户经理</span>
          </div>
        </div>
        <ContactServiceModal v-model={this.contactServiceModalVisible} />
      </div>
    );
  },
});

export default AppSidebar;
