import { blackList, customer } from '@/shared/services';
import { isArray, isNil } from 'lodash';

export function useValidateCount(type: 'customer' | 'blacklist') {
  const apiService = type === 'customer' ? customer : blackList;
  const isInRange = async ({
    validationType,
    groupId,
    departmentNames,
    id,
    addCount,
  }: {
    validationType: 'group' | 'department';
    groupId?: number;
    departmentNames?: string[];
    id?: number | number[];
    addCount?: number;
  }) => {
    try {
      let message: string | undefined;
      const { allowed, departmentDetails } = await apiService.validateCount({
        validationType,
        groupId,
        departmentNames,
        businessIds: isArray(id) ? id : !isNil(id) ? [id] : undefined,
        addCount,
      });
      if (departmentDetails) {
        const departmentName = departmentDetails
          .filter((d) => d.isOverLimit)
          .map((d) => d.name)
          .join(', ');
        message = `${departmentName}下企业数已超限`;
      }
      return { allowed, message };
    } catch (e) {
      return { allowed: true };
    }
  };

  const groupValidator = ({ id, count = 1 }) => {
    return async (rule, value, cb) => {
      if (!value) return cb();
      const { allowed } = await isInRange({
        validationType: 'group',
        groupId: value,
        id,
        addCount: count,
      });
      return allowed ? cb() : cb(new Error('该分组下企业数已超限'));
    };
  };
  const departmentValidator = ({ id, count = 1 }) => {
    return async (rule, value, cb) => {
      if (!value?.length) return cb();
      const { allowed, message } = await isInRange({
        validationType: 'department',
        departmentNames: value,
        id,
        addCount: count,
      });
      return allowed ? cb() : cb(new Error(message ?? '该部门下企业数已超限'));
    };
  };

  return {
    isInRange,
    groupValidator,
    departmentValidator,
  };
}
