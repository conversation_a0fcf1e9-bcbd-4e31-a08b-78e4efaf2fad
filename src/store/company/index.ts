import { ActionTree, GetterTree, MutationTree } from 'vuex';
import { sortBy } from 'lodash';

import { company, diligence } from '@/shared/services';

import { CompanyInfo, ICompanyState, IRootState } from '../interfaces';

export const namespaced = true;

export const mutations: MutationTree<ICompanyState> = {
  // 保存公司工商信息
  SET_COMPANY_INFO(state, companyInfo: CompanyInfo) {
    state.companyInfo = companyInfo;
  },
  SET_RISK_INFO(state, payload) {
    state.riskInfo = payload;
  },
  // 保存资质证书信息
  SET_QUALIFICATIONSDATA(state, payload) {
    const [qualificationsRaw, creditsRaw] = payload;
    state.qualifications = qualificationsRaw;
    state.credits = creditsRaw;
  },
  // 保存详情展开列信息
  SET_EXPAND_COLUMNS(state, payload) {
    state.expandKeys = payload;
  },
  //
  SET_ACTIVE_TAB(state, payload) {
    state.activeTabKey = payload;
  },
};

export const actions: ActionTree<ICompanyState, IRootState> = {
  // 获取公司工商信息
  getCompanyInfo: async ({ commit }, params: { keyNo: string }) => {
    const res = await company.getDetail(params);
    commit('SET_COMPANY_INFO', res);
  },
  // 获取公司资质信息
  getQualificationData: async ({ commit }, keyNo: string) => {
    const [qualificationsRaw, creditsRaw] = await Promise.all([
      company.getCertificationList(keyNo).then((res) => res || {}),
      company.getCreditList(keyNo).then(({ Result }) => Result || {}),
    ]);
    commit('SET_QUALIFICATIONSDATA', [qualificationsRaw, creditsRaw]);
  },
  // 获取准入排查的详情
  getDiligence: async (
    { commit },
    params: {
      keyNo: string;
      diligenceId?: string;
      snapshotId?: string;
      isDynamicDetails?: boolean;
      settingId?: number; // 允许指定设置Id
      ambiguousSettingId?: number; // 刷新快照时需要传入 `orgSettingsId`
    }
  ) => {
    let request: Promise<any>;
    if (params.isDynamicDetails) {
      request = diligence.scanBackgroundRisk(params.diligenceId);
    } else {
      request = diligence.scanRisk({
        companyName: state.companyInfo.Name,
        companyId: params.keyNo,
        diligenceId: params.diligenceId,
        snapshotId: params.snapshotId,
        settingId: params.settingId,
        ambiguousSettingId: params.ambiguousSettingId,
      });
    }
    const res = await request;

    if (res.details.dimensionScoreDetails?.length) {
      res.details.dimensionScoreDetails.forEach((item: any) => {
        item.scoreDetails.forEach((detail: any) => {
          detail.pageInfo = {
            total: 0,
            current: 1,
            pageSize: 5,
          };
          detail.companyId = params.keyNo;
          detail.companyName = state.companyInfo.Name;
          if (detail.subDimension && detail.subDimension.length) {
            detail.subDimension.forEach((v: any) => {
              v.parentDimensionKey = detail.key;
              v.companyId = params.keyNo;
              v.companyName = state.companyInfo.Name;
              v.pageInfo = {
                total: 0,
                current: 1,
                pageSize: 5,
              };
            });
          }
        });
      });
    }
    commit('SET_RISK_INFO', res);
  },
};

export const getters: GetterTree<ICompanyState, IRootState> = {
  riskLevel: (state) => state.riskInfo?.details?.result || 0,
  dimensionDetails: (state) => state.riskInfo?.details?.dimensionScoreDetails || [],
  snapshotId: (state) => state.riskInfo?.snapshotId || '',
  tabs: (state, getters) => {
    return [
      {
        name: '全部',
        groupKey: 'all',
        scoreDetails: getters.dimensionDetails.map((v) => v.scoreDetails).flat(), // totalHits为0的情况下，关键项但未命中的需在排查详情中显示
        totalHits: state.riskInfo.details?.totalHits || 0,
        emptyText: '该企业未检测到风险信息',
      },
      ...sortBy(getters.dimensionDetails, ['sort']),
    ];
  },
  activeTab: (state, getters) => {
    return getters.tabs.find((v) => v.groupKey === state.activeTabKey);
  },
};

export const state: ICompanyState = {
  companyInfo: {},
  riskInfo: {},
  qualifications: {},
  credits: {},
  expandKeys: [],
  activeTabKey: '',
};
