import { GetterTree, MutationTree } from 'vuex';

import { IMenuItem } from '@/components/common/app-side-menu/types';

import { IMenuState, IRootState } from '../interfaces';

export const namespaced = true;

export const mutations: MutationTree<IMenuItem> = {
  SET_COLLAPSE(state, payload) {
    state.isCollapse = payload;
  },
  SET_MENUS(state, payload) {
    state.menus = payload;
  },
  SET_MENUKEY(state, payload) {
    state.currentKey = payload;
  },
};

export const getters: GetterTree<IMenuState, IRootState> = {
  flatMenu: (state) => {
    return state.menus.reduce((pre: IMenuItem[], cur: IMenuItem) => {
      if (cur.children) {
        pre.push(...(cur.children as any));
      } else {
        pre.push(cur);
      }
      return pre;
    }, []);
  },
  currentMenu: (state, getters) => {
    return getters.flatMenu.find((item) => item.key === state.currentKey);
  },
};

export const state: IMenuState = {
  isCollapse: false,
  currentKey: '',
  menus: [],
};
