import Vue from 'vue';
import Vuex from 'vuex';

import * as app from './app';
import * as user from './user';
import * as company from './company';
import * as person from './person';
import * as modalSetting from './modal-setting';
import * as message from './message';
import * as menu from './menu';

Vue.use(Vuex);

const store = new Vuex.Store({
  modules: {
    app,
    user,
    company,
    person,
    modalSetting,
    message,
    menu,
  },
});

export const useStore = () => store;

export default store;
