import { computed } from 'vue';

import { useUserStore } from '@/shared/composables/use-user-store';
import { useI18n } from '@/shared/composables/use-i18n';
import { useStore } from '@/store';
export const useMenuStore = () => {
  const store = useStore();
  const currentMenu = computed(() => {
    return store.getters['menu/currentMenu'];
  });
  const currentKey = computed(() => {
    return store.state.menu.currentKey;
  });

  const isCollapse = computed(() => {
    return store.state.menu.isCollapse;
  });

  const menus = computed(() => {
    return store.state.menu.menus;
  });

  const updateMenus = (payload) => {
    store.commit('menu/SET_MENUS', payload);
  };

  const updateMenuKey = (payload) => {
    store.commit('menu/SET_MENUKEY', payload);
  };

  const currentTitle = computed(() => {
    const { tc } = useI18n();
    const { isZeiss } = useUserStore();
    if (!currentMenu.value) return '';
    return tc(currentMenu.value.label, isZeiss.value + 1);
  });

  return {
    currentMenu,
    currentTitle,
    currentKey,
    isCollapse,
    menus,
    updateMenus,
    updateMenuKey,
  };
};
