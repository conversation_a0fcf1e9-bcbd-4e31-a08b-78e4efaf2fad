import { computed } from 'vue';
import { useStore } from '@/store';

export type CompanyInfo = { KeyNo?: string; Name?: string; [x: string]: any };

export const uesInvestStore = () => {
  const store = useStore();
  const companyInfo = computed(() => store.state.company.companyInfo);
  const riskInfo = computed(() => store.state.company.riskInfo);
  const expandKeys = computed(() => store.state.company.expandKeys);
  const activeTabKey = computed(() => store.state.company.activeTabKey);
  const qualifications = computed(() => store.state.company.qualifications);
  const credits = computed(() => store.state.company.credits);
  const riskLevel = computed(() => store.getters['company/riskLevel']);
  const snapshotId = computed(() => store.getters['company/snapshotId']);
  const dimensionDetails = computed(() => store.getters['company/dimensionDetails']);
  const tabs = computed(() => store.getters['company/tabs']);
  const activeTab = computed(() => store.getters['company/activeTab']);
  // 获取工商信息
  const getCompanyInfo = async (keyNo) => {
    await store.dispatch('company/getCompanyInfo', { keyNo });
  };

  // 获取资质信息
  const getQualificationData = async (keyNo) => {
    await store.dispatch('company/getQualificationData', keyNo);
  };
  const getDiligenceData = async (params) => {
    await store.dispatch('company/getDiligence', params);
  };
  const updateActiveTab = (tab) => {
    store.commit('company/SET_ACTIVE_TAB', tab);
  };

  const updateExpandKeys = (keys) => {
    store.commit('company/SET_EXPAND_COLUMNS', keys);
  };
  const updateBasicData = () => {
    store.commit('company/SET_QUALIFICATIONSDATA', [{}, {}]);
    store.commit('company/SET_RISK_INFO', {});
    store.commit('company/SET_COMPANY_INFO', {});
  };
  return {
    companyInfo,
    riskInfo,
    riskLevel,
    expandKeys,
    snapshotId,
    dimensionDetails,
    tabs,
    activeTabKey,
    activeTab,
    qualifications,
    credits,
    updateActiveTab,
    updateExpandKeys,
    updateBasicData,
    getCompanyInfo,
    getQualificationData,
    getDiligenceData,
  };
};
