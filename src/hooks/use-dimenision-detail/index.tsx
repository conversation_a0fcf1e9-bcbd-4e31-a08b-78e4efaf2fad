import { isFunction } from 'lodash';

import { DETAIL_TYPES_MAP, getDetailByType } from '@/shared/config/risk-detail.config';

const TooltipMap = {
  BillDefaults: () =>
    `<div>
      <p>
        <strong>累计承兑发生额</strong>
        ：指当年1月1日至披露日上一月月末累计承兑的电子汇票总金额。
      </p>
      <p>
        <strong>承兑余额</strong>
        ：指截至披露日上一月月末，承兑人已承兑但未结清的电子汇票总金额。
      </p>
      <p>
        <strong>累计逾期发生额</strong>
        ：指截至披露日上一月月末，近5年内发生过逾期的全部电子汇票总金额。
      </p>
      <p>
        <strong>逾期余额</strong>
        ：指截至披露日上一月月末，承兑人已逾期但未结清的电子汇票总金额。
      </p>
    </div>`,
};
export const useDimensionDetail = (vm, props) => {
  const showDetail = (key: string, item: Record<string, any>) => {
    let ntype: any = '';
    if (item.CaseReasonType === '工业品') {
      ntype = '2';
    } else {
      ntype = '1';
    }
    const params: any = {
      keyNo: props.meta.keyNo,
      id: item.id || item.Id || item.No,
      type: ntype,
      dimensionKey: item.dimensionKey,
      Details: item.Details,
    };
    const extraParams = {};
    switch (key) {
      case 'TaxationOffences':
      case 'FreezeEquity':
        params.id = item.RiskId;
        break;
      case 'EquityPledge':
        params.pledgeId = item.No;
        break;
      case 'CapitalReduction':
        params.id = item.ObjectId;
        params.type = 'deceaseCapiNotice';
        break;
      case 'BillDefaults':
        params.tooltip = isFunction(TooltipMap[key]) ? TooltipMap[key]() : TooltipMap[key];
        break;
      case 'CancellationOfFiling':
        params.KeyNo = props.meta.keyNo;
        break;
      case 'SeparationNotice':
        Object.assign(params, item, { title: `企业${item.NoticeType === 7 ? '分立' : '合并'}公告` });
        break;
      default:
    }
    vm.$modal.showDimension(DETAIL_TYPES_MAP[key], params, extraParams);
  };

  const goDetail = (url: string) => {
    if (!url) {
      return;
    }
    window.open(url);
  };
  const showInfo = (key: string, item: Record<string, any>) => {
    let type = '';
    if (key === 'ProductQualityProblem5' && item.Type === '21') {
      type = 'drc';
    }
    vm.$modal.showDimension(DETAIL_TYPES_MAP[key] || type, item);
  };

  const gotoDetail = (key: string, item: Record<string, any>) => {
    const url = getDetailByType(key, item);
    window.open(url);
  };

  return {
    showDetail,
    goDetail,
    showInfo,
    gotoDetail,
  };
};
