import { computed, defineComponent, PropType } from 'vue';
import { <PERSON><PERSON>, Tooltip } from 'ant-design-vue';

import QRichTableEmpty from '@/components/global/q-rich-table/components/empty';
import QRichTable, { IQRichTablePagination } from '@/components/global/q-rich-table';
import ClampContent from '@/components/clamp-content';
import { createTrackEvent } from '@/config/tracking-events';

import styles from '../upload-confirm.module.less';
import WrongEdit from './wrongEdit';

const BatchConfirmResult = defineComponent({
  name: 'BatchConfirmResult',
  props: {
    dataSource: {
      type: Array,
      required: true,
      default: () => [],
    },
    columns: {
      type: Array,
      required: true,
      default: () => [],
    },
    selectedIds: {
      type: Array,
      required: true,
    },
    pagination: {
      type: [Object, Boolean] as PropType<Partial<IQRichTablePagination> | boolean>,
      required: false,
      default: false,
    },
    loading: {
      type: Boolean,
      default: true,
    },
  },
  setup(props, { emit }) {
    const rowSelection = computed(() => ({
      checkStrictly: false,
      selectedRowKeys: props.selectedIds,
      onChange: (selectedRowKeys, selectedRows) => {
        emit('select', selectedRows);
      },
    }));
    const handleRemove = (ids) => {
      emit('delete', ids);
    };

    return {
      rowSelection,
      handleRemove,
    };
  },
  render() {
    const { dataSource, pagination, loading, rowSelection } = this;
    if (!dataSource.length && !loading) {
      return (
        <QRichTableEmpty size={'100px'} minHeight={'calc(100vh - 315px)'}>
          <span class={styles.empty}>
            <div>暂时没有找到相关数据</div>
          </span>
        </QRichTableEmpty>
      );
    }

    return (
      <QRichTable
        class={styles.tableData}
        rowKey={'id'}
        showIndex={false}
        columns={this.columns}
        loading={loading}
        dataSource={dataSource}
        rowSelection={rowSelection}
        pagination={pagination}
        paddingDistance={110}
        scroll={{ x: this.columns.length > 5 ? '1500px' : undefined, y: 'calc(100vh - 146px - 260px)' }}
        onChange={(...arg) => this.$emit('change', ...arg)}
        scopedSlots={{
          action: (text, record) => {
            return (
              <Button type="link" onClick={() => this.handleRemove([record.id])}>
                移除
              </Button>
            );
          },
          company: (item) => {
            if ([1, 2].includes(item.flag)) {
              return [
                <WrongEdit
                  rowData={item}
                  onSelect={(data) => {
                    this.$track(createTrackEvent(6959, '企业核实', '修改公司'));
                    this.$emit('change', data);
                  }}
                />,
              ];
            }
            return (
              <div>
                <ClampContent clampKey={item.companyId}>
                  <a
                    href={`/embed/companyDetail?keyNo=${item.companyId}&title=${item.name}`}
                    target="_blank"
                    domPropsInnerHTML={item.name}
                    onClick={() => this.$track(createTrackEvent(6959, '企业核实', '查看企业'))}
                  ></a>
                </ClampContent>
                <p class={styles.originalname} v-show={item.matchBy === 3}>
                  曾用名：
                  <Tooltip title={item.originalname?.join(', ')}>{item.originalname?.join(', ')}</Tooltip>
                </p>
              </div>
            );
          },
        }}
      />
    );
  },
});

export default BatchConfirmResult;
