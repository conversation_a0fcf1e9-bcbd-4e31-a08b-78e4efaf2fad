import Vue, { computed, defineComponent, onMounted, PropType, ref, unref } from 'vue';
import { orderBy, reverse } from 'lodash';
import ECharts from 'vue-echarts';

import { MapColor } from '@/config';
import { map as provinceConfig } from '@/shared/constants/area.constant';
import Empty from '@/shared/components/empty';

const getBaseMapOptions = (data: Array<{ name: string; value: string | number }>, barData) => {
  const baseMapOptions = {
    visualMap: {
      type: 'continuous',
      // min: 0,
      // max: 100,
      inRange: {
        color: ['#E2F1FD', '#128BED', '#1D3C8F'],
      },
      itemWidth: 6,
      itemHeight: 67,
      text: ['多', '少'],
      textGap: 3,
      textStyle: {
        color: '#666',
      },
      left: 20,
      bottom: 15,
      seriesIndex: 0,
    },
    tooltip: {
      trigger: 'item',
      triggerOn: 'mousemove',
      formatter: (params) => {
        if (params.value) {
          return `${params.name}<br/>企业数量：${params.value}`;
        }
        return null;
      },
    },
    title: [
      {
        text: 'TOP5地区',
        left: 300,
      },
    ],
    grid: {
      left: 305,
      right: '3%',
      top: 30,
      bottom: 30,
      width: 235,
      containLabel: true,
    },
    xAxis: {
      type: 'value',
      scale: true,
      position: 'top',
      min: 0,
      boundaryGap: false,
      splitLine: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        show: false,
      },
    },
    yAxis: {
      type: 'category',
      nameGap: 16,
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        textStyle: {
          color: '#333333',
          fontSize: 12,
        },
      },
      data: barData.map((item) => item.name),
    },
    series: [
      {
        zlevel: 1,
        data,
        type: 'map',
        map: 'china',
        selectedMode: 'single',
        zoom: 1.5,
        center: [140, 34],
        emphasis: {
          label: {
            color: '#666',
          },
        },
        itemStyle: {
          borderWidth: '0.2',
        },
      },
      {
        zlevel: 2,
        type: 'bar',
        barWidth: 10,
        itemStyle: {
          color: '#5B8FF9',
        },
        label: {
          show: true,
          position: 'right',
          color: '#128BED',
        },
        data: barData,
      },
    ],
  };

  return baseMapOptions;
};

const QMap = defineComponent({
  name: 'QMap',
  props: {
    dataSource: {
      type: Array as PropType<Array<{ fieldValue: string; count: number }>>,
      default: () => [],
    },
  },
  setup(props, { emit }) {
    const loading = ref(true);
    const formatData = computed(() => {
      const data = orderBy(props.dataSource, ['count'], ['desc'])
        .filter((v) => v.count > 0 && v.fieldValue)
        .map((item) => {
          return {
            ...item,
            name: provinceConfig[item.fieldValue]?.label,
            value: item.count,
          };
        });
      return data;
    });
    const mapOptions = computed(() => {
      return {
        ...getBaseMapOptions(formatData.value, reverse(formatData.value.slice(0, 5))),
        inRange: {
          color: unref(MapColor),
        },
      };
    });

    const registerMap = async () => {
      return new Promise((resolve) => {
        import('@/utils/echarts').then(({ default: install }) => {
          install(Vue);
          resolve(true);
          loading.value = false;
        });
      });
    };

    const handleClick = (params) => {
      const fieldValue = params?.data?.fieldValue;
      if (!fieldValue) {
        return;
      }
      emit('action', { value: fieldValue });
    };

    onMounted(async () => {
      await registerMap();
    });

    return {
      formatData,
      loading,
      mapOptions,
      handleClick,
    };
  },
  render() {
    if (this.loading || this.formatData.length === 0) {
      return (
        <div style={{ height: '193px' }}>
          <Empty type="search" description="暂时没有找到符合条件的数据" />
        </div>
      );
    }
    return <ECharts option={this.mapOptions} autoresize style={{ height: '193px', width: '580px' }} onClick={this.handleClick} />;
  },
});

export default QMap;
