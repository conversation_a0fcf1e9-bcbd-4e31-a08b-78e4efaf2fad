import { defineComponent, onMounted, ref, unref } from 'vue';
import { Dropdown, Menu, message } from 'ant-design-vue';
import { useRouter } from 'vue-router/composables';
import { get } from 'lodash';

import { openPartnersModal } from '@/components/modal/supplier/add-partner-modal/hooks';
import { openGroupSelectModal } from '@/components/modal/supplier/group-select-modal';
import { uesInvestStore } from '@/hooks/use-invest-store';
import { customerGroup, customerLabel, riskMonitor } from '@/shared/services';
import RiskAction from '@/shared/components/risk-action';
import { createTrackEvent, useTrack } from '@/config/tracking-events';
import { openBlackListModal } from '@/components/modal/supplier/add-blacklist-modal/hooks';
import { getFilterOptions } from '@/hooks/use-group-label-oprator';
import { hasPermission } from '@/shared/composables/use-permission';
import { useUserStore } from '@/shared/composables/use-user-store';

import styles from './risk-watch.module.less';
import { getOptionsStatus } from '../../hook/use-option-status';

const BTNMAP = {
  viewWatch: {
    label: '合作监控列表',
    router: 'risk-monitor',
    statusKey: 'ongoing',
    addConfig: {
      permissionCode: [2102],
      key: 'addWatch',
    },
    viewConfig: {
      permissionCode: [2101],
      key: 'viewWatch',
    },
  },
  viewThird: {
    label: '第三方列表',
    router: 'supplier-partners',
    getRoute: (isSinoPharm) => {
      return {
        path: isSinoPharm ? '/supplier/third-party-partners' : '/supplier/third-party/partners',
      };
    },
    statusKey: 'partner',
    addConfig: {
      permissionCode: [2032],
      key: 'addThird',
    },
    viewConfig: {
      permissionCode: [2031],
      key: 'viewThird',
    },
  },
  viewBlackList: {
    label: '内部黑名单列表',
    router: 'internal-blacklist',
    statusKey: 'blacklist',
    addConfig: {
      permissionCode: [2042],
      key: 'addBlackList',
    },
    viewConfig: {
      permissionCode: [2041],
      key: 'viewBlackList',
    },
  },
};

const RiskWatch = defineComponent({
  name: 'RiskWatch',
  props: {
    value: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const { isSinoPharm } = useUserStore();
    const router = useRouter();
    const loading = ref(false);
    const visible = ref(false);
    const track = useTrack();

    const { riskInfo, companyInfo } = uesInvestStore();
    const { status, result, detectOperateActionsStatus } = getOptionsStatus();

    const handleAddWatch = async () => {
      try {
        loading.value = true;
        const groupId = await openGroupSelectModal({});
        await riskMonitor.addCompany([
          {
            companyName: unref(riskInfo).name,
            companyId: unref(riskInfo).companyId,
            groupId,
          },
        ]);
        message.success('添加成功');
        status.ongoing = false;
      } finally {
        loading.value = false;
      }
    };

    const handleAddPartners = async () => {
      const groups = await customerGroup.search({});
      const allTags = (await customerLabel.search({}))?.map((tag) => ({
        ...tag,
        value: tag.labelId,
        label: tag.name,
      }));
      try {
        const {
          // --V1--------------
          statuscode,
          // 注册资本
          registcapi,
          registcapiamount,
          // 企业类型
          econkindcode,
          // 区域
          province,
          areacode,
          // 行业
          industry,
          // 其它
          subind,
          // --V2--------------
          IndustryV3 = {},
          KeyNo,
          // 成立时间
          StartDate,
        } = unref(companyInfo);
        openPartnersModal({
          data: {
            groupId: -1,
            name: unref(riskInfo).name,
            id: unref(riskInfo).companyId,
            disable: true,
            // **工商信息**
            // 登记状态
            statusCode: statuscode,
            // 成立时间
            startDateCode: StartDate ? StartDate * 1000 : undefined,
            // 注册资本
            registcapi,
            registcapiamount,
            // 企业类型
            econkind: Array.isArray(econkindcode) ? econkindcode.join(',') : econkindcode,
            // 区域
            province,
            areacode,
            // 行业
            industry,
            industry1: get(IndustryV3, 'IndustryCode'),
            industry2: get(IndustryV3, 'SubIndustryCode'),
            industry3: get(IndustryV3, 'MiddleCategoryCode'),
            industry4: get(IndustryV3, 'SmallCategoryCode'),
            // 其它
            subind,
            companyId: KeyNo,
          },
          tabs: [{ key: 'single', label: '添加到第三方' }],
          groups,
          from: 'riskWatch',
          allTags,
          onUpdated: ({ isOld }) => {
            status.partner = false;
            if (!isOld) {
              message.warn('企业已存在，请勿重新提交');
            } else {
              message.success('添加成功');
            }
          },
        });
      } catch (error) {
        console.error(error);
      }
    };

    const handleAddBlackList = async () => {
      const initOptions = async () => {
        const { allTags, groups, getAllTags, getGroups } = getFilterOptions({
          type: 3,
          table: 'InnerBlacklist',
        });
        if (!unref(groups).length) {
          await getGroups();
        }
        if (!unref(allTags).length) {
          await getAllTags();
        }
      };

      await initOptions();

      const data = { companyId: unref(riskInfo).companyId, companyName: unref(riskInfo).name };
      openBlackListModal({
        data,
        tabs: [{ key: 'single', label: '添加到黑名单' }],
        from: 'riskWatch',
        disabled: true,
        onOk: ({ exist }) => {
          if (exist) {
            message.warn('企业已存在，请勿重新提交');
          } else {
            message.success('添加成功');
          }
          status.blacklist = false;
        },
      });
    };
    const handleView = (key) => {
      const fn = BTNMAP[key]?.getRoute;
      if (fn) {
        const config = fn(isSinoPharm.value);
        router.push(config);
        return;
      }
      const url = BTNMAP[key]?.router;
      if (url) {
        router.push({
          name: url,
        });
        track(createTrackEvent(6204, '准入排查详情页', `${BTNMAP[key]?.label}详情`));
      }
    };

    onMounted(async () => {
      await detectOperateActionsStatus(unref(riskInfo)?.companyId || companyInfo.value.KeyNo);
      emit('init', { partnerInfo: result?.partner });
    });

    return {
      loading,
      status,
      handleAddPartners,
      handleAddWatch,
      handleAddBlackList,
      handleView,
      visible,
    };
  },
  render() {
    // 如果没有第三方，黑名单，风险动态的查看权限，直接不显示
    if (!hasPermission([2031, 2041, 2101])) {
      return null;
    }

    const { ongoing, partner, blacklist } = this.status;
    const renderMenuItem = (itemData) => {
      if (!itemData) {
        return null;
      }
      // 是否已经添加过
      const isAdd = !this.status[itemData.statusKey];
      const config = isAdd ? itemData.viewConfig : itemData.addConfig;
      return (
        <Menu.Item key={config.key} v-permission={config.permissionCode}>
          {isAdd ? (
            <div class="flex-between">
              <span class={styles.greyed}>已添加至{itemData.label} </span>
              <span style="margin-left: 10px">
                详情<q-icon type="icon-wenzilianjiantou"></q-icon>
              </span>
            </div>
          ) : (
            <span>{itemData.label}</span>
          )}
        </Menu.Item>
      );
    };
    const renderMenuContent = () => {
      // 如果都是未添加且有查看权限的时候，才显示无权限
      if (
        !(
          (ongoing && hasPermission([2102])) ||
          (!ongoing && hasPermission([2101])) ||
          (partner && hasPermission([2032])) ||
          (!partner && hasPermission([2031])) ||
          (blacklist && hasPermission([2042])) ||
          (!blacklist && hasPermission([2041]))
        )
      ) {
        return <Menu.Item key="norights">您暂无权限</Menu.Item>;
      }
      return Object.values(BTNMAP).reduce((nodesArr, cur) => {
        nodesArr.push(renderMenuItem(cur));
        return nodesArr;
      }, []);
    };

    return (
      <Dropdown overlayClassName={styles.dropdownBtn} placement={'bottomRight'} v-model={this.visible}>
        <RiskAction loading={this.loading} icon={'icon-xinzengqiye'}>
          添加至
          <q-icon
            type="icon-a-shixinxia1x1"
            class={[
              styles.arrow,
              'arrow',
              {
                [styles.rotate]: this.visible,
              },
            ]}
          ></q-icon>
        </RiskAction>
        <Menu
          slot="overlay"
          onClick={({ key }) => {
            switch (key) {
              case 'viewWatch':
              case 'viewThird':
              case 'viewBlackList':
                this.handleView(key);
                break;
              case 'addWatch':
                this.$track(createTrackEvent(6204, '准入排查详情页', '添加至合作监控'));
                this.handleAddWatch();
                break;
              case 'addThird':
                this.$track(createTrackEvent(6204, '准入排查详情页', '添加至第三方管理'));
                this.handleAddPartners();
                break;
              case 'addBlackList':
                this.$track(createTrackEvent(6204, '准入排查详情页', '添加至内部黑名单'));
                this.handleAddBlackList();
                break;
              default:
                break;
            }
          }}
        >
          {renderMenuContent()}
        </Menu>
      </Dropdown>
    );
  },
});

export default RiskWatch;
