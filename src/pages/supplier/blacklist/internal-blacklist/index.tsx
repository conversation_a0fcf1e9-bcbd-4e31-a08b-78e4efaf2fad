import { defineComponent, ref, onMounted, computed, unref } from 'vue';
import { isEmpty, omit } from 'lodash';
import { Button, message, Checkbox, Space } from 'ant-design-vue';
import moment from 'moment';

import QCard from '@/components/global/q-card';
import { stateToQuery } from '@/utils/search-transform/company/state-to-query';
import { blackList as blackListService } from '@/shared/services';
import CommonSearchFilter from '@/components/common/common-search-filter';
import HeroicLayout from '@/shared/layouts/heroic';
import { useMenuStore } from '@/hooks/use-menu-store';
import { useAbility } from '@/libs/plugins/user-ability';
import UserCountStatistics from '@/components/user-count-statistics';
import { createTrackEvent, useTrack } from '@/config/tracking-events';
import { getFilterOptions } from '@/hooks/use-group-label-oprator';
import { openBlackListModal } from '@/components/modal/supplier/add-blacklist-modal/hooks';
import { useUsageStore } from '@/hooks/use-usage-store';
import { syncUsage } from '@/hooks/use-sync-usage-hook';
import ColumnSort from '@/shared/components/column-sort/column-sort';
import DropdownButtonWapper from '@/components/dropdown-button-wrapper';
import { EXPORTITEMS } from '@/config/record.config';
import { openGroupEditModal, openTagEditModal } from '@/components/modal/supplier/add-partner-modal/hooks';
import { useCommonSettingStore } from '@/hooks/use-common-settings';
import { useUnionFilter } from '@/hooks/use-union-filter';
import CommonResult from '@/shared/components/common-result';
import { useTrackEvent } from '@/hooks/use-track-event';

import { getBlacklistFilters, ORIGINAL_TABLE_COLUMN } from './config';
import styles from './internal-blacklist.page.module.less';
import { useValidateCount } from '@/shared/composables/use-validate-count';

const InternalBlacklistPage = defineComponent({
  name: 'InternalBlacklistPage',
  setup() {
    const resRef = ref();
    const init = ref(true);
    const {
      selectRows,
      dataSource,
      selectedIds,
      selectedItemLabels,
      pagination,
      sortInfo,
      btnDisable,
      columnsOri,
      columns,
      showBatchData,
      showBatchSelectAll,
      generateSelectData,
      isFixed,
      dynamicColumns,
    } = useCommonSettingStore({ idKey: 'id', key: 'internal-blacklist', columnData: ORIGINAL_TABLE_COLUMN });

    const track = useTrack();
    const { handleSearchTrack } = useTrackEvent('内部黑名单');
    const industryMap = ref({});
    const formData = ref({});
    const filters = ref<Record<string, any>>({});
    const selectAll = ref(false);
    const companyNames: any = ref([]);

    const { allTags, getAllTags } = getFilterOptions({
      type: 3,
      table: 'InnerBlacklist',
    });

    const getIndustryMap = async () => {
      try {
        industryMap.value = await blackListService.searchIndustry();
      } catch (error) {
        console.log(error);
      }
    };

    const getParams = computed(() => {
      const payload = filters.value;

      const filter = stateToQuery({
        keyword: payload?.keywords,
        filters: payload?.filters,
      });
      const enterpriseType = payload.filters?.enterpriseType?.length ? payload.filters?.enterpriseType : undefined;
      const econType = filter.filter?.econType?.length ? filter.filter?.econType : undefined;
      const getStartDateCode = () => {
        if (!payload.filters?.startDateCode) {
          return undefined;
        }
        if (!payload.filters?.startDateCode?.currently) {
          return {
            currently: true,
            flag: 5,
            min: payload.filters?.startDateCode?.min ? moment().year(payload.filters?.startDateCode?.min).startOf('year') : undefined,
            max: payload.filters?.startDateCode?.max ? moment().year(payload.filters?.startDateCode?.max).endOf('year') : undefined,
            unit: 'year',
          };
        }
        return payload.filters?.startDateCode;
      };

      const startDateCode = getStartDateCode();
      return {
        searchKey: filter?.searchKey,
        enterpriseType,
        treasuryType: filter.filter?.treasuryType,
        operators: payload.filters?.o,
        region: filter.filter?.r,
        industry: filter.filter?.i,
        joinDate: filter.filter?.sd ? [filter.filter?.sd] : undefined,
        expiredStatus: payload.filters?.j,
        departmentIds: payload.filters?.departmentIds?.length > 0 ? payload.filters?.departmentIds : undefined,
        groupIds: payload.filters?.g || undefined,
        updateDate: payload.filters?.at ? [payload.filters?.at] : undefined,
        expiredDate: payload.filters?.et ? [payload.filters?.et] : undefined,
        labelIds: payload.filters?.t,
        statusCode: payload.filters?.statusCode,
        result: payload.filters?.result,
        registcapiAmount: payload.filters?.registcapiAmount,
        startDateCode: startDateCode ? [startDateCode] : undefined,
        pageIndex: pagination.current,
        pageSize: pagination.pageSize,
        companyNames: companyNames.value.length > 0 ? companyNames.value : undefined,
      };
    });

    const {
      getUnionOptions,
      optionsMap,
      isLoading: filterLoading,
      groups,
      getGroups,
      clearFilterStash,
    } = useUnionFilter((aggsField) => {
      return blackListService.aggsSearch({
        query: aggsField === 'group' ? {} : getParams.value,
        aggsField,
      });
    });

    const filterGroups = computed(() => {
      return getBlacklistFilters({
        registration: optionsMap.value.statusCode,
        riskLevel: optionsMap.value.riskLevel,
        enterpriseType: optionsMap.value.enterpriseType,
        econType: optionsMap.value.treasuryType,
        tags: optionsMap.value.label,
        groups: groups.value,
        departList: optionsMap.value.department,
        operators: optionsMap.value.operator,
      });
    });

    const searchParams = ref<Record<string, any>>({});
    const handleFilterChange = (payload) => {
      filters.value = payload;
      pagination.current = 1;
      clearFilterStash();
    };

    const { isInRange } = useValidateCount('blacklist');

    const updateGroup = async (groupId) => {
      const { allowed } = await isInRange({
        id: selectedIds.value,
        groupId,
        validationType: 'group',
      });
      if (!allowed) {
        throw new Error('该分组下企业数已超限');
      }
      const params = {
        groupId,
        ids: selectedIds.value,
      };
      await blackListService.updateGroup(params);
      message.success('移动分组成功');
      resRef.value?.search();
      getGroups();
    };

    const openGroupModal = () => {
      track(createTrackEvent(6232, '内部黑名单', '移动分组'));
      openGroupEditModal({
        groups: unref(groups).map((el) => ({ groupId: el.value, name: el.label, count: el.count })),
        groupType: 3,
        updateGroup,
      });
    };
    const updateLabels = (currentTag) => {
      const params = {
        labelIds: currentTag.map((v) => v.labelId),
        ids: selectedIds.value,
      };
      blackListService.updateLabels(params).then(() => {
        message.success('修改标签成功！');
        resRef.value?.search();
        clearFilterStash('label');
      });
    };

    const openTagModal = async () => {
      track(createTrackEvent(6232, '内部黑名单', '批量修改标签'));
      if (!allTags.value?.length) {
        await getAllTags(3);
      }
      await openTagEditModal({
        allTags,
        selectedItemLabels: unref(selectedItemLabels),
        labelType: 3,
        ok: updateLabels,
      });
    };

    const getAllIds = async (params?) => {
      const res = await blackListService.search(
        params || {
          selectAll: true,
          companyNames: companyNames.value,
        }
      );
      selectRows.value = (res.blacklistIds || []).map((id) => {
        return {
          id,
        };
      });
      selectedItemLabels.value = res.labels;
    };

    const handleDelete = async (params) => {
      const { ids, key } = params;
      if (!ids?.length && key === 'batch') {
        message.warning('请勾选需要移除的企业');
        return;
      }
      // 获取选中的公司id，如果是全部选中就要去调用接口获得
      await blackListService.delete(key === 'all' ? { ...omit(getParams.value, ['pageSize', 'pageIndex']), selectAll: true } : { ids });
      syncUsage('innerBlacklistQuantity', -ids.length);
      selectRows.value = [];
      // 当批量选中且全部删除后，返回默认列表
      if (showBatchData.value && selectAll.value) {
        showBatchData.value = false;
        companyNames.value = [];
      }
      if (pagination.current === 1) {
        resRef.value?.search();
      } else {
        pagination.current = 1;
      }
      message.success('移除成功');
      selectAll.value = false;
      if (key === 'all') {
        filters.value = {};
      }
      getGroups();
      clearFilterStash();
    };

    const handelOpenBlackListModal = async (data: Record<string, any> = {}, options: Record<string, unknown> = {}) => {
      const { currentTab = 'single' } = options;
      const ability = useAbility();
      if (!(await ability.check('stock', ['InnerBlacklistQuantity']))) return;
      openBlackListModal({
        data,
        currentTab,
        onOk: () => {
          message.success(!data?.id ? '添加成功' : '更新成功');
          resRef.value?.search();
          getGroups();
          clearFilterStash();
          if (!data?.id) {
            syncUsage('innerBlacklistQuantity', 1);
          }
        },
      });
    };

    const handleExport = async () => {
      try {
        await blackListService.export(getParams.value);
        message.success('正在导出，稍后可前往任务列表查看进度');
      } catch (error) {
        console.error(error);
      }
    };

    const handleExportByIds = async () => {
      try {
        if (!selectedIds.value.length) {
          message.warning('请选择需要导出的记录');
          return;
        }
        await blackListService.export({ ids: selectedIds.value });
        message.success('正在导出，稍后可前往任务列表查看进度');
      } catch (error) {
        console.error(error);
      }
    };

    onMounted(async () => {
      init.value = false;
      getGroups();
      getIndustryMap();
    });

    // 是否是初次进入页面
    const isFirstImport = computed(() => {
      return pagination?.current === 1 && isEmpty(filters.value) && pagination.total === 0 && !init.value;
    });

    // 更新cache的数据
    const update = ({ type, value }) => {
      const { total, data, pageIndex, pageSize } = value;
      switch (type) {
        case 'init':
          init.value = false;
          dataSource.value = data;
          pagination.current = pageIndex;
          pagination.total = total;
          pagination.pageSize = pageSize;
          break;
        case 'pagination':
          pagination.current = pageIndex;
          pagination.pageSize = pageSize;
          break;
        default:
          Object.assign(sortInfo, value);
      }
    };

    const { hasUsage } = useUsageStore();
    const { currentTitle } = useMenuStore();

    return {
      init,
      resRef,
      filterGroups,
      dataSource,
      pagination,
      industryMap,
      handleDelete,
      selectedIds,
      formData,
      filters,
      handleFilterChange,
      groups,
      handleExport,
      handleExportByIds,
      isFirstImport,
      openTagModal,
      getGroups,
      updateGroup,
      updateLabels,
      selectAll,
      showBatchData,
      getAllIds,
      companyNames,
      handelOpenBlackListModal,
      columns,
      columnsOri,
      selectRows,
      selectedItemLabels,
      openGroupModal,
      btnDisable,
      generateSelectData,
      showBatchSelectAll,
      filterLoading,
      getUnionOptions,

      hasUsage,
      currentTitle,
      getParams,
      searchParams,
      isFixed,
      dynamicColumns,
      update,
      handleSearchTrack,
    };
  },
  render() {
    return (
      <HeroicLayout loading={this.init}>
        <QCard
          slot="hero"
          title={this.currentTitle}
          bodyStyle={{
            paddingTop: 0,
          }}
        >
          <CommonSearchFilter
            class={styles.searchPart}
            placeholder="请输入企业名称或统一社会信用代码"
            inputWidth={'320px'}
            batchPlaceholder="请输入完整的企业名称，系统会自动为您匹配到相对应企业，不同企业之间请通过换行间隔。\n\n输入样例：\n企查查科技股份有限公司\n小米科技有限责任公司"
            filterConfig={this.filterGroups}
            defaultValue={this.filters}
            multiSearch={true}
            loading={this.filterLoading}
            onUpdateBatchData={async (names: string[]) => {
              this.companyNames = names;
              this.pagination.current = 1;
              this.showBatchData = true;
            }}
            onChange={(filterData, group) => {
              this.selectRows = [];
              this.handleFilterChange(filterData);
              this.handleSearchTrack(6230, { keyword: filterData.keywords, filter: group?.label });
            }}
            onMultiClear={() => {
              this.companyNames = [];
              this.showBatchData = false;
              this.selectAll = false;
              this.selectRows = [];
            }}
            onGetOptions={this.getUnionOptions}
          />
        </QCard>
        <CommonResult
          ref="resRef"
          rowKey={'id'}
          useCache={true}
          needSelect={true}
          showSelectCount={false}
          searchFn={blackListService.search}
          selectedIds={this.selectedIds}
          showIndex={false}
          isFixed={this.isFixed}
          industryMap={this.industryMap}
          filterParams={this.getParams}
          groups={this.groups}
          scroll={{ x: true, y: 'calc(100vh - 146px - 260px)' }}
          columns={this.dynamicColumns}
          onUpdateCache={this.update}
          onSelect={(values) => {
            this.generateSelectData(values);
            this.selectAll = this.showBatchData && this.selectRows.length === this.pagination.total;
          }}
          onDelete={(ids) => {
            this.handleDelete({ ids, key: 'batch' });
          }}
          onEdit={(value) => {
            this.handelOpenBlackListModal(value);
          }}
          onReset={() => this.$track(createTrackEvent(6232, '内部黑名单', '重置筛选'))}
        >
          <div slot="extra">
            <div class={styles.extra}>
              <UserCountStatistics dimension={'innerBlacklistQuantity'} />
              <DropdownButtonWapper
                v-permission={[2043]}
                totalCount={this.pagination.total}
                btnText="移除列表"
                selectIdlength={this.selectedIds.length}
                onConfirm={(key) => {
                  this.handleDelete({ ids: this.selectedIds, key });
                  this.$track(createTrackEvent(6232, '内部黑名单', key === 'all' ? '移除全部' : '移除选中'));
                }}
              />

              <Button
                v-disable-tip={'请先选择要操作的企业'}
                v-permission={[2046]}
                disabled={this.btnDisable}
                v-debounceclick={this.openGroupModal}
              >
                移动分组
              </Button>

              <DropdownButtonWapper
                totalCount={this.pagination.total}
                v-permission={[2044]}
                btnText="导出列表"
                selectIdlength={this.selectedIds.length}
                needPopConfirm={false}
                menuItems={EXPORTITEMS}
                onConfirm={(key) => {
                  if (key === 'export') {
                    this.handleExport();
                    this.$track(createTrackEvent(6232, '内部黑名单', '全部导出'));
                  } else {
                    this.handleExportByIds();
                    this.$track(createTrackEvent(6232, '内部黑名单', '选中导出'));
                  }
                }}
              />

              <Button
                v-disable-tip={'请先选择要操作的企业'}
                v-permission={[2047]}
                disabled={this.btnDisable}
                v-debounceclick={this.openTagModal}
              >
                批量修改标签
              </Button>

              <Button
                v-permission={[2042]}
                type={'primary'}
                onClick={() => {
                  this.handelOpenBlackListModal({
                    groupId: this.filters?.filters?.g?.length === 1 ? this.filters?.filters?.g[0] : -1,
                  });
                  this.$track(createTrackEvent(6232, '内部黑名单', '新增黑名单'));
                }}
                icon="plus-circle"
              >
                新增黑名单
              </Button>
            </div>
          </div>
          {this.showBatchSelectAll && (
            <div style="margin-bottom: 15px;" slot="batchSelect">
              <Checkbox
                style={{ height: '22px' }}
                defaultChecked={this.selectAll}
                indeterminate={this.selectedIds.length > 0 && this.selectedIds.length < this.pagination.total}
                checked={this.selectAll}
                onChange={(e) => {
                  if (e.target.checked) {
                    this.getAllIds();
                  } else {
                    this.selectRows = [];
                  }
                  this.selectAll = e.target.checked;
                }}
              >
                全部选中
              </Checkbox>
            </div>
          )}
          <ColumnSort
            slot="columnSort"
            class={['sort-icon', this.showBatchSelectAll ? styles.extraPosition : '']}
            colums={this.columnsOri}
            onChange={(data) => {
              this.columnsOri = data;
            }}
            nativeOnClick={() => this.$track(createTrackEvent(6232, '内部黑名单', '设置icon'))}
          />

          <div slot="emptyExtra">
            <Space v-permission={[2042]} v-show={this.isFirstImport} style="margin-top: 15px;">
              <Button
                data-testid="btn-import-single"
                onClick={() => {
                  this.$track(createTrackEvent(6232, '内部黑名单', '单个添加'));
                  this.handelOpenBlackListModal({});
                }}
              >
                单个添加
              </Button>
              <Button
                data-testid="btn-import-all"
                onClick={() => {
                  this.$track(createTrackEvent(6232, '内部黑名单', '批量导入'));
                  this.handelOpenBlackListModal({}, { currentTab: 'bulk' });
                }}
                type="primary"
              >
                批量导入
              </Button>
            </Space>
          </div>
        </CommonResult>
      </HeroicLayout>
    );
  },
});

export default InternalBlacklistPage;
