import { computed, defineComponent, nextTick, provide, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';
import qs from 'querystring';
import { message, Spin } from 'ant-design-vue';
import { cloneDeep } from 'lodash';

import HeroicLayout from '@/shared/layouts/heroic';
import RiskTrendsTab from '@/components/tabs';
import { TABS } from '@/pages/supplier/potential-investigation/config';
import QCard from '@/components/global/q-card';
import GuideWidget from '@/pages/supplier/risk-assessment-batch/widgets/guide';
import CommonResult from '@/shared/components/common-result';
import { batchImport, potential as potentialService } from '@/shared/services';
import { useI18n } from '@/shared/composables/use-i18n';
import { useRoomSocket } from '@/hooks/use-room-socket/use-room-socket';
import { BatchBusinessTypeEnum } from '@/shared/constants/batch-business-type.constant';
import { objectValuesToNumeric } from '@/utils/transform/object/object-values-to-numeric';
import { useDataHook } from '@/shared/components/common-result/hooks/use-data-hook';
import { useNavTab } from '@/pages/supplier/potential-investigation/hooks/use-nav-tab';

import BatchUpload from './widgets/batch-upload';
import styles from './potential-investigation-batch.module.less';

const PotentialInvestigationBatch = defineComponent({
  name: 'PotentialInvestigationBatch',
  setup() {
    const route = useRoute();
    const router = useRouter();
    const { tc } = useI18n();
    const { tabs, handleTabChange } = useNavTab(TABS);

    const resRef = ref();
    const init = ref(true);
    const pageKey = route?.name || route?.params?.pageType;
    const { dataSource } = useDataHook(pageKey);

    const hasData = computed(() => {
      return dataSource.value.length > 0;
    });

    const getParams = computed(() => {
      return {
        batchType: 0,
        businessType: [
          BatchBusinessTypeEnum.Potential_Batch_Data,
          BatchBusinessTypeEnum.Potential_Batch_Excel,
          BatchBusinessTypeEnum.Potential_Batch_Customer,
        ],
        pageSize: 10,
        pageIndex: 1,
      };
    });

    // TODO 暂无用量
    const checkUsage = async () => true;
    provide('abilityCheck', checkUsage);

    const fetchData = async () => {
      await nextTick();
      resRef.value?.search?.();
    };

    const updateData = async (data) => {
      if (data?.toDetail && data?.batchId) {
        return router.push({
          path: '/supplier/potential-investigation/batch/upload-confirm',
          query: {
            batchId: data.batchId,
          },
        });
      }
      // 选择上传后
      message.success('批量排查任务正在进行中！请稍后在批量排查任务中查看结果');
      return fetchData();
    };

    // 更新单个任务状态
    const updateBatchItem = (incomingItem: Record<string, any>) => {
      const oriData = cloneDeep(dataSource.value);
      const changeIndex = oriData.findIndex((item) => +incomingItem.batchId === item.batchId);
      if (changeIndex > -1) {
        oriData[changeIndex] = {
          ...oriData[changeIndex],
          status: +incomingItem.status,
          canRetry: +incomingItem.canRetry,
          statisticsInfo: {
            ...oriData[changeIndex].statisticsInfo,
            ...objectValuesToNumeric(incomingItem.statisticsInfo),
          },
        };
        dataSource.value = oriData;
      }
    };

    useRoomSocket('/rover/socket', {
      filter: (messageData) =>
        messageData.roomType === 'BatchProcessMonitor' &&
        [
          BatchBusinessTypeEnum.Potential_Batch_Excel,
          BatchBusinessTypeEnum.Potential_Batch_Customer,
          BatchBusinessTypeEnum.Potential_Batch_Data,
        ].includes(+messageData.data.businessType),
      update: updateBatchItem,
      refresh: fetchData,
    });

    const gotoDetail = (record) => {
      router.push({
        name: 'potential-batch-detail',
        query: {
          batchId: record.batchId,
        },
      });
    };

    return {
      getParams,
      init,
      resRef,
      tc,
      hasData,
      tabs,
      handleTabChange,
      updateData,
      gotoDetail,
      fetchData,
    };
  },
  render() {
    return (
      <HeroicLayout align={this.hasData ? undefined : 'center'}>
        <QCard slot="hero">
          <RiskTrendsTab slot="title" tabs={this.tabs} value={this.tabs[0].value} onChange={this.handleTabChange} />
          <header class={styles.hero}>
            <div class={styles.title}>{this.tc('Potential Relation Screening Batch')}</div>
            <div class={styles.batch}>
              <BatchUpload
                action={(file) =>
                  `/rover/batch/import/potential/excel?${qs.stringify({
                    fileName: file.name,
                  })}`
                }
                customerAction={potentialService.customerBatchImport}
                textAction={async (data) => {
                  const res = await potentialService.textBatchImport(data);
                  await this.updateData({});
                  return res;
                }}
                // beforeFileUpload={() => this.checkUsage()}
                onUpdateData={this.updateData}
              />
            </div>
          </header>
        </QCard>
        <Spin spinning={this.init}>
          <GuideWidget v-show={!this.hasData} />

          {/* 因为需要调用search，用v-show */}
          <CommonResult
            ref="resRef"
            v-show={this.hasData}
            title={`${this.tc('Potential Relation Screening Batch')}任务`}
            rowKey={'batchId'}
            searchFn={batchImport.search}
            scroll={{ x: true }}
            filterParams={this.getParams}
            onUpdateCache={() => {
              this.init = false;
            }}
            onAction={this.gotoDetail}
            // onExport={() => this.$track(createTrackEvent(6208, '批量排查', '导出名单'))}
            onRetry={this.fetchData}
          ></CommonResult>
        </Spin>
      </HeroicLayout>
    );
  },
});

export default PotentialInvestigationBatch;
