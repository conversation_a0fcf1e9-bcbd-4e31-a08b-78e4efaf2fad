import { PotentialPermissionCode } from '@/shared/constants/diligence.constant';
import { Group } from '@/components/global/q-filter/interface';
import { DEFAULT_DATE_RANGE } from '@/config/tender.config';
import { IQRichTableColumn } from '@/components/global/q-rich-table';
import { potentialResult } from '@/shared/config/potential-investigation.config';

export const TABS = [
  {
    label: 'Potential Relation Screening Batch',
    value: 'potential-batch',
    permission: [PotentialPermissionCode.batch],
  },
  {
    label: 'Potential Relation Screening Records',
    value: 'potential-investigation-history',
    permission: [PotentialPermissionCode.history],
  },
];

export const BATCH_DETAIL_FILTER_GROUPS = [
  {
    field: 'filters',
    label: '',
    type: 'groups',
    children: [
      {
        field: 'result',
        label: '排查结果',
        type: 'multiple',
        options: [...potentialResult],
      },
    ],
  },
];

export const FILTER_GROUPS: Group[] = [
  {
    field: 'filters',
    label: '筛选条件',
    type: 'groups',
    children: [
      {
        field: 'result',
        type: 'multiple',
        label: '排查结果',
        options: potentialResult,
        layout: 'inline',
      },
      {
        field: 'operators',
        type: 'multiple',
        label: '操作人',
        options: [],
        layout: 'inline',
        meta: {
          showFilter: true,
        },
      },
      {
        field: 'sd',
        type: 'single',
        label: '排查时间',
        options: [{ value: undefined, label: '不限' }, ...DEFAULT_DATE_RANGE],
        custom: {
          type: 'date-range',
        },
      },
    ],
  },
];

export const TABLE_COLUMNS: IQRichTableColumn[] = [
  {
    title: '企业名称',
    fixed: 'left',
    width: 300,
    scopedSlots: {
      customRender: 'companyWithoutLogo',
    },
  },
  {
    title: '统一社会信用代码',
    dataIndex: 'creditcode',
    width: 180,
  },
  {
    title: '排查结果',
    width: 160,
    sorter: true,
    key: 'result',
    dataIndex: 'result',
    scopedSlots: {
      customRender: 'potentialResult',
    },
  },
  {
    title: '命中维度',
    scopedSlots: {
      customRender: 'potentialHitDimensions',
    },
  },
  {
    title: '操作人',
    width: 150,
    dataIndex: 'editor.name',
  },
  {
    title: '排查时间',
    width: 180,
    key: 'createDate',
    dataIndex: 'createDate',
    sorter: true,
    scopedSlots: {
      customRender: 'date',
    },
    dateProps: {
      pattern: 'YYYY-MM-DD HH:mm:ss',
    },
  },
  {
    title: '操作',
    fixed: 'right',
    width: 80,
    scopedSlots: {
      customRender: 'diligencehisAction',
    },
  },
];

export const getBatchDetailFilterGroups = ({ dimensions = [] as any[], subDimensions = [] as any[], isSub = undefined } = {}) => {
  const data = [
    {
      field: 'groupKey',
      type: 'button',
      label: '风险维度',
      options: dimensions,
      meta: {
        maxLength: 15,
        defaultButton: false,
      },
    },
    {
      field: 'dimensionKeys',
      type: 'button',
      label: '风险指标',
      options: subDimensions,
      meta: {
        defaultButton: false,
      },
    },
  ];

  return isSub ? data : data.slice(0, -1);
};

export const batchDetailTableColumns = [
  {
    title: '企业名称',
    scopedSlots: { customRender: 'companyWithoutLogo' },
  },
  {
    title: '排查结果',
    dataIndex: 'result',
    scopedSlots: {
      customRender: 'potentialResult',
    },
  },
  {
    title: '潜在利益冲突',
    scopedSlots: {
      customRender: 'StaffWorkingOutsideForeignInvestment',
    },
  },
  {
    title: '疑似潜在利益冲突',
    scopedSlots: {
      customRender: 'SuspectedInterestConflict',
    },
  },
  {
    title: '内容',
    scopedSlots: {
      customRender: 'diligencehisAction',
    },
  },
];
