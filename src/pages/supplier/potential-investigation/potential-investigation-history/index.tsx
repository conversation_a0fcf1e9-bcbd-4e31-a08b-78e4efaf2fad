import { computed, defineComponent, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router/composables';

import HeroicLayout from '@/shared/layouts/heroic';
import { useCommonSettingStore } from '@/hooks/use-common-settings';
import QCard from '@/components/global/q-card';
import CommonSearchFilter from '@/components/common/common-search-filter';
import { getTranslateZeissFilterGroups } from '@/shared/composables/use-translate';
import { useCacheQuery } from '@/hooks/use-cache-query';
import { isJSONString } from '@/utils/data-type/is-json-string';
import CommonResult from '@/shared/components/common-result';
import { potential as potentialService } from '@/shared/services';
import { useTrackEvent } from '@/hooks/use-track-event';
import RiskTrendsTab from '@/components/tabs';
import { useNavTab } from '@/pages/supplier/potential-investigation/hooks/use-nav-tab';

import { FILTER_GROUPS, TABLE_COLUMNS, TABS } from '../config';

const PotentialInvestigationHistoryPage = defineComponent({
  name: 'PotentialInvestigationHistoryPage',
  props: {},
  setup() {
    const init = ref(true);
    const route = useRoute();
    const router = useRouter();
    const { tabs, handleTabChange } = useNavTab(TABS);

    const { dataSource, selectedIds, sortInfo, columnsOri, generateSelectData, dynamicColumns, columns } = useCommonSettingStore({
      idKey: 'id',
      key: 'potential-investigation-history',
      columnData: TABLE_COLUMNS,
    });

    const filterGroups = ref(getTranslateZeissFilterGroups(FILTER_GROUPS));

    const dateRangeFromUrlQuery = isJSONString(route.query?.dateRange as string)
      ? JSON.parse(route.query?.dateRange as string)
      : { currently: true, flag: 1, number: 30, unit: 'day' };
    const recordNoFromUrlQuery = route.query?.recordNo || undefined;
    const DEFAULT_SEARCH_CONDITIONS = Object.freeze({
      query: {
        keywords: recordNoFromUrlQuery,
        filters: { sd: recordNoFromUrlQuery ? undefined : dateRangeFromUrlQuery },
      },
      pagination: { pageSize: 10, pageIndex: 1 },
      sort: {},
    });
    const { cached } = useCacheQuery('potential-investigation-history', DEFAULT_SEARCH_CONDITIONS, { route, router });

    const getParams = computed(() => {
      const { keywords, filters = {} } = cached.query.value;
      return {
        searchKey: keywords,
        createDate: filters?.sd ? [filters?.sd] : undefined,
        result: filters?.result,
        operators: filters?.operators,
        ...cached.sort.value,
        ...cached.pagination.value,
      };
    });

    const { handleSearchTrack } = useTrackEvent('特定利益关系排查历史记录');
    const handleFilterChange = (payload, group) => {
      cached.query.value = payload;
      cached.pagination.value.pageIndex = 1;
      handleSearchTrack(6941, { keyword: cached.query.value.keywords, filter: group.label });
    };

    const getUserList = async () => {
      const res = await potentialService.operators();
      filterGroups.value = filterGroups.value.map((item: any) => {
        const children = item.children.map((child: any) => {
          if (child.field === 'operators') {
            return {
              ...child,
              options: (res || []).map((user: any) => {
                return {
                  value: user.operator,
                  label: user.name,
                };
              }),
            };
          }
          return child;
        });
        return {
          ...item,
          children,
        };
      });
    };

    const goDetail = ({ id, companyId }) => {
      router.push({
        name: 'potential-investigation-detail',
        params: {
          type: 'history',
          id: companyId,
        },
        query: {
          id,
        },
      });
    };
    const totalCount = ref(0);
    // 更新cache的数据
    const update = ({ type, value }) => {
      const { total, data, pageIndex, pageSize } = value;
      switch (type) {
        case 'init':
          init.value = false;
          dataSource.value = data;
          cached.pagination.value.pageIndex = pageIndex;
          cached.pagination.value.total = total;
          cached.pagination.value.pageSize = pageSize;
          totalCount.value = total;
          break;
        case 'pagination':
          cached.pagination.value.pageIndex = pageIndex;
          cached.pagination.value.pageSize = pageSize;
          break;
        default:
          Object.assign(sortInfo, value);
      }
    };
    /* const handleExportAll = async () => {
      try {
        await specific.export(getParams.value);
        message.success('正在导出，稍后可前往任务列表查看进度');
      } catch (error) {
        console.error(error);
      }
    };

    const handleExportByIds = async () => {
      try {
        if (!selectedIds.value.length) {
          message.warning('请选择需要导出的记录');
          return;
        }
        await specific.export({ diligenceIds: selectedIds.value });
        message.success('正在导出，稍后可前往任务列表查看进度');
      } catch (error) {
        console.error(error);
      }
    };

    const handleExport = async (key: string) => {
      if (key === 'export') {
        await handleExportAll();
      } else {
        await handleExportByIds();
      }
    }; */

    onMounted(async () => {
      await getUserList();
    });

    return {
      init,
      selectedIds,
      filterGroups,
      columnsOri,
      dynamicColumns,
      columns,
      cached,
      sorter: cached.sort,
      filters: cached.query,
      totalCount,
      getParams,
      handleFilterChange,
      goDetail,
      generateSelectData,
      update,
      // handleExport,
      tabs,
      handleTabChange,
    };
  },
  render() {
    return (
      <HeroicLayout loading={this.init}>
        <QCard
          slot="hero"
          bodyStyle={{
            paddingTop: 0,
          }}
        >
          <RiskTrendsTab slot="title" tabs={this.tabs} value={this.tabs[1].value} onChange={this.handleTabChange} />
          <CommonSearchFilter
            placeholder="支持按企业名称查询"
            inputWidth={'450px'}
            filterConfig={this.filterGroups}
            onChange={this.handleFilterChange}
            defaultValue={this.filters}
          />
        </QCard>
        <CommonResult
          ref="tableRef"
          rowKey={'id'}
          searchKey={this.filters?.keywords}
          searchFn={potentialService.history}
          sorter={this.sorter}
          columns={this.columns}
          scroll={{
            x: 1240,
            y: 'calc(100vh - 146px - 220px)',
          }}
          showIndex={true}
          isFixed={true}
          selectedIds={this.selectedIds}
          needSelect={false}
          useCache={true}
          filterParams={this.getParams}
          onUpdateCache={this.update}
          onSelect={this.generateSelectData}
          onAction={this.goDetail}
        >
          {/* <ColumnSort
            slot="columnSort"
            class="sort-icon"
            colums={this.columnsOri}
            onChange={(data) => {
              this.columnsOri = data;
            }}
            nativeOnClick={() => {
              this.$track(createTrackEvent(6226, '排查记录', '设置icon'));
            }}
          /> */}
          <div slot="extra">
            {/* <DropdownButtonWrapper
              totalCount={this.totalCount}
              v-permission={[PotentialPermissionCode.export]}
              btnText="导出列表"
              needPopConfirm={false}
              menuItems={EXPORTITEMS}
              selectIdlength={this.selectedIds.length}
              onConfirm={this.handleExport}
            /> */}
          </div>
        </CommonResult>
      </HeroicLayout>
    );
  },
});

export default PotentialInvestigationHistoryPage;
