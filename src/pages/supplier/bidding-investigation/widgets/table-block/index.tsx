import { defineComponent, getCurrentInstance, nextTick, onMounted, ref, unref, watch } from 'vue';
import { cloneDeep, intersectionBy, isArray, isEqual, isString, sortBy } from 'lodash';
import { Button, message } from 'ant-design-vue';
import moment from 'moment';

import QRichTable from '@/components/global/q-rich-table';
import QEntityLink from '@/components/global/q-entity-link';
import { biddings as biddingService } from '@/shared/services';
import { getScopedSlots } from '@/pages/supplier/investigate-detail/widgets/risk-table-next/get-scoped-slots';
import { getDetailByType } from '@/shared/config/risk-detail.config';
import { createPromiseDialog } from '@/components/promise-dialogs';
import StaffCheckModal from '@/components/modal/staff-check';
import { useDimensionDetail } from '@/hooks/use-dimenision-detail';
import { getCertificationStyle } from '@/config/risk.config';
import {
  openAtlasChartDialog,
  openBidCollusiveDialog,
  openDetailModal,
  openGranteeDetailDialog,
} from '@/shared/composables/open-dialog-drawer';

import styles from './styles.module.less';
import { openQualificationListModal } from '../qualification-list-modal';

const TableBlock = defineComponent({
  name: 'TableBlock',
  props: {
    columns: {
      type: Array,
      default: () => [],
    },
    defaultData: {
      type: [Object, Array],
      default: () => ({
        Result: [],
        Paging: {
          PageIndex: 1,
          PageSize: 5,
          TotalRecords: 0,
        },
      }),
    },
    params: {
      type: Object,
      default: () => ({}),
    },
    request: {
      type: Function,
      default: () => {
        //
      },
    },
    title: {
      type: String,
      default: '',
    },
    immediate: {
      type: Boolean,
      default: false,
    },
    rowKey: {
      type: [Function, String],
      default: (row) => row.id ?? row.Id,
    },
    meta: {
      type: Object,
      default: () => ({
        key: 'default',
      }),
    },
  },
  setup(props) {
    const data = ref<Record<string, any>[]>([]);
    const loading = ref(false);
    const vm = getCurrentInstance()?.proxy;
    const { showDetail, goDetail, showInfo, gotoDetail } = useDimensionDetail(vm, props);
    const sort = ref({
      isSortAsc: undefined as any,
      sortField: undefined,
    });
    const pagination = ref({
      current: 1,
      pageSize: 10,
      total: 0,
      pageSizeOptions: ['5', '10'],
      onChange: (current, pageSize) => {
        pagination.value.current = current;
        pagination.value.pageSize = pageSize;
        // eslint-disable-next-line no-use-before-define
        handleFetch();
      },
      onShowSizeChange: (current, pageSize) => {
        pagination.value.current = 1;
        pagination.value.pageSize = pageSize;
        // eslint-disable-next-line no-use-before-define
        handleFetch();
      },
    });
    const handleFetch = async () => {
      try {
        loading.value = true;
        const params = {
          ...sort.value,
          ...(props.params as any),
          pageIndex: unref(pagination).current,
          pageSize: unref(pagination).pageSize,
        };

        const res = await (props.request as any)(params);
        const { Result, Paging } = res ?? {};
        data.value = Result;
        pagination.value.total = Paging?.TotalRecords ?? 0;
        pagination.value.current = Paging?.PageIndex ?? 1;
        pagination.value.pageSize = Paging?.PageSize ?? 5;
      } finally {
        loading.value = false;
      }
    };

    const handleOrderChange = ({ sorter }) => {
      const isSortAsc = sorter.order ? sorter.order === 'ascend' : undefined;
      sort.value = {
        isSortAsc,
        sortField: sorter.order ? sorter.columnKey : undefined,
      };

      handleFetch();
    };

    const init = () => {
      const { Paging = { TotalRecords: 0, PageIndex: 1, PageSize: 10 }, Result = [] } = props.defaultData as any;
      data.value = sortBy(Result, 'keyNo');
      pagination.value.total = Paging.TotalRecords;
      pagination.value.current = Paging.PageIndex;
      pagination.value.pageSize = Paging.PageSize;
    };

    // 计算名字的数量
    const companyNameCountMap = ref({});

    watch(data, (val) => {
      companyNameCountMap.value = val?.reduce((acc, cur, index) => {
        if (!acc[cur.keyNo]) {
          // total一共几个同名，cur就是已经几个进行了colspan设置
          acc[cur.keyNo] = {
            total: 1,
            index,
          };
        } else {
          acc[cur.keyNo].total += 1;
        }
        return acc;
      }, {});
    });

    onMounted(async () => {
      await nextTick();
      init();
    });

    watch(
      () => props.params,
      (val, oldVal) => {
        if (isEqual(val, oldVal)) return;
        handleFetch();
      },
      { deep: false, immediate: props.immediate as any }
    );

    return {
      data,
      sort,
      pagination,
      loading,
      companyNameCountMap,
      handleFetch,
      handleOrderChange,
      showDetail,
      goDetail,
      showInfo,
      gotoDetail,
    };
  },
  render() {
    const { columns, meta, params } = this as any;
    const mkey = meta.key;
    const keyNoAndNames = params.keyNoAndNames;

    const getNewColoums = () => {
      if (mkey !== 'BiddingCompanyCertification') {
        return columns;
      }

      const columnsOri = cloneDeep(columns);
      let idx = 0;
      columnsOri[0].customRender = (record, row, index) => {
        const idxn = this.companyNameCountMap[record.keyNo].index === index ? ++idx : 0;
        const rowSpan = this.companyNameCountMap[record.keyNo].index === index ? this.companyNameCountMap[record.keyNo].total : 0;
        return {
          children: idxn,
          attrs: {
            rowSpan,
          },
        };
      };
      columnsOri[1].customRender = (record, row, index) => {
        const rowSpan = this.companyNameCountMap[record.keyNo].index === index ? this.companyNameCountMap[record.keyNo].total : 0;
        const obj = {
          children: (
            <a href={`/embed/companyDetail?keyNo=${record.keyNo}&title=${record.companyName}`} target="_blank">
              {record.companyName}
            </a>
          ),
          attrs: {
            rowSpan,
          },
        };
        return obj;
      };
      return columnsOri;
    };
    return (
      <QRichTable
        class={styles.table}
        rowKey={(this as any).rowKey}
        columns={getNewColoums()}
        showIndex={mkey !== 'BiddingCompanyCertification'}
        dataSource={this.data}
        pagination={this.pagination}
        loading={this.loading}
        onChange={this.handleOrderChange}
        scopedSlots={{
          ...getScopedSlots({ mkey, key: mkey }),
          certificationName: (name, record) => {
            return (
              <div class="flex items-center justify-center">
                {name}
                {record.status === 1 ? (
                  <Button
                    style={{ fontSize: '12px', marginLeft: '5px' }}
                    type="link"
                    onClick={() => {
                      openQualificationListModal({
                        keyNo: record.keyNo,
                        companyName: record.companyName,
                        certificateCode: record.certificationCode,
                        CertificateCodeDesc: record.certificationName,
                      });
                    }}
                  >
                    更多
                  </Button>
                ) : null}
              </div>
            );
          },
          certificationStatus: (status) => {
            const codeMap = {
              0: '缺失',
              1: '有效',
              2: '无效',
              3: '暂停',
              4: '撤销',
              5: '注销',
              6: '过期失效',
            };
            const text = codeMap[status] ?? '-';
            return <span class={getCertificationStyle(text)}>{text}</span>;
          },
          certificateNo: (code, record) => {
            if (!code) {
              return '-';
            }
            return (
              <Button
                type="link"
                onClick={() => {
                  openDetailModal({
                    Id: record.certificationId,
                    CertificateCodeDesc: record.certificationName,
                  });
                }}
              >
                {code}
              </Button>
            );
          },
          atlasAction: (text, record) => {
            return (
              <Button type="link" onClick={() => openAtlasChartDialog(record)}>
                图谱
              </Button>
            );
          },
          urlAction: (text, record) => {
            const { key } = (this as any).meta as any;
            const url = getDetailByType(key, record);
            return (
              <a href={url} target="_blank">
                详情
              </a>
            );
          },
          companyName: (item: any) => {
            return (
              <QEntityLink
                coyObj={{
                  KeyNo: item.companyId ?? item.keyNo,
                  Name: item.companyName,
                }}
              />
            );
          },
          guarantorDetail: (record) => {
            return (
              <Button type="link" onClick={() => openGranteeDetailDialog(record)}>
                详情
              </Button>
            );
          },
          qEntityLink: (data) => {
            if (!data) {
              return '-';
            }
            let coyData;
            if (isString(data)) {
              coyData = JSON.parse(data);
            } else if (isArray(data)) {
              coyData = data;
            }
            if (Array.isArray(coyData)) {
              return <QEntityLink coyArr={coyData}></QEntityLink>;
            }
            return <QEntityLink coyObj={coyData}></QEntityLink>;
          },
          EquityPledgeCom: (record) => {
            const excludeArr = [...(record.pledgee ?? []), ...(record.pledgor ?? [])];
            const arr = JSON.parse(record.nameandkeyno).filter((item) => !excludeArr.includes(item.Name));
            if (arr) {
              return <QEntityLink coyArr={arr} />;
            }
            return '-';
          },
          gqczcontetnt: (record) => {
            return (
              <Button
                type="link"
                onClick={() => {
                  this.$modal.showDimension('pledge', {
                    KeyNo: record.keyno,
                    pledgeId: record.id,
                  });
                }}
              >
                详情
              </Button>
            );
          },
          mentionedCompany: (record) => {
            // 渲染标签的方法
            const renderTagBlock = (data) => {
              if (!data) {
                return '-';
              }
              const renderData = isArray(data) ? data : [data];
              return (
                <div>
                  {renderData.map((item) => {
                    const { Name, KeyNo, Tag } = item;
                    return (
                      <div style={{ maxWidth: '200px', width: '150px', lineHeight: '22px' }}>
                        <a href={`/embed/companyDetail?keyNo=${KeyNo}&title=${Name}`} target="_blank">
                          {Name}
                        </a>
                        {Tag && <span class={styles.extrabtn}>{Tag}</span>}
                      </div>
                    );
                  })}
                </div>
              );
            };
            let renderData;
            // 如果有被提及，就用被提及
            if (record?.involveRole?.length > 0) {
              renderData = record.involveRole;
            } else {
              // 没有被提及，就用caserolegroupbyrolename&&排查公司的交集
              const defendant = record.caserolegroupbyrolename?.reduce((arr, cur) => {
                const roleDetail = cur?.DetailList?.map((detail) => {
                  return {
                    ...detail,
                    Tag: cur.Role,
                  };
                });
                return [...arr, ...roleDetail];
              }, []);
              const defendantinBidding = intersectionBy(defendant, ((this as any).params as any)?.keyNoAndNames ?? [], 'KeyNo');
              renderData = defendantinBidding;
            }
            return renderTagBlock(renderData);
          },
          openBDetail: (record) => {
            return (
              <Button
                type="link"
                onClick={() => {
                  this.$modal.showDimension('govProcurementIllegal', {
                    keyNo: record.KeyNo,
                    id: record.id,
                  });
                }}
              >
                详情
              </Button>
            );
          },
          checkStatus: (record) => {
            return (
              <Button
                type="link"
                onClick={async () => {
                  const openModal = createPromiseDialog(StaffCheckModal);
                  const { value, type } = (await openModal({})) as any;
                  if (type !== 'submit') {
                    return;
                  }
                  const queryParams = {
                    ...value,
                    personId: record.personId,
                    keyNo: record.keyNo,
                    diligenceId: this.$route.query.id,
                    key: 'SuspectedInterestConflict',
                    companyName: record.sourceCompanyName,
                    companyId: record.sourceCompanyId,
                  };
                  try {
                    await biddingService.checkPerson(queryParams);
                    message.success('核实成功');
                    await nextTick();
                    window.location.reload();
                  } catch (error) {
                    message.error('核实失败');
                  }
                }}
              >
                核实
              </Button>
            );
          },
          validDate: (record) => {
            const startDate = record.startDate ? moment(record.startDate).format('YYYY-MM-DD') : '-';
            const endDate = record.endDate ? moment(record.endDate).format('YYYY-MM-DD') : '';
            if (startDate === '-' && !endDate) {
              return '-';
            }
            return `${startDate} - ${endDate}`;
          },
          bidCollusiveListContent: (record) => {
            return (
              <Button
                type="link"
                onClick={() =>
                  openBidCollusiveDialog({
                    ...record,
                    keyNoRelated: keyNoAndNames.map((item) => item.companyId),
                  })
                }
              >
                详情
              </Button>
            );
          },
          bidConclusiveRelatedConpany: (record) => {
            const data = JSON.parse(record.collusivekeynoarray);
            return <QEntityLink coyArr={data}></QEntityLink>;
          },
          bidCollusiveDocs: (record) => {
            const data = JSON.parse(record.collusivekeynoarray).filter((item) => item.DocNo);
            if (!data?.length) {
              return '-';
            }
            return (
              <div style={{ display: 'flex', flexWrap: 'wrap' }}>
                {data.map((item) => {
                  const url = getDetailByType('BidCollusive', {
                    id: item.SourceId,
                    CaseNo: item.DocNo,
                  });
                  return (
                    <a href={url} target="_blank">
                      详情
                      {item.DocNo}
                    </a>
                  );
                })}
              </div>
            );
          },
        }}
      />
    );
  },
});

export default TableBlock;
