import { isEmpty } from 'lodash';
import { defineComponent, PropType, ref, shallowReactive } from 'vue';
import { Button, Dropdown, Icon, Menu, message } from 'ant-design-vue';

import QIcon from '@/components/global/q-icon';
import { customer, customerGroup, customerLabel, riskMonitor } from '@/shared/services';
import { openGroupSelectModal } from '@/components/modal/supplier/group-select-modal';
import { openBlackListModal } from '@/components/modal/supplier/add-blacklist-modal/hooks';
import { getHTMLText } from '@/utils';
import { hasPermission } from '@/shared/composables/use-permission';
import { createTrackEvent, useTrack } from '@/config/tracking-events';

import styles from './operate-actions.module.less';
import { openPartnersModal } from '@/components/modal/supplier/add-partner-modal/hooks.ts';

const OperateActions = defineComponent({
  name: 'OperateActions',
  props: {
    item: {
      type: Object as PropType<Record<string, any>>,
      required: true,
    },
  },
  emits: ['select'],
  setup(props, { emit }) {
    const track = useTrack();
    const isLoading = ref(true);
    const isInit = ref(false);
    const status = shallowReactive({
      partner: false,
      ongoing: false,
      blacklist: false,
    });

    /**
     * 检查操作按钮状态
     */
    const detectOperateActionsStatus = async () => {
      isInit.value = true;

      try {
        const { item } = props;
        isLoading.value = true;

        const [partnerStatus, ongoingStatus, blacklistStatus] = await Promise.all([
          customer.checkInsertThird((item as any).KeyNo),
          riskMonitor.getStatusByCompanyId((item as any).KeyNo),
          riskMonitor.getInnerBlackListStatus((item as any).KeyNo),
        ]);
        status.ongoing = isEmpty(ongoingStatus);
        status.partner = isEmpty(partnerStatus);
        status.blacklist = isEmpty(blacklistStatus);
      } catch (error) {
        console.error(error);
      } finally {
        isLoading.value = false;
      }
    };

    /**
     * 添加到第三方管理
     */

    const handleAddPartner = async (item) => {
      const groups = await customerGroup.search({});
      const allTags = (await customerLabel.search({}))?.map((tag) => ({
        ...tag,
        value: tag.labelId,
        label: tag.name,
      }));
      openPartnersModal({
        data: {
          groupId: -1,
          name: item.Name.replace(/(<([^>]+)>)/gi, ''),
          companyId: item.KeyNo,
        },
        tabs: [{ key: 'single', label: '添加到第三方' }],
        groups,
        allTags,
        onUpdated: () => {
          status.partner = false;
          message.success('添加成功');
        },
      });
    };

    /**
     * 添加到合作监控
     */
    async function handleAddOngoingInvestigation(item) {
      try {
        const groupId = await openGroupSelectModal({});
        await riskMonitor.addCompany([
          {
            companyId: item.KeyNo,
            companyName: item.Name.replace(/(<([^>]+)>)/gi, ''),
            groupId,
          },
        ]);
        message.success('添加成功');
        status.ongoing = false;
      } catch (err) {
        console.error(err);
      }
    }

    const handleAddInnerBlacklist = async (item) => {
      const data = {
        companyId: item.KeyNo,
        companyName: getHTMLText(item.Name),
      };
      await openBlackListModal({
        data,
        disabled: true,
        tabs: [{ key: 'single', label: '添加至内部黑名单' }],
        onOk: () => {
          message.success('添加成功');
          status.blacklist = false;
        },
      });
    };

    const handleOperateActions = (actionType: string, item) => {
      switch (actionType) {
        case 'add-partner': // 添加至第三方管理
          handleAddPartner(item);
          track(createTrackEvent(7717, '搜索结果', '添加至第三方管理'));
          break;
        case 'add-ongoing-investigation': // 添加至合作监控列表
          handleAddOngoingInvestigation(item);
          track(createTrackEvent(7717, '搜索结果', '添加至合作监控列表'));
          break;
        case 'add-inner-blacklist':
          handleAddInnerBlacklist(item);
          track(createTrackEvent(7717, '搜索结果', '添加至内部黑名单'));
          break;
        default:
          emit('select', actionType, item);
          break;
      }
    };

    return {
      isLoading,
      isInit,
      status,
      detectOperateActionsStatus,
      handleOperateActions,
    };
  },
  render() {
    const { item } = this;
    const { status } = this;
    // 准入排查，合作监控，第三方，黑名单都没有查看权限的时候，不展示操作按钮
    if (!hasPermission([2001, 2031, 2041, 2101])) {
      return null;
    }
    const { ongoing, partner, blacklist } = status;
    // 如果都是未添加且有查看权限的时候，才显示无权限
    const renderOps = () => {
      if (
        !(
          (ongoing && hasPermission([2102])) ||
          (!ongoing && hasPermission([2101])) ||
          (partner && hasPermission([2032])) ||
          (!partner && hasPermission([2031])) ||
          (blacklist && hasPermission([2042])) ||
          (!blacklist && hasPermission([2041])) ||
          hasPermission([2001])
        )
      ) {
        return [<Menu.Item key="norights">您暂无权限</Menu.Item>];
      }
      return [
        <Menu.Item key="investigation" v-permission={[2001]}>
          风险排查
        </Menu.Item>,
        <Menu.Item key="add-partner" disabled={!this.status.partner} v-permission={[this.status.partner ? 2032 : 2031]}>
          {this.status.partner ? '' : '已'}
          添加至第三方管理
        </Menu.Item>,
        <Menu.Item key="add-ongoing-investigation" disabled={!this.status.ongoing} v-permission={[this.status.ongoing ? 2102 : 2101]}>
          {this.status.ongoing ? '' : '已'}
          添加至合作监控列表
        </Menu.Item>,
        <Menu.Item key="add-inner-blacklist" disabled={!this.status.blacklist} v-permission={[this.status.blacklist ? 2042 : 2041]}>
          {this.status.blacklist ? '' : '已'}
          添加至内部黑名单
        </Menu.Item>,
      ];
    };
    return (
      <Dropdown
        placement="bottomRight"
        onVisibleChange={(visible: boolean) => {
          if (visible && !this.isInit) {
            this.detectOperateActionsStatus();
          }
        }}
      >
        <Button class={styles.actionBtn}>
          <span>操作</span>
          <QIcon type="icon-a-shixinxia1x1" style={{ marginLeft: '0', fontSize: '16px', color: '#666' }} />
        </Button>
        <Menu
          slot="overlay"
          onClick={(menuItemInfo) => {
            this.handleOperateActions(menuItemInfo.key, item);
          }}
        >
          {this.isLoading ? (
            <Menu.Item disabled class={styles.loading}>
              <Icon type="sync" spin />
            </Menu.Item>
          ) : (
            renderOps()
          )}
        </Menu>
      </Dropdown>
    );
  },
});

export default OperateActions;
